# 讯飞TTS多音色生成工具说明

## 📋 工具概述

我已经为您创建了完整的讯飞TTS多音色生成工具，可以生成1000条音频文件，每500条使用一个音色。

## 📁 创建的文件

### 1. `xunfei_tts_multi_voice_1000.py` - 主要生成工具
- **功能**: 生成1000条音频文件（Sc0010001.wav - Sc0011000.wav）
- **音色配置**: 2种音色，每种生成500条
  - 讯飞小燕 (x4_xiaoyan): 10001-10500
  - 讯飞小露 (x4_yezi): 10501-11000

### 2. `xunfei_tts_test.py` - 多音色测试工具
- **功能**: 测试5种音色的可用性
- **测试音色**: 
  - 讯飞小燕 (x4_xiaoyan)
  - 讯飞小露 (x4_yezi)
  - 讯飞许久 (aisjiuxu)
  - 讯飞小婧 (aisjinger)
  - 讯飞许小宝 (aisbabyxu)

### 3. `xunfei_tts_simple_test.py` - 简单连接测试
- **功能**: 测试基本的讯飞TTS连接
- **用途**: 验证API配置和网络连接

## ⚙️ 配置信息

### API配置
```python
XUNFEI_CONFIG = {
    "APPID": "fee2aa97",
    "APISecret": "YWYzODMwN2ExNjhlMDBmN2ExMzE5MWQz",
    "APIKey": "a4727f9636df58ee310a31d2b857361a",
    
    # 语音参数
    "SPEED": "50",      # 语速 (0-100)
    "VOLUME": "80",     # 音量 (0-100)
    "PITCH": "50",      # 音调 (0-100)
    
    # 文件配置
    "TEXT_FILE": "../shuffled_from_rank_random.txt",
    "PINYIN_FILE": "../shuffled_from_rank_random_pinyin.txt",
    "OUTPUT_DIR": "audio_output",
    "REQUEST_INTERVAL": 0.5,  # 请求间隔(秒)
}
```

### 音色配置
```python
VOICE_CONFIGS = [
    {"voice_type": "x4_xiaoyan", "name": "讯飞小燕", "start": 10001, "end": 10500},
    {"voice_type": "x4_yezi", "name": "讯飞小露", "start": 10501, "end": 11000},
]
```

## 🚀 使用方法

### 步骤1: 测试连接
```bash
python xunfei_tts_simple_test.py
```
验证API配置和网络连接是否正常。

### 步骤2: 测试多音色
```bash
python xunfei_tts_test.py
```
测试5种音色是否都能正常工作。

### 步骤3: 批量生成
```bash
python xunfei_tts_multi_voice_1000.py
```
开始生成1000条音频文件。

## 📊 输出格式

### 音频文件
- **格式**: WAV (16kHz, 16bit, 单声道)
- **命名**: Sc0010001.wav - Sc0011000.wav
- **位置**: audio_output目录

### CSV记录文件
- **文件名**: xunfei_audio_info_1000.csv
- **格式**: 标准CSV格式，UTF-8编码
- **列**: 音频名, 类型, 文本, 注音1, 音色

示例：
```csv
"音频名","类型","文本","注音1","音色"
"Sc0010001","c","被使","bei4 shi3","讯飞小燕"
"Sc0010002","c","起做","qi3 zuo4","讯飞小燕"
```

## 🔧 功能特性

### ✅ 智能断点续传
- 自动检测已存在的音频文件
- 跳过已生成的文件，继续未完成的部分
- 支持中断后重新运行

### ✅ 错误处理和重试
- 每个文件最多重试3次
- 详细的错误日志记录
- 网络异常自动重试

### ✅ 进度监控
- 实时显示生成进度
- 每50个文件显示预计剩余时间
- 分批次处理，便于监控

### ✅ 安全备份
- 自动创建CSV记录文件
- 实时保存处理结果
- 支持中断恢复

## ⚠️ 注意事项

### 网络连接问题
当前测试中遇到了网络连接问题（WinError 10060），可能的原因：
1. **防火墙阻止**: 检查防火墙设置，允许WebSocket连接
2. **网络代理**: 如果使用代理，需要配置WebSocket代理
3. **DNS解析**: 确保能正常解析 ws-api.xfyun.cn
4. **网络稳定性**: 确保网络连接稳定

### 解决方案
1. **检查网络**: 确保能访问讯飞官网
2. **关闭防火墙**: 临时关闭防火墙测试
3. **更换网络**: 尝试使用其他网络环境
4. **联系网管**: 如果是企业网络，联系网络管理员

## 🎯 自定义配置

### 修改音色组合
如果需要使用其他音色，可以修改 `VOICE_CONFIGS`：

```python
VOICE_CONFIGS = [
    {"voice_type": "aisjiuxu", "name": "讯飞许久", "start": 10001, "end": 10500},
    {"voice_type": "aisjinger", "name": "讯飞小婧", "start": 10501, "end": 11000},
]
```

### 修改生成数量
如果需要生成不同数量的文件，修改配置：

```python
XUNFEI_CONFIG = {
    "START_INDEX": 10001,
    "END_INDEX": 11000,  # 修改结束索引
    "TOTAL_COUNT": 1000  # 修改总数
}
```

### 调整语音参数
```python
XUNFEI_CONFIG = {
    "SPEED": "60",    # 提高语速
    "VOLUME": "90",   # 提高音量
    "PITCH": "45",    # 降低音调
}
```

## 📈 性能优化

### 请求间隔
- 当前设置: 0.5秒
- 可以根据网络情况调整
- 过快可能导致限流，过慢影响效率

### 批处理大小
- 当前每500条一个音色
- 可以根据需要调整分批大小
- 建议保持在500-1000条之间

## 🔍 故障排除

### 常见错误
1. **10060 连接超时**: 网络连接问题
2. **11200 授权失败**: API配置错误或权限不足
3. **10163 参数错误**: 文本格式或参数配置问题

### 调试方法
1. 运行简单测试确认基本连接
2. 检查API配置是否正确
3. 确认文本文件格式正确
4. 查看详细错误日志

## 📞 技术支持

如果遇到问题：
1. 首先运行测试工具确认问题
2. 检查网络和防火墙设置
3. 确认API配置正确
4. 查看讯飞开放平台文档
5. 联系讯飞技术支持

## 🎉 总结

讯飞TTS多音色生成工具已经完成开发，具备：
- ✅ 完整的多音色支持
- ✅ 智能断点续传
- ✅ 详细的进度监控
- ✅ 完善的错误处理
- ✅ 标准的CSV输出格式

一旦网络连接问题解决，即可开始批量生成1000条高质量的讯飞语音音频文件！
