"""
阿里云TTS多音色批量生成器
每种音色生成1小时音频（18000-24000字），文本长度从2-30均匀分布
"""

import requests
import json
import time
import csv
import os
import uuid
import hashlib
import hmac
import base64
import glob
import random
from datetime import datetime
from urllib.parse import quote
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# ==================== 阿里云配置 ====================
ALIYUN_CONFIG = {
    "ACCESS_KEY_ID": "LTAI5t7reocF8UzbqkiReQQy",
    "ACCESS_KEY_SECRET": "******************************",
    "APP_KEY": "9LNJJ3RhO4Ys8O35",
    
    # 语音参数配置
    "FORMAT": "wav",
    "SAMPLE_RATE": 16000,
    "VOLUME": 50,
    "SPEECH_RATE": 0,
    "PITCH_RATE": 0,
    
    # 文件配置
    "TEXT_FILE": "shuffled.txt",
    "PINYIN_FILE": "shuffled.txt",
    "OUTPUT_DIR": "audio_output_aliyun_multi",
    "CSV_FILE": "aliyun_multi_voice_log.csv",
    "REQUEST_INTERVAL": 0.5,  # 请求间隔（秒）
    
    # 生成配置
    "MIN_CHARS_PER_VOICE": 18000,  # 每种音色最少字数
    "MAX_CHARS_PER_VOICE": 24000,  # 每种音色最多字数
    "TARGET_CHARS_PER_VOICE": 21000,  # 每种音色目标字数
}

# 阿里云音色配置 - 27种音色
VOICE_CONFIGS = [
    {"voice_code": "A001", "voice_name": "Xiaoyun", "name": "小云"},
    {"voice_code": "A002", "voice_name": "Xiaogang", "name": "小刚"},
    {"voice_code": "A003", "voice_name": "Ruoxi", "name": "若汐"},
    {"voice_code": "A004", "voice_name": "Siqi", "name": "思琪"},
    {"voice_code": "A005", "voice_name": "Sijia", "name": "思佳"},
    {"voice_code": "A006", "voice_name": "Sicheng", "name": "思程"},
    {"voice_code": "A007", "voice_name": "Aiqi", "name": "艾琪"},
    {"voice_code": "A008", "voice_name": "Aijia", "name": "艾佳"},
    {"voice_code": "A009", "voice_name": "Aicheng", "name": "艾程"},
    {"voice_code": "A010", "voice_name": "Aida", "name": "艾达"},
    {"voice_code": "A011", "voice_name": "Ninger", "name": "宁儿"},
    {"voice_code": "A012", "voice_name": "Ruilin", "name": "瑞琳"},
    {"voice_code": "A013", "voice_name": "Siyue", "name": "思悦"},
    {"voice_code": "A014", "voice_name": "Aiya", "name": "艾雅"},
    {"voice_code": "A015", "voice_name": "Aixia", "name": "艾夏"},
    {"voice_code": "A016", "voice_name": "Aimei", "name": "艾美"},
    {"voice_code": "A017", "voice_name": "Aiyu", "name": "艾语"},
    {"voice_code": "A018", "voice_name": "Aiyue", "name": "艾悦"},
    {"voice_code": "A019", "voice_name": "Aijing", "name": "艾静"},
    {"voice_code": "A020", "voice_name": "Xiaomei", "name": "小美"},
    {"voice_code": "A021", "voice_name": "Sijing", "name": "思静"},
    {"voice_code": "A022", "voice_name": "Sitong", "name": "思彤"},
    {"voice_code": "A023", "voice_name": "Xiaobei", "name": "小贝"},
    {"voice_code": "A024", "voice_name": "Aitong", "name": "艾彤"},
    {"voice_code": "A025", "voice_name": "Aiwei", "name": "艾薇"},
    {"voice_code": "A026", "voice_name": "Aibao", "name": "艾宝"},
    {"voice_code": "A027", "voice_name": "Aishuo", "name": "艾硕"},
]

print("✅ 配置加载完成")

def generate_signature(method, uri, params, access_key_secret):
    """生成阿里云API签名"""
    # 排序参数
    sorted_params = sorted(params.items())
    query_string = '&'.join([f"{quote(k, safe='')}={quote(str(v), safe='')}" for k, v in sorted_params])
    
    # 构建待签名字符串
    string_to_sign = f"{method}&{quote(uri, safe='')}&{quote(query_string, safe='')}"
    
    # 计算签名
    signature = base64.b64encode(
        hmac.new(
            (access_key_secret + '&').encode(),
            string_to_sign.encode(),
            hashlib.sha1
        ).digest()
    ).decode()
    
    return signature

class AliyunTTSMultiVoice:
    """阿里云TTS多音色批量生成器"""
    
    def __init__(self, config):
        self.config = config
        self.access_key_id = config["ACCESS_KEY_ID"]
        self.access_key_secret = config["ACCESS_KEY_SECRET"]
        self.app_key = config["APP_KEY"]
        self.access_token = None
        self.token_expire_time = 0
        self.success_count = 0
        self.error_count = 0
    
    def _get_access_token(self):
        """获取阿里云访问令牌"""
        # 检查现有令牌是否还有效（提前5分钟刷新）
        current_time = time.time()
        if self.access_token and self.token_expire_time > current_time + 300:
            return self.access_token
        
        print("正在获取新的访问令牌...")
        
        # 使用正确的API端点
        domain = "nls-meta.cn-shanghai.aliyuncs.com"
        uri = "/"
        url = f"https://{domain}{uri}"
        
        # 构建请求参数
        timestamp = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')
        nonce = str(uuid.uuid4())
        
        params = {
            'AccessKeyId': self.access_key_id,
            'Action': 'CreateToken',
            'Format': 'JSON',
            'RegionId': 'cn-shanghai',
            'SignatureMethod': 'HMAC-SHA1',
            'SignatureNonce': nonce,
            'SignatureVersion': '1.0',
            'Timestamp': timestamp,
            'Version': '2019-02-28'
        }
        
        # 生成签名
        signature = generate_signature('POST', uri, params, self.access_key_secret)
        params['Signature'] = signature
        
        try:
            response = requests.post(url, data=params, timeout=10, verify=False)
            
            if response.status_code == 200:
                result = response.json()
                
                if 'Token' in result and 'Id' in result['Token']:
                    self.access_token = result['Token']['Id']
                    self.token_expire_time = result['Token']['ExpireTime']
                    print(f"✓ 成功获取访问令牌: {self.access_token[:20]}...")
                    return self.access_token
                else:
                    print(f"✗ 令牌响应格式错误: {result}")
                    return None
            else:
                print(f"✗ 获取令牌失败: {response.status_code}")
                print(f"错误内容: {response.text}")
                return None
                
        except Exception as e:
            print(f"✗ 获取令牌异常: {e}")
            return None
    
    def synthesize_text(self, text, voice_name):
        """合成单个文本为语音"""
        # 确保有有效的访问令牌
        if not self._get_access_token():
            return None
        
        url = "https://nls-gateway.cn-shanghai.aliyuncs.com/stream/v1/tts"
        
        headers = {
            'Content-Type': 'application/json',
            'X-NLS-Token': self.access_token
        }
        
        data = {
            'appkey': self.app_key,
            'text': text,
            'voice': voice_name.lower(),  # 阿里云音色名称需要小写
            'format': self.config["FORMAT"],
            'sample_rate': self.config["SAMPLE_RATE"],
            'volume': self.config["VOLUME"],
            'speech_rate': self.config["SPEECH_RATE"],
            'pitch_rate': self.config["PITCH_RATE"]
        }
        
        try:
            response = requests.post(
                url,
                headers=headers,
                data=json.dumps(data),
                timeout=30,
                verify=False
            )
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                if 'audio' in content_type or 'application/octet-stream' in content_type:
                    self.success_count += 1
                    return response.content
                else:
                    # 可能是错误响应
                    try:
                        error_info = response.json()
                        print(f"  ✗ API错误: {error_info}")
                    except:
                        print(f"  ✗ 未知响应格式: {response.text[:200]}")
                    self.error_count += 1
                    return None
            else:
                print(f"  ✗ HTTP错误 {response.status_code}: {response.text}")
                self.error_count += 1
                return None
                
        except requests.exceptions.Timeout:
            print(f"  ✗ 请求超时")
            self.error_count += 1
            return None
        except Exception as e:
            print(f"  ✗ 请求异常: {e}")
            self.error_count += 1
            return None

def load_text_files(config):
    """加载文本和拼音文件"""
    print("=== 加载文本文件 ===")
    
    # 读取文本文件
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    return texts, pinyins

def select_texts_for_target_chars(texts, target_chars):
    """
    根据目标字数选择文本，确保文本长度从2-30字符均匀分布
    
    Args:
        texts: 文本列表
        target_chars: 目标字符数
    
    Returns:
        selected_texts: 选中的文本列表
        selected_indices: 选中文本的原始索引
        current_chars: 实际选中的字符数
    """
    # 按文本长度分组
    length_groups = {}
    for i, text in enumerate(texts):
        text_length = len(text)
        # 只考虑长度在2-30之间的文本
        if 2 <= text_length <= 30:
            if text_length not in length_groups:
                length_groups[text_length] = []
            length_groups[text_length].append((i, text))
    
    print(f"  文本长度分布: {sorted(length_groups.keys())}")
    print(f"  各长度文本数量: {[(length, len(group)) for length, group in sorted(length_groups.items())]}")
    
    # 计算每个长度需要的文本数量（均匀分布）
    available_lengths = sorted(length_groups.keys())
    if not available_lengths:
        print("  ⚠️ 没有找到长度在2-30之间的文本")
        return [], [], 0
    
    # 估算每个长度大约需要多少个文本
    total_length_categories = len(available_lengths)
    avg_chars_per_category = target_chars // total_length_categories
    
    selected_texts = []
    selected_indices = []
    current_chars = 0
    length_stats = {}
    
    # 为每个长度类别选择文本
    for text_length in available_lengths:
        group = length_groups[text_length]
        
        # 计算这个长度需要多少个文本
        target_count_for_length = max(1, avg_chars_per_category // text_length)
        actual_count = min(target_count_for_length, len(group))
        
        # 随机选择这个长度的文本
        selected_from_group = random.sample(group, actual_count)
        
        for idx, text in selected_from_group:
            if current_chars + text_length <= target_chars:
                selected_texts.append(text)
                selected_indices.append(idx)
                current_chars += text_length
                
                # 统计
                if text_length not in length_stats:
                    length_stats[text_length] = 0
                length_stats[text_length] += 1
            else:
                break
        
        # 如果已经达到目标字数，停止
        if current_chars >= target_chars * 0.95:  # 达到95%就停止
            break
    
    # 如果还没达到目标字数，随机补充一些文本
    if current_chars < target_chars * 0.9:
        remaining_chars = target_chars - current_chars
        all_remaining = []
        
        for length, group in length_groups.items():
            for idx, text in group:
                if idx not in selected_indices and len(text) <= remaining_chars:
                    all_remaining.append((idx, text, len(text)))
        
        # 按长度排序，优先选择能填满剩余空间的文本
        all_remaining.sort(key=lambda x: abs(x[2] - remaining_chars))
        
        for idx, text, text_len in all_remaining:
            if current_chars + text_len <= target_chars:
                selected_texts.append(text)
                selected_indices.append(idx)
                current_chars += text_len
                
                if text_len not in length_stats:
                    length_stats[text_len] = 0
                length_stats[text_len] += 1
                
                if current_chars >= target_chars * 0.95:
                    break
    
    # 显示长度分布统计
    print(f"  选中文本长度分布: {dict(sorted(length_stats.items()))}")
    print(f"  长度覆盖范围: {min(length_stats.keys()) if length_stats else 0}-{max(length_stats.keys()) if length_stats else 0}")
    print(f"  总文本数: {len(selected_texts)}, 总字数: {current_chars}")
    
    return selected_texts, selected_indices, current_chars

def get_existing_files(output_dir, voice_code):
    """获取指定音色已存在的文件"""
    pattern = os.path.join(output_dir, f"Sc{voice_code}*.wav")
    audio_files = glob.glob(pattern)
    return len(audio_files)

def generate_voice_batch_aliyun(voice_config, texts, pinyins, config, batch_num):
    """使用阿里云TTS生成单个音色批次的音频"""
    voice_name = voice_config["voice_name"]
    voice_display_name = voice_config["name"]
    voice_code = voice_config["voice_code"]
    target_chars = config["TARGET_CHARS_PER_VOICE"]

    print(f"\n=== 批次 {batch_num}: {voice_display_name} ({voice_code}) ===")
    print(f"音色名称: {voice_name}")
    print(f"目标字数: {target_chars} 字")

    # 检查已存在的文件
    existing_count = get_existing_files(config["OUTPUT_DIR"], voice_code)
    if existing_count > 0:
        print(f"已存在 {existing_count} 个文件，继续生成...")

    # 选择文本达到目标字数
    selected_texts, selected_indices, actual_chars = select_texts_for_target_chars(texts, target_chars)

    print(f"选择文本: {len(selected_texts)} 条")
    print(f"实际字数: {actual_chars} 字")

    if not selected_texts:
        print("✗ 没有可用的文本")
        return False

    # 创建TTS对象
    tts = AliyunTTSMultiVoice(config)

    # 统计信息
    success_count = 0
    error_count = 0
    start_time = time.time()

    # 打开CSV文件进行追加
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    csv_exists = os.path.exists(csv_path)

    with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
        # 使用制表符分隔的CSV写入器
        writer = csv.writer(csvfile, delimiter='\t')

        # 写入标题行（如果文件不存在）
        if not csv_exists:
            writer.writerow(['音频名', '类型', '文本', '注音1'])

        for i, (text_idx, text) in enumerate(zip(selected_indices, selected_texts), 1):
            pinyin = pinyins[text_idx] if text_idx < len(pinyins) else ""

            # 音频文件名格式：Sc + 音色代码 + 序号
            audio_name = f"Sc{voice_code}{i:07d}"
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")

            print(f"[{i}/{len(selected_texts)}] {audio_name}: {text[:30]}{'...' if len(text) > 30 else ''}")

            # 检查文件是否已存在
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                success_count += 1
                continue

            # 生成音频（使用阿里云TTS，带重试）
            audio_data = None
            for retry in range(3):
                audio_data = tts.synthesize_text(text, voice_name)
                if audio_data:
                    break
                if retry < 2:
                    print(f"  重试 {retry + 1}/3...")
                    time.sleep(2)

            if audio_data:
                try:
                    # 保存音频文件
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)

                    # 写入CSV记录 - 使用writer.writerow确保正确的列分隔
                    writer.writerow([audio_name, 'c', text, pinyin])
                    csvfile.flush()

                    print(f"  ✓ 成功 ({len(audio_data)} 字节)")
                    success_count += 1

                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1

            # 请求间隔
            time.sleep(config["REQUEST_INTERVAL"])

            # 每100个显示进度
            if i % 100 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(selected_texts) - i) * avg_time
                success_rate = (tts.success_count / (tts.success_count + tts.error_count) * 100) if (tts.success_count + tts.error_count) > 0 else 0
                print(f"  进度: {i}/{len(selected_texts)}, 预计剩余: {remaining/60:.1f}分钟, 成功率: {success_rate:.1f}%")

    elapsed_time = time.time() - start_time
    print(f"\n批次 {batch_num} 完成:")
    print(f"  耗时: {elapsed_time/60:.1f}分钟")
    print(f"  成功: {success_count}")
    print(f"  失败: {error_count}")
    print(f"  实际生成字数: {actual_chars}")
    print(f"  成功率: {(success_count/(success_count+error_count)*100):.1f}%" if success_count+error_count > 0 else "0%")

    return error_count == 0

def main():
    """主函数 - 阿里云TTS多音色版"""
    print("=" * 60)
    print("阿里云TTS多音色批量生成器")
    print("每种音色生成1小时音频，文本长度2-30均匀分布")
    print("=" * 60)

    config = ALIYUN_CONFIG

    print("配置信息:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  拼音文件: {config['PINYIN_FILE']}")
    print(f"  输出目录: {config['OUTPUT_DIR']}")
    print(f"  目标字数: {config['TARGET_CHARS_PER_VOICE']} 字/音色")
    print(f"  请求间隔: {config['REQUEST_INTERVAL']}秒")

    print(f"\n音色配置 (前10个):")
    for i, voice in enumerate(VOICE_CONFIGS[:10], 1):
        print(f"  {i}. {voice['name']} ({voice['voice_code']}) - {voice['voice_name']}")
    print(f"  ... 总共 {len(VOICE_CONFIGS)} 种音色")

    print("=" * 60)

    # 1. 检查必要文件
    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return

    # 2. 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)

    # 3. 加载文本和拼音文件
    print(f"\n=== 加载文本文件 ===")
    texts, pinyins = load_text_files(config)
    if texts is None:
        return

    # 计算总字数
    total_chars = sum(len(text) for text in texts)
    print(f"文本总字数: {total_chars} 字")

    # 4. 初始化CSV文件
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    if not os.path.exists(csv_path):
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile, delimiter='\t')
            writer.writerow(['音频名', '类型', '文本', '注音1'])
        print(f"✓ 创建CSV文件: {csv_path}")

    # 5. 检查现有文件
    print(f"\n=== 检查现有文件 ===")
    total_existing = 0
    for voice in VOICE_CONFIGS:
        existing = get_existing_files(config["OUTPUT_DIR"], voice["voice_code"])
        total_existing += existing
        if existing > 0:
            print(f"  {voice['name']}: {existing} 个文件")

    if total_existing > 0:
        print(f"总计已存在: {total_existing} 个文件")

    # 6. 开始分批生成
    print(f"\n=== 开始分批生成 ===")

    for i, voice_config in enumerate(VOICE_CONFIGS, 1):
        success = generate_voice_batch_aliyun(voice_config, texts, pinyins, config, i)
        if not success:
            print(f"⚠️ {voice_config['name']}生成失败，可能部分文件未完成")

    # 7. 最终统计
    print(f"\n=== 最终统计 ===")

    total_generated = 0
    for voice in VOICE_CONFIGS:
        existing = get_existing_files(config["OUTPUT_DIR"], voice["voice_code"])
        total_generated += existing
        print(f"{voice['name']}: {existing} 个文件")

    print(f"\n总计生成文件: {total_generated}")
    print(f"输出目录: {config['OUTPUT_DIR']}")
    print(f"CSV记录: {csv_path}")
    print("=" * 60)

print("✅ 阿里云TTS多音色版准备完成")

if __name__ == "__main__":
    main()
