#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
华为云TTS测试版 - 生成第8001开始的少量音频验证程序可运行性
"""

import io
import os
import sys
import json
import pycurl
import requests
import time
import csv
from datetime import datetime

# ==================== 测试配置 ====================
TEST_CONFIG = {
    # 华为云API配置
    "AK": "EC7BD181019626B182FC676E6EBEB83D",
    "SK": "C2C7CD527D0A8D3E77C8C6BD669CFED8",
    "AUTH_URL": "http://10.150.4.10:8080/auth/v3/generateToken",
    "TTS_URL": "http://10.150.4.10:8080/hivoice/v3/tts",
    
    # 音频参数配置
    "PERSON": 0,  # 小艺音色
    "COMPRESS_RATE": 0,  # WAV格式使用0
    "SPEED": 100,
    "VOLUME": 140,
    "PITCH": 100,
    "SAMPLE_RATE": 16000,  # 16000Hz
    
    # 文件配置
    "TEXT_FILE": "shuffled_from_rank_random.txt",
    "PINYIN_FILE": "shuffled_from_rank_random_pinyin.txt",
    "OUTPUT_DIR": "audio_output",
    "CSV_FILE": "test_huawei_8001_audio_info.csv",
    "REQUEST_INTERVAL": 1.0,  # 测试时稍微慢一点
    
    # 测试范围配置 - 只生成5个文件进行测试
    "START_INDEX": 8001,
    "TEST_COUNT": 5  # 只生成5个文件
}

def get_token():
    """获取华为云访问令牌"""
    print("正在获取华为云访问令牌...")
    
    url = TEST_CONFIG["AUTH_URL"]
    
    headers = {
        'Content-Type': "application/json",
        'sender': "APP",
        'receiver': "AS",
        'deviceId': "Testbywanyahua",
        'sessionId': "testsff12345",
        'interactionId': "1",
        'locate': "CN",
        'appVersion': "11-0"
    }
    
    body = {
        'ak': TEST_CONFIG["AK"],
        'sk': TEST_CONFIG["SK"]
    }
    
    payload = json.dumps(body)
    
    try:
        response = requests.post(
            url=url,
            data=payload,
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            print('✓ 成功获取访问令牌')
            data = response.text.split()[-2]
            token = json.loads(data)['accessToken']
            print(f"令牌: {token[:20]}...")
            return token
        else:
            print(f'✗ 获取令牌失败: {response.status_code}')
            print(f'响应内容: {response.text}')
            return None
    except Exception as e:
        print(f'✗ 获取令牌异常: {e}')
        return None

def text_to_speech(token, text, index):
    """将文本转换为语音"""
    url = TEST_CONFIG["TTS_URL"]
    request_id = f"test_request_{index}_{int(time.time())}"
    
    headers = [
        "messageName: text2audio",
        "deviceId: Testbywanyahua",
        "appName: com.huawei.vassistant",
        "appVersion: 11.0.12.20",
        "deviceCategory: phone",
        f"token: Bearer {token}",
        "sender: APP",
        "receiver: TTS",
        "sessionId: testsff12345",
        "interactionId: 1",
        "locate: CN",
        "language: zh",
        f"person: {TEST_CONFIG['PERSON']}"
    ]
    
    body = {
        "session": {
            "appId": "",
            "devF": "GWQ**00307",
            "dialogId": 0,
            "interactionId": 0,
            "isExperiencePlan": False,
            "isFinish": False,
            "messageId": "06cff0ec-2430-4992-a54a-3ec6fe7c984f",
            "sender": "com.huawei.vassistant",
            "sessionId": "0f026d5a-83ab-4f9b-9929-df1c8365b2bf"
        },
        "contexts": [{
            "header": {
                "namespace": "System",
                "name": "Device"
            },
            "payload": {
                "deviceName": "MHA",
                "deviceType": "M300-AL00",
                "osType": "android",
                "osVersion": "1.0.0",
                "sysVersion": "11.0",
                "romVersion": "PE_TL10C00B560",
                "mccmnc": "",
                "appInfo": "tts1.0.0",
                "net": "4G",
                "deltaPlatformVer": "-1",
                "minApiLevel": "0",
                "deviceBrand": "HUAWEI",
                "screenOrientation": "portrait",
                "voiceKitVersion": "10.1.3.200",
                "securityLevel": "unlocked"
            }
        }],
        "events": [{
            "header": {
                "namespace": "TTS",
                "name": "dospeak"
            },
            "payload": {
                "token": "",
                "device_id": "Testbywyh",
                "request_id": request_id,
                "text": text,
                "device_type": 0,
                "compress_rate": TEST_CONFIG["COMPRESS_RATE"],
                "speed": TEST_CONFIG["SPEED"],
                "volume": TEST_CONFIG["VOLUME"],
                "pitch": TEST_CONFIG["PITCH"],
                "person": TEST_CONFIG["PERSON"],
                "style": "",
                "sample_rate": TEST_CONFIG["SAMPLE_RATE"]
            }
        }]
    }
    
    payload = json.dumps(body)
    buf = io.BytesIO()
    
    try:
        curl = pycurl.Curl()
        curl.setopt(pycurl.WRITEFUNCTION, buf.write)
        curl.setopt(pycurl.URL, url)
        curl.setopt(pycurl.HTTPHEADER, headers)
        curl.setopt(pycurl.POSTFIELDS, payload)
        curl.perform()
        
        res_code = curl.getinfo(pycurl.HTTP_CODE)
        res = buf.getvalue()
        
        if res_code == 200:
            return res
        else:
            try:
                error_info = json.loads(res)
                print(f"  ✗ API错误: {error_info.get('message', '未知错误')}")
            except:
                print(f"  ✗ HTTP错误 {res_code}: {res[:200]}")
            return None
    except Exception as e:
        print(f"  ✗ 合成异常: {e}")
        return None
    finally:
        buf.close()
        curl.close()

def load_test_data():
    """加载测试数据"""
    print("=== 加载测试数据 ===")
    
    # 读取文本文件
    try:
        with open(TEST_CONFIG["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(TEST_CONFIG["PINYIN_FILE"]):
        try:
            with open(TEST_CONFIG["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    return texts, pinyins

def run_test_generation():
    """运行测试生成"""
    print("=== 开始华为云TTS测试 (第8001条开始) ===")
    print(f"测试范围: {TEST_CONFIG['START_INDEX']} - {TEST_CONFIG['START_INDEX'] + TEST_CONFIG['TEST_COUNT'] - 1}")
    print(f"测试数量: {TEST_CONFIG['TEST_COUNT']} 个文件")
    print(f"格式: WAV, {TEST_CONFIG['SAMPLE_RATE']}Hz")
    print("=" * 50)
    
    # 1. 加载数据
    texts, pinyins = load_test_data()
    if texts is None:
        return False
    
    # 2. 创建输出目录
    os.makedirs(TEST_CONFIG["OUTPUT_DIR"], exist_ok=True)
    
    # 3. 获取访问令牌
    token = get_token()
    if not token:
        print("✗ 无法获取访问令牌，测试失败")
        return False
    
    # 4. 生成测试文件
    success_count = 0
    error_count = 0
    
    csv_path = os.path.join(TEST_CONFIG["OUTPUT_DIR"], TEST_CONFIG["CSV_FILE"])
    
    print(f"\n开始生成测试音频...")
    start_time = time.time()
    
    # 写入CSV记录
    with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        csvfile.write('音频名\t类型\t文本\t注音1\n')
    
        for i in range(TEST_CONFIG['TEST_COUNT']):
            index = TEST_CONFIG['START_INDEX'] + i
            text_index = index - 1  # 转换为数组索引
            
            if text_index >= len(texts):
                print(f"[{i+1}/{TEST_CONFIG['TEST_COUNT']}] 索引超出范围，跳过")
                continue
            
            text = texts[text_index]
            pinyin = pinyins[text_index] if text_index < len(pinyins) else ""
            audio_name = f"Sc{index:07d}"
            
            print(f"[{i+1}/{TEST_CONFIG['TEST_COUNT']}] {audio_name}: {text[:40]}{'...' if len(text) > 40 else ''}")
            
            # 检查文件是否已存在
            audio_path = os.path.join(TEST_CONFIG["OUTPUT_DIR"], f"{audio_name}.wav")
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                continue
            
            # 生成音频
            audio_data = text_to_speech(token, text, index)
            if audio_data:
                try:
                    # 保存音频文件
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)
                    
                    # 写入CSV记录
                    csvfile.write(f"{audio_name}\tc\t{text}\t{pinyin}\n")
                    csvfile.flush()
                    
                    print(f"  ✓ 成功 ({len(audio_data)} 字节) -> {audio_name}.wav")
                    success_count += 1
                    
                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1
            
            # 请求间隔
            time.sleep(TEST_CONFIG["REQUEST_INTERVAL"])
    
    elapsed_time = time.time() - start_time
    print(f"\n=== 测试完成 ===")
    print(f"耗时: {elapsed_time:.1f}秒")
    print(f"成功: {success_count}")
    print(f"失败: {error_count}")
    print(f"CSV记录: {csv_path}")
    
    return success_count > 0

def main():
    """主函数"""
    print("=" * 60)
    print("华为云TTS程序可运行性测试 (第8001条开始)")
    print("=" * 60)
    
    print("测试配置:")
    print(f"  文本文件: {TEST_CONFIG['TEXT_FILE']}")
    print(f"  拼音文件: {TEST_CONFIG['PINYIN_FILE']}")
    print(f"  输出目录: {TEST_CONFIG['OUTPUT_DIR']}")
    print(f"  测试数量: {TEST_CONFIG['TEST_COUNT']} 个文件")
    print(f"  起始索引: {TEST_CONFIG['START_INDEX']}")
    print(f"  音色: 小艺")
    print(f"  格式: WAV, {TEST_CONFIG['SAMPLE_RATE']}Hz")
    print("=" * 60)
    
    # 检查文件
    if not os.path.exists(TEST_CONFIG['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {TEST_CONFIG['TEXT_FILE']}")
        return
    
    if not os.path.exists(TEST_CONFIG['PINYIN_FILE']):
        print(f"✗ 拼音文件不存在: {TEST_CONFIG['PINYIN_FILE']}")
        return
    
    # 运行测试
    success = run_test_generation()
    
    if success:
        print("\n🎉 华为云TTS程序可运行性测试通过！")
        print("可以运行完整版本进行批量生成第8001-9000条音频")
    else:
        print("\n⚠️ 程序测试失败，请检查配置和网络")

if __name__ == "__main__":
    main()
