#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包TTS多音色生成 - 确认测试版
测试每种音色生成前后的确认功能
"""

import asyncio
import time
import os

# 简化配置
TEST_CONFIG = {
    "TEXT_FILE": "../shuffled_from_rank_random.txt",
    "PINYIN_FILE": "../shuffled_from_rank_random_pinyin.txt",
    "OUTPUT_DIR": "audio_output_test",
    "MIN_CHARS_PER_VOICE": 1000,  # 测试用较小值
    "MAX_CHARS_PER_VOICE": 2000,
}

# 测试音色配置（只用3种音色测试）
TEST_VOICE_CONFIGS = [
    {"voice_code": "D001", "voice_type": "zh_female_wanqudashu_moon_bigtts", "name": "婉曲大叔"},
    {"voice_code": "D002", "voice_type": "zh_female_daimengchuanmei_moon_bigtts", "name": "黛梦传媒"},
    {"voice_code": "D003", "voice_type": "zh_male_guozhoudege_moon_bigtts", "name": "国州德哥"},
]

def load_text_files(config):
    """加载文本和拼音文件"""
    print("=== 加载文本文件 ===")
    
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    return texts, pinyins

def group_texts_by_length(texts, pinyins):
    """按文本长度分组"""
    length_groups = {}
    
    for i, text in enumerate(texts):
        length = len(text)
        if length not in length_groups:
            length_groups[length] = []
        
        pinyin = pinyins[i] if i < len(pinyins) else ""
        length_groups[length].append({
            'text': text,
            'pinyin': pinyin,
            'index': i
        })
    
    return length_groups

def distribute_texts_to_voices(length_groups, target_voices, target_chars_per_voice):
    """将文本分配给各个音色"""
    voice_assignments = [[] for _ in range(target_voices)]
    voice_char_counts = [0] * target_voices
    
    for length in sorted(length_groups.keys()):
        texts_of_length = length_groups[length]
        
        for text_info in texts_of_length:
            # 找到当前字数最少的音色
            voice_idx = voice_char_counts.index(min(voice_char_counts))
            
            # 检查是否会超过目标字数
            if voice_char_counts[voice_idx] + length <= target_chars_per_voice * 1.2:
                voice_assignments[voice_idx].append(text_info)
                voice_char_counts[voice_idx] += length
            else:
                # 找下一个最少的音色
                sorted_indices = sorted(range(target_voices), key=lambda x: voice_char_counts[x])
                assigned = False
                for voice_idx in sorted_indices:
                    if voice_char_counts[voice_idx] + length <= target_chars_per_voice * 1.2:
                        voice_assignments[voice_idx].append(text_info)
                        voice_char_counts[voice_idx] += length
                        assigned = True
                        break
                
                if not assigned:
                    voice_idx = voice_char_counts.index(min(voice_char_counts))
                    voice_assignments[voice_idx].append(text_info)
                    voice_char_counts[voice_idx] += length
    
    return voice_assignments, voice_char_counts

async def simulate_voice_generation(voice_config, text_assignments, batch_num, total_voices):
    """模拟音色生成过程"""
    voice_name = voice_config["name"]
    voice_code = voice_config["voice_code"]
    
    print(f"\n=== 模拟生成批次 {batch_num}/{total_voices}: {voice_name} ({voice_code}) ===")
    
    if not text_assignments:
        print("✗ 没有分配的文本")
        return False
    
    actual_chars = sum(len(item['text']) for item in text_assignments)
    print(f"分配文本: {len(text_assignments)} 条")
    print(f"实际字数: {actual_chars} 字")
    
    # 模拟生成过程
    print(f"开始模拟生成...")
    start_time = time.time()
    
    success_count = 0
    total_files = min(10, len(text_assignments))  # 只模拟前10个文件
    
    for i in range(1, total_files + 1):
        text_info = text_assignments[i-1]
        text = text_info['text']
        
        # 模拟文件名
        audio_name = f"Sc{voice_code}{i:07d}"
        
        print(f"[{i}/{total_files}] {audio_name}: {text[:30]}{'...' if len(text) > 30 else ''}")
        
        # 模拟生成时间
        await asyncio.sleep(0.1)  # 模拟0.1秒生成时间
        
        # 模拟成功
        print(f"  ✓ 模拟成功")
        success_count += 1
        
        # 每5个显示进度
        if i % 5 == 0:
            elapsed = time.time() - start_time
            avg_time = elapsed / i
            remaining = (total_files - i) * avg_time
            print(f"  进度: {i}/{total_files}, 预计剩余: {remaining:.2f}秒")
    
    elapsed_time = time.time() - start_time
    print(f"\n批次 {batch_num} 模拟完成:")
    print(f"  耗时: {elapsed_time:.2f}秒")
    print(f"  成功: {success_count}")
    print(f"  失败: 0")
    print(f"  实际生成字数: {actual_chars}")
    print(f"  成功率: 100.0%")
    print(f"  生成文件: Sc{voice_code}0000001.wav - Sc{voice_code}{success_count:07d}.wav")
    
    return True

async def test_confirm_workflow():
    """测试确认工作流程"""
    print("=" * 60)
    print("豆包TTS多音色生成 - 确认功能测试")
    print("=" * 60)
    
    config = TEST_CONFIG
    
    # 1. 加载文本文件
    texts, pinyins = load_text_files(config)
    if texts is None:
        return
    
    # 2. 按长度分组文本
    print(f"\n=== 分析文本长度分布 ===")
    length_groups = group_texts_by_length(texts, pinyins)
    print(f"文本长度范围: {min(length_groups.keys())}-{max(length_groups.keys())} 字")
    
    # 3. 计算音色分配
    total_chars = sum(len(text) for text in texts)
    target_voices = len(TEST_VOICE_CONFIGS)
    target_chars_per_voice = total_chars // target_voices
    
    print(f"\n=== 计算音色分配 ===")
    print(f"文本总字数: {total_chars}")
    print(f"使用音色: {target_voices} 种")
    print(f"每种音色平均字数: {target_chars_per_voice}")
    
    # 4. 分配文本到各个音色
    voice_assignments, voice_char_counts = distribute_texts_to_voices(
        length_groups, target_voices, target_chars_per_voice
    )
    
    # 显示分配结果
    print(f"\n音色分配结果:")
    for i in range(target_voices):
        voice_config = TEST_VOICE_CONFIGS[i]
        char_count = voice_char_counts[i]
        text_count = len(voice_assignments[i])
        print(f"  {voice_config['voice_code']} {voice_config['name']}: {text_count} 条文本, {char_count} 字")
    
    # 5. 确认开始生成
    try:
        confirm = input(f"\n是否开始测试 {target_voices} 种音色的生成确认流程？(y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("已取消测试")
            return
    except KeyboardInterrupt:
        print("\n测试被中断")
        return
    
    # 6. 开始分批生成（带确认）
    print(f"\n=== 开始分批生成测试 ===")
    
    for i in range(target_voices):
        voice_config = TEST_VOICE_CONFIGS[i]
        text_assignments = voice_assignments[i]
        
        # 生成前确认
        print(f"\n准备生成第 {i + 1}/{target_voices} 种音色:")
        print(f"  音色: {voice_config['voice_code']} - {voice_config['name']}")
        print(f"  文本数量: {len(text_assignments)} 条")
        print(f"  预计字数: {sum(len(item['text']) for item in text_assignments)} 字")
        
        try:
            start_choice = input(f"是否开始生成 {voice_config['name']}？(y/n): ").strip().lower()
            if start_choice not in ['y', 'yes', '是']:
                print(f"跳过 {voice_config['name']}")
                continue
        except KeyboardInterrupt:
            print("\n生成被中断")
            break
        
        # 模拟生成过程
        success = await simulate_voice_generation(
            voice_config, text_assignments, i + 1, target_voices
        )
        
        # 生成完成后的状态报告
        if success:
            print(f"✅ {voice_config['name']} 生成完成")
        else:
            print(f"⚠️ {voice_config['name']} 生成失败")
        
        # 如果不是最后一个音色，询问是否继续
        if i < target_voices - 1:
            try:
                continue_choice = input(f"\n是否继续生成下一个音色？(y/n): ").strip().lower()
                if continue_choice not in ['y', 'yes', '是']:
                    print("生成被中断")
                    break
            except KeyboardInterrupt:
                print("\n生成被中断")
                break
        else:
            print(f"\n🎉 所有 {target_voices} 种音色测试完成！")
    
    print(f"\n=== 测试完成 ===")
    print("确认功能工作正常，可以应用到实际生成程序中")

def main():
    """主函数"""
    asyncio.run(test_confirm_workflow())

if __name__ == "__main__":
    main()
