#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包TTS音色测试工具
测试可用的音色，找出有效的voice_type
"""

import requests
import base64
import json
import uuid
import time

# API配置
DOUYIN_CONFIG = {
    "APPID": "1500263695",
    "ACCESS_TOKEN": "lsZJKq3sy6GngicgAcZN8mWQmqZ1cKGN", 
    "CLUSTER": "volcano_tts",
}

HOST = "openspeech.bytedance.com"
API_URL = f"https://{HOST}/api/v1/tts"

# 测试音色列表 - 从简单到复杂
TEST_VOICES = [
    # {"voice_code": "D001", "voice_type": "zh_female_wanqudashu_moon_bigtts", "name": "婉曲大叔"},
    # {"voice_code": "D002", "voice_type": "zh_female_daimengchuanmei_moon_bigtts", "name": "黛梦传媒"},
    # {"voice_code": "D003", "voice_type": "zh_male_guozhoudege_moon_bigtts", "name": "国州德哥"},
    # {"voice_code": "D004", "voice_type": "zh_male_beijingxiaoye_moon_bigtts", "name": "北京小爷"},
    # {"voice_code": "D005", "voice_type": "zh_male_shaonianzixin_moon_bigtts", "name": "少年子心"},
    # {"voice_code": "D006", "voice_type": "zh_female_meilinvyou_moon_bigtts", "name": "美丽女友"},
    # {"voice_code": "D007", "voice_type": "zh_male_shenyeboke_moon_bigtts", "name": "深夜播客"},
    # {"voice_code": "D008", "voice_type": "zh_female_sajiaonvyou_moon_bigtts", "name": "撒娇女友"},
    # {"voice_code": "D009", "voice_type": "zh_female_yuanqinvyou_moon_bigtts", "name": "远亲女友"},
    # {"voice_code": "D010", "voice_type": "zh_male_haoyuxiaoge_moon_bigtts", "name": "浩宇小哥"},
    # {"voice_code": "D011", "voice_type": "zh_male_guangxiyuanzhou_moon_bigtts", "name": "广西远州"}, #广西
    # {"voice_code": "D012", "voice_type": "zh_female_meituojieer_moon_bigtts", "name": "妹坨洁儿"},
    # {"voice_code": "D013", "voice_type": "zh_male_yuzhouzixuan_moon_bigtts", "name": "豫州子轩"},
    # {"voice_code": "D014", "voice_type": "zh_female_linjianvhai_moon_bigtts", "name": "邻家女孩"},
    # {"voice_code": "D015", "voice_type": "zh_female_gaolengyujie_moon_bigtts", "name": "高冷御姐"},
    # {"voice_code": "D016", "voice_type": "zh_male_yuanboxiaoshu_moon_bigtts", "name": "渊博小叔"},
    # {"voice_code": "D017", "voice_type": "zh_male_yangguangqingnian_moon_bigtts", "name": "阳光青年"},
    # {"voice_code": "D018", "voice_type": "zh_male_aojiaobazong_moon_bigtts", "name": "傲娇霸总"},
    # {"voice_code": "D019", "voice_type": "zh_male_jingqiangkanye_moon_bigtts", "name": "京腔侃爷"},  #京腔
    {"voice_code": "D020", "voice_type": "zh_female_shuangkuaisisi_moon_bigtts", "name": "爽快思思"}, 
    {"voice_code": "D021", "voice_type": "zh_male_wennuanahu_moon_bigtts", "name": "温暖阿虎"},
    {"voice_code": "D022", "voice_type": "zh_female_wanwanxiaohe_moon_bigtts", "name": "婉婉小荷"},
    {"voice_code": "D023", "voice_type": "ICL_zh_female_bingruoshaonv_tob", "name": "冰若少女"},
    {"voice_code": "D024", "voice_type": "ICL_zh_female_huoponvhai_tob", "name": "活泼女孩"},
    {"voice_code": "D025", "voice_type": "ICL_zh_female_heainainai_tob", "name": "和蔼奶奶"},
    {"voice_code": "D026", "voice_type": "ICL_zh_female_linjuayi_tob", "name": "邻居阿姨"},
    {"voice_code": "D027", "voice_type": "zh_female_wenrouxiaoya_moon_bigtts", "name": "温柔小雅"},
    {"voice_code": "D028", "voice_type": "zh_female_tianmeixiaoyuan_moon_bigtts", "name": "甜美小园"},
    {"voice_code": "D029", "voice_type": "zh_female_qingchezizi_moon_bigtts", "name": "清澈紫紫"},
    {"voice_code": "D030", "voice_type": "zh_male_dongfanghaoran_moon_bigtts", "name": "东方浩然"},
    {"voice_code": "D031", "voice_type": "zh_male_jieshuoxiaoming_moon_bigtts", "name": "解说小明"},
    {"voice_code": "D032", "voice_type": "zh_female_kailangjiejie_moon_bigtts", "name": "开朗姐姐"},
    {"voice_code": "D033", "voice_type": "zh_male_linjiananhai_moon_bigtts", "name": "邻家男孩"},
    {"voice_code": "D034", "voice_type": "zh_female_tianmeiyueyue_moon_bigtts", "name": "甜美月月"},
    {"voice_code": "D035", "voice_type": "zh_female_xinlingjitang_moon_bigtts", "name": "心灵鸡汤"},
    {"voice_code": "D036", "voice_type": "zh_female_cancan_mars_bigtts", "name": "灿灿"},
    {"voice_code": "D037", "voice_type": "zh_male_tiancaitongsheng_mars_bigtts", "name": "天才童声"},
    {"voice_code": "D038", "voice_type": "zh_male_naiqimengwa_mars_bigtts", "name": "奶气萌娃"},
    {"voice_code": "D039", "voice_type": "zh_male_sunwukong_mars_bigtts", "name": "孙悟空"},
    {"voice_code": "D040", "voice_type": "zh_male_xionger_mars_bigtts", "name": "熊二"},
    {"voice_code": "D041", "voice_type": "zh_female_peiqi_mars_bigtts", "name": "佩奇"},
    {"voice_code": "D042", "voice_type": "zh_female_zhixingnvsheng_mars_bigtts", "name": "知性女声"},
    {"voice_code": "D043", "voice_type": "zh_female_qingxinnvsheng_mars_bigtts", "name": "清新女声"},
    {"voice_code": "D044", "voice_type": "zh_male_changtianyi_mars_bigtts", "name": "长天一"},
    {"voice_code": "D045", "voice_type": "zh_female_popo_mars_bigtts", "name": "婆婆"},
    {"voice_code": "D046", "voice_type": "zh_female_wuzetian_mars_bigtts", "name": "武则天"},
    {"voice_code": "D047", "voice_type": "zh_female_shaoergushi_mars_bigtts", "name": "少儿故事"},
    {"voice_code": "D048", "voice_type": "zh_male_silang_mars_bigtts", "name": "四郎"},
    {"voice_code": "D049", "voice_type": "zh_female_gujie_mars_bigtts", "name": "古姐"},
    {"voice_code": "D050", "voice_type": "zh_female_yingtaowanzi_mars_bigtts", "name": "樱桃丸子"},
    {"voice_code": "D051", "voice_type": "zh_male_jieshuonansheng_mars_bigtts", "name": "解说男生"},
    {"voice_code": "D052", "voice_type": "zh_female_jitangmeimei_mars_bigtts", "name": "鸡汤妹妹"},
    {"voice_code": "D053", "voice_type": "zh_male_chunhui_mars_bigtts", "name": "春晖"},
    {"voice_code": "D054", "voice_type": "zh_male_qingshuangnanda_mars_bigtts", "name": "清爽男声"},
    {"voice_code": "D055", "voice_type": "zh_female_tiexinnvsheng_mars_bigtts", "name": "贴心女生"},
    {"voice_code": "D056", "voice_type": "zh_female_qiaopinvsheng_mars_bigtts", "name": "俏皮女生"},
    {"voice_code": "D057", "voice_type": "zh_female_mengyatou_mars_bigtts", "name": "萌丫头"},
    {"voice_code": "D058", "voice_type": "zh_male_ruyaqingnian_mars_bigtts", "name": "儒雅青年"},
    {"voice_code": "D059", "voice_type": "zh_male_baqiqingshu_mars_bigtts", "name": "霸气倾诉"},
    {"voice_code": "D060", "voice_type": "zh_male_qingcang_mars_bigtts", "name": "青苍"},
    {"voice_code": "D061", "voice_type": "zh_male_yangguangqingnian_mars_bigtts", "name": "阳光青年"},
    {"voice_code": "D062", "voice_type": "zh_female_gufengshaoyu_mars_bigtts", "name": "古风少女"},
    {"voice_code": "D063", "voice_type": "zh_female_wenroushunv_mars_bigtts", "name": "温柔淑女"},
    {"voice_code": "D064", "voice_type": "ICL_zh_female_zhixingwenwan_tob", "name": "知性温婉"},
    {"voice_code": "D065", "voice_type": "ICL_zh_male_lvchaxiaoge_tob", "name": "旅途小哥"},
    {"voice_code": "D066", "voice_type": "ICL_zh_female_jiaoruoluoli_tob", "name": "娇弱萝莉"},
    {"voice_code": "D067", "voice_type": "ICL_zh_male_lengdanshuli_tob", "name": "冷淡梳理"},
    {"voice_code": "D068", "voice_type": "ICL_zh_male_nuanxintitie_tob", "name": "暖心体贴"},
    {"voice_code": "D069", "voice_type": "ICL_zh_male_hanhoudunshi_tob", "name": "憨厚敦实"},
    {"voice_code": "D070", "voice_type": "ICL_zh_female_wenrouwenya_tob", "name": "温柔文雅"},
    {"voice_code": "D071", "voice_type": "ICL_zh_male_aiqilingren_tob", "name": "爱妻暖男"},
    {"voice_code": "D072", "voice_type": "ICL_zh_male_kailangqingkuai_tob", "name": "开朗轻快"},
    {"voice_code": "D073", "voice_type": "ICL_zh_female_huopodiaoman_tob", "name": "活泼刁蛮"},
    {"voice_code": "D074", "voice_type": "ICL_zh_male_guzhibingjiao_tob", "name": "孤傲冰角"},
    {"voice_code": "D075", "voice_type": "ICL_zh_male_huoposhuanglang_tob", "name": "活泼爽朗"},
    {"voice_code": "D076", "voice_type": "ICL_zh_male_sajiaonianren_tob", "name": "撒娇粘人"},
    {"voice_code": "D077", "voice_type": "ICL_zh_female_aomanjiaosheng_tob", "name": "傲娇女声"},
    {"voice_code": "D078", "voice_type": "ICL_zh_male_xiaosasuixing_tob", "name": "潇洒随性"},
    {"voice_code": "D079", "voice_type": "ICL_zh_male_fuheigongzi_tob", "name": "腹黑公子"},
    {"voice_code": "D080", "voice_type": "ICL_zh_male_guiyishenmi_tob", "name": "诡秘神秘"},
    {"voice_code": "D081", "voice_type": "ICL_zh_male_ruyacaijun_tob", "name": "儒雅才俊"},
    {"voice_code": "D082", "voice_type": "ICL_zh_male_bingjiaobailian_tob", "name": "冰角白莲"},
    {"voice_code": "D083", "voice_type": "ICL_zh_male_zhengzhiqingnian_tob", "name": "正直青年"},
    {"voice_code": "D084", "voice_type": "ICL_zh_male_shuaizhenxiaohuo_tob", "name": "率真小伙"},
    {"voice_code": "D085", "voice_type": "ICL_zh_female_jiaohannvwang_tob", "name": "娇蛮女王"},
    {"voice_code": "D086", "voice_type": "ICL_zh_female_bingjiaomengmei_tob", "name": "冰角萌妹"},
    {"voice_code": "D087", "voice_type": "ICL_zh_male_qingsenaigou_tob", "name": "青涩爱狗"},
    {"voice_code": "D088", "voice_type": "ICL_zh_male_chunzhenxuedi_tob", "name": "纯真学弟"},
    {"voice_code": "D089", "voice_type": "ICL_zh_female_nuanxinxuejie_tob", "name": "暖心学姐"},
    {"voice_code": "D090", "voice_type": "ICL_zh_female_keainvsheng_tob", "name": "可爱女生"},
    {"voice_code": "D091", "voice_type": "ICL_zh_female_chengshujiejie_tob", "name": "成熟姐姐"},
    {"voice_code": "D092", "voice_type": "ICL_zh_female_bingjiaojiejie_tob", "name": "冰角姐姐"},
    {"voice_code": "D093", "voice_type": "ICL_zh_male_youroubangzhu_tob", "name": "温柔帮手"},
    {"voice_code": "D094", "voice_type": "ICL_zh_male_yourougongzi_tob", "name": "温柔公子"},
    {"voice_code": "D095", "voice_type": "ICL_zh_female_wumeiyujie_tob", "name": "妩媚御姐"},
    {"voice_code": "D096", "voice_type": "ICL_zh_female_tiaopigongzhu_tob", "name": "调皮公主"},
    {"voice_code": "D097", "voice_type": "ICL_zh_female_aojiaonvyou_tob", "name": "傲娇女友"},
    {"voice_code": "D098", "voice_type": "ICL_zh_male_tiexinnanyou_tob", "name": "贴心男友"},
    {"voice_code": "D099", "voice_type": "ICL_zh_male_shaonianjiangjun_tob", "name": "少年将军"},
    {"voice_code": "D100", "voice_type": "ICL_zh_female_tiexinnvyou_tob", "name": "贴心女友"},
    {"voice_code": "D101", "voice_type": "ICL_zh_male_bingjiaogege_tob", "name": "冰角哥哥"},
    {"voice_code": "D102", "voice_type": "ICL_zh_male_xuebanantongzhuo_tob", "name": "学霸同桌"},
    {"voice_code": "D103", "voice_type": "ICL_zh_male_youmoshushu_tob", "name": "幽默叔叔"},
    {"voice_code": "D104", "voice_type": "ICL_zh_female_xingganyujie_tob", "name": "性感御姐"},
    {"voice_code": "D105", "voice_type": "ICL_zh_female_jiaxiaozi_tob", "name": "假小子"},
    {"voice_code": "D106", "voice_type": "ICL_zh_male_lengjunshangsi_tob", "name": "冷峻上司"},
    {"voice_code": "D107", "voice_type": "ICL_zh_male_wenrounantongzhuo_tob", "name": "温柔同桌"},
    {"voice_code": "D108", "voice_type": "ICL_zh_male_bingjiaodidi_tob", "name": "冰角弟弟"},
    {"voice_code": "D109", "voice_type": "ICL_zh_male_youmodaye_tob", "name": "幽默大爷"},
    {"voice_code": "D110", "voice_type": "ICL_zh_male_aomanshaoye_tob", "name": "傲慢少爷"},
    {"voice_code": "D111", "voice_type": "ICL_zh_male_shenmifashi_tob", "name": "神秘法师"},
    {"voice_code": "D112", "voice_type": "zh_male_fanjuanqingnian_mars_bigtts", "name": "犯困青年"},
    {"voice_code": "D113", "voice_type": "zh_male_dongmanhaimian_mars_bigtts", "name": "动漫海绵"},
    {"voice_code": "D114", "voice_type": "zh_male_wenrouxiaoge_mars_bigtts", "name": "温柔小哥"},
    {"voice_code": "D115", "voice_type": "zh_male_lanxiaoyang_mars_bigtts", "name": "懒小羊"},
    {"voice_code": "D117", "voice_type": "zh_female_roumeinvyou_emo_v2_mars_bigtts", "name": "柔美女友"},
    {"voice_code": "D118", "voice_type": "zh_male_yangguangqingnian_emo_v2_mars_bigtts", "name": "阳光青年"},
    {"voice_code": "D119", "voice_type": "zh_female_meilinvyou_emo_v2_mars_bigtts", "name": "美丽女友"},
    {"voice_code": "D120", "voice_type": "zh_female_shuangkuaisisi_emo_v2_mars_bigtts", "name": "爽快思思"},
    {"voice_code": "D121", "voice_type": "zh_female_tianmeitaozi_mars_bigtts", "name": "甜美桃子"},
    {"voice_code": "D122", "voice_type": "zh_female_kefunvsheng_mars_bigtts", "name": "客服女生"},
    {"voice_code": "D123", "voice_type": "ICL_zh_female_ganli_v1_tob", "name": "甘莉"},
    {"voice_code": "D124", "voice_type": "ICL_zh_male_flc_v1_tob", "name": "风铃草"},
    {"voice_code": "D125", "voice_type": "ICL_zh_female_luoqing_v1_tob", "name": "洛清"},
    {"voice_code": "D126", "voice_type": "zh_female_qinqienvsheng_moon_bigtts", "name": "亲切女生"},
    {"voice_code": "D127", "voice_type": "ICL_zh_male_lengkugege_v1_tob", "name": "冷酷哥哥"},
    {"voice_code": "D128", "voice_type": "zh_male_M100_conversation_wvae_bigtts", "name": "M100"},
    {"voice_code": "D129", "voice_type": "zh_male_xudong_conversation_wvae_bigtts", "name": "徐冬"},
    {"voice_code": "D130", "voice_type": "zh_female_maomao_conversation_wvae_bigtts", "name": "毛毛"},
    {"voice_code": "D131", "voice_type": "zh_male_qingyiyuxuan_mars_bigtts", "name": "清逸宇轩"},
    {"voice_code": "D132", "voice_type": "zh_female_sophie_conversation_wvae_bigtts", "name": "苏菲"},
    {"voice_code": "D133", "voice_type": "ICL_zh_female_bingjiao3_tob", "name": "冰角三号"},
    {"voice_code": "D134", "voice_type": "ICL_zh_male_yangyang_v1_tob", "name": "阳阳"},
    {"voice_code": "D135", "voice_type": "ICL_zh_female_wenyinvsheng_v1_tob", "name": "温婉女生"},
    {"voice_code": "D136", "voice_type": "ICL_zh_male_BV144_paoxiaoge_v1_tob", "name": "咆哮哥"},
    {"voice_code": "D137", "voice_type": "zh_male_junlangnanyou_emo_v2_mars_bigtts", "name": "俊朗男友"},
    {"voice_code": "D138", "voice_type": "zh_male_ruyayichen_emo_v2_mars_bigtts", "name": "儒雅一辰"},
    {"voice_code": "D139", "voice_type": "ICL_zh_male_bingruogongzi_tob", "name": "孱弱公子"},
    {"voice_code": "D141", "voice_type": "zh_male_zhoujielun_emo_v2_mars_bigtts", "name": "周杰伦"},
    {"voice_code": "D142", "voice_type": "zh_male_yourougongzi_emo_v2_mars_bigtts", "name": "温柔公子"},
    {"voice_code": "D143", "voice_type": "zh_female_linjuayi_emo_v2_mars_bigtts", "name": "林俊怡"},
    {"voice_code": "D144", "voice_type": "zh_male_jingqiangkanye_emo_mars_bigtts", "name": "京腔侃爷"},
    {"voice_code": "D146", "voice_type": "zh_male_aojiaobazong_emo_v2_mars_bigtts", "name": "傲娇霸总"},
    {"voice_code": "D147", "voice_type": "zh_female_tianxinxiaomei_emo_v2_mars_bigtts", "name": "甜心小妹"},
    {"voice_code": "D148", "voice_type": "zh_female_gaolengyujie_emo_v2_mars_bigtts", "name": "高冷御姐"},
    {"voice_code": "D150", "voice_type": "ICL_zh_male_you_tob", "name": "悠"},
    {"voice_code": "D151", "voice_type": "ICL_zh_male_ms_tob", "name": "明山"},
    {"voice_code": "D152", "voice_type": "ICL_zh_female_yilin_tob", "name": "依琳"},
    {"voice_code": "D153", "voice_type": "ICL_zh_female_yry_tob", "name": "嫣然"},
    {"voice_code": "D154", "voice_type": "ICL_zh_female_xnx_tob", "name": "小暖"},
    {"voice_code": "D155", "voice_type": "ICL_zh_female_yuxin_v1_tob", "name": "雨欣"},
    {"voice_code": "D156", "voice_type": "ICL_zh_female_qiuling_v1_tob", "name": "秋玲"},
    {"voice_code": "D157", "voice_type": "ICL_zh_male_shenmi_v1_tob", "name": "神秘"},
    {"voice_code": "D158", "voice_type": "ICL_zh_male_huzi_v1_tob", "name": "胡子"},
    {"voice_code": "D159", "voice_type": "ICL_zh_male_buyan_v1_tob", "name": "不言"},
    {"voice_code": "D160", "voice_type": "ICL_zh_female_feicui_v1_tob", "name": "翡翠"},
    {"voice_code": "D161", "voice_type": "ICL_zh_female_xiangliangya_v1_tob", "name": "香良雅"},
    {"voice_code": "D162", "voice_type": "ICL_zh_male_guaogongzi_v1_tob", "name": "贵公子"},
    {"voice_code": "D163", "voice_type": "ICL_zh_male_asmryexiu_tob", "name": "夜修"},
    {"voice_code": "D164", "voice_type": "ICL_zh_male_aomanqingnian_tob", "name": "傲慢青年"},
    {"voice_code": "D165", "voice_type": "ICL_zh_male_cujingnanyou_tob", "name": "粗犷男友"},
    {"voice_code": "D166", "voice_type": "ICL_zh_male_cujingnansheng_tob", "name": "粗犷男生"},
    {"voice_code": "D167", "voice_type": "ICL_zh_male_shuanglangshaonian_tob", "name": "爽朗少年"},
    {"voice_code": "D168", "voice_type": "ICL_zh_male_sajiaonanyou_tob", "name": "撒娇男友"},
    {"voice_code": "D169", "voice_type": "ICL_zh_male_wenrounanyou_tob", "name": "温柔男友"},
    {"voice_code": "D170", "voice_type": "ICL_zh_male_wenshunshaonian_tob", "name": "温顺少年"},
    {"voice_code": "D171", "voice_type": "ICL_zh_male_naigounanyou_tob", "name": "奶狗男友"},
    {"voice_code": "D172", "voice_type": "ICL_zh_male_sajiaonansheng_tob", "name": "撒娇男生"},
    {"voice_code": "D173", "voice_type": "ICL_zh_male_huoponanyou_tob", "name": "活泼男友"},
    {"voice_code": "D174", "voice_type": "ICL_zh_male_tianxinanyou_tob", "name": "甜心男友"},
    {"voice_code": "D175", "voice_type": "ICL_zh_male_huoliqingnian_tob", "name": "活力青年"},
    {"voice_code": "D176", "voice_type": "ICL_zh_male_kailangqingnian_tob", "name": "开朗青年"},
    {"voice_code": "D177", "voice_type": "ICL_zh_male_lengmoxiongzhang_tob", "name": "冷漠兄长"},
    {"voice_code": "D178", "voice_type": "ICL_zh_male_tiancaitongzhuo_tob", "name": "天才同桌"},
    {"voice_code": "D179", "voice_type": "ICL_zh_male_aojiaojingying_tob", "name": "傲娇精英"},
    {"voice_code": "D180", "voice_type": "ICL_zh_male_pianpiangongzi_tob", "name": "翩翩公子"},
    {"voice_code": "D181", "voice_type": "ICL_zh_male_mengdongqingnian_tob", "name": "懵懂青年"},
    {"voice_code": "D182", "voice_type": "ICL_zh_male_lenglianxiongzhang_tob", "name": "冷脸兄长"},
    {"voice_code": "D183", "voice_type": "ICL_zh_male_bingjiaoshaonian_tob", "name": "冰角少年"},
    {"voice_code": "D184", "voice_type": "ICL_zh_male_bingjiaonanyou_tob", "name": "冰角男友"},
    {"voice_code": "D185", "voice_type": "ICL_zh_male_bingruoshaonian_tob", "name": "孱弱少年"},
    {"voice_code": "D186", "voice_type": "ICL_zh_male_yiqishaonian_tob", "name": "意气少年"},
    {"voice_code": "D187", "voice_type": "ICL_zh_male_ganjingshaonian_tob", "name": "干净少年"},
    {"voice_code": "D188", "voice_type": "ICL_zh_male_lengmonanyou_tob", "name": "冷漠男友"},
    {"voice_code": "D189", "voice_type": "ICL_zh_male_jingyingqingnian_tob", "name": "精英青年"},
    {"voice_code": "D190", "voice_type": "ICL_zh_male_fengfashaonian_tob", "name": "风发少年"},
    {"voice_code": "D191", "voice_type": "ICL_zh_male_rexueshaonian_tob", "name": "热血少年"},
    {"voice_code": "D192", "voice_type": "ICL_zh_male_qingshuangshaonian_tob", "name": "清爽少年"},
    {"voice_code": "D193", "voice_type": "ICL_zh_male_zhongerqingnian_tob", "name": "中二青年"},
    {"voice_code": "D194", "voice_type": "ICL_zh_male_lingyunqingnian_tob", "name": "凌云青年"},
    {"voice_code": "D195", "voice_type": "ICL_zh_male_zifuqingnian_tob", "name": "自富青年"},
    {"voice_code": "D196", "voice_type": "ICL_zh_male_bujiqingnian_tob", "name": "不羁青年"},
    {"voice_code": "D197", "voice_type": "ICL_zh_male_ruyajunzi_tob", "name": "儒雅君子"},
    {"voice_code": "D198", "voice_type": "ICL_zh_male_diyinchenyu_tob", "name": "低音沉郁"},
    {"voice_code": "D199", "voice_type": "ICL_zh_male_lenglianxueba_tob", "name": "冷脸学霸"},
    {"voice_code": "D200", "voice_type": "ICL_zh_male_ruyazongcai_tob", "name": "儒雅总裁"},
    {"voice_code": "D201", "voice_type": "ICL_zh_male_shenchenzongcai_tob", "name": "深沉总裁"},
    {"voice_code": "D202", "voice_type": "ICL_zh_male_xiaohouye_tob", "name": "小侯爷"},
    {"voice_code": "D203", "voice_type": "ICL_zh_male_gugaogongzi_tob", "name": "孤高公子"},
    {"voice_code": "D204", "voice_type": "ICL_zh_male_zhangjianjunzi_tob", "name": "仗剑君子"},
    {"voice_code": "D205", "voice_type": "ICL_zh_male_wenrunxuezhe_tob", "name": "温润学者"},
    {"voice_code": "D206", "voice_type": "ICL_zh_male_qinqieqingnian_tob", "name": "亲切青年"},
    {"voice_code": "D207", "voice_type": "ICL_zh_male_wenrouxuezhang_tob", "name": "温柔学长"},
    {"voice_code": "D208", "voice_type": "ICL_zh_male_gaolengzongcai_tob", "name": "高冷总裁"},
    {"voice_code": "D209", "voice_type": "ICL_zh_male_lengjungaozhi_tob", "name": "冷峻高知"},
    {"voice_code": "D210", "voice_type": "ICL_zh_male_chanruoshaoye_tob", "name": "孱弱少爷"},
    {"voice_code": "D211", "voice_type": "ICL_zh_male_zixinqingnian_tob", "name": "自信青年"},
    {"voice_code": "D212", "voice_type": "ICL_zh_male_qingseqingnian_tob", "name": "青色青年"},
    {"voice_code": "D213", "voice_type": "ICL_zh_male_xuebatongzhuo_tob", "name": "学霸同桌"},
    {"voice_code": "D214", "voice_type": "ICL_zh_male_lengaozongcai_tob", "name": "冷傲总裁"},
    {"voice_code": "D215", "voice_type": "ICL_zh_male_badaoshaoye_tob", "name": "霸道少爷"},
    {"voice_code": "D216", "voice_type": "ICL_zh_male_yuanqishaonian_tob", "name": "元气少年"},
    {"voice_code": "D217", "voice_type": "ICL_zh_male_satuoqingnian_tob", "name": "洒脱青年"},
    {"voice_code": "D218", "voice_type": "ICL_zh_male_zhishuaiqingnian_tob", "name": "智帅青年"},
    {"voice_code": "D219", "voice_type": "ICL_zh_male_siwenqingnian_tob", "name": "斯文青年"},
    {"voice_code": "D220", "voice_type": "ICL_zh_male_chengshuzongcai_tob", "name": "成熟总裁"},
    {"voice_code": "D221", "voice_type": "ICL_zh_male_junyigongzi_tob", "name": "俊逸公子"},
    {"voice_code": "D222", "voice_type": "ICL_zh_male_aojiaogongzi_tob", "name": "傲娇公子"},
    {"voice_code": "D223", "voice_type": "ICL_zh_male_zhangjianxiake_tob", "name": "仗剑侠客"},
    {"voice_code": "D224", "voice_type": "ICL_zh_male_jijiaozhineng_tob", "name": "机教智能"},
    {"voice_code": "D225", "voice_type": "zh_male_lengkugege_emo_v2_mars_bigtts", "name": "冷酷哥哥"},
    {"voice_code": "D226", "voice_type": "ICL_zh_male_badaozongcai_v1_tob", "name": "霸道总裁"},
    {"voice_code": "D227", "voice_type": "zh_female_yingyujiaoyu_mars_bigtts", "name": "英语教育"},
    {"voice_code": "D228", "voice_type": "zh_female_vv_mars_bigtts", "name": "薇薇"},
    {"voice_code": "D229", "voice_type": "BV001_streaming", "name": "BV001"},
    {"voice_code": "D230", "voice_type": "BV002_streaming", "name": "BV002"},
    {"voice_code": "D231", "voice_type": "BV005_streaming", "name": "BV005"},
    {"voice_code": "D232", "voice_type": "BV007_streaming", "name": "BV007"},
    {"voice_code": "D233", "voice_type": "BV056_streaming", "name": "BV056"},
    {"voice_code": "D234", "voice_type": "BV102_streaming", "name": "BV102"},
    {"voice_code": "D235", "voice_type": "BV113_streaming", "name": "BV113"},
    {"voice_code": "D236", "voice_type": "BV115_streaming", "name": "BV115"},
    {"voice_code": "D237", "voice_type": "BV119_streaming", "name": "BV119"},
    {"voice_code": "D238", "voice_type": "BV700_streaming", "name": "BV700"},
    {"voice_code": "D239", "voice_type": "BV701_streaming", "name": "BV701"}
]

def get_cluster(voice_type):
    """获取集群名称"""
    if voice_type.startswith("ICL_"):
        return "volcano_icl"
    return "volcano_tts"

def test_voice(voice_type, voice_name, test_text="你好，这是一个测试"):
    """测试单个音色"""
    cluster = get_cluster(voice_type)
    
    request_json = {
        "app": {
            "appid": DOUYIN_CONFIG["APPID"],
            "token": DOUYIN_CONFIG["ACCESS_TOKEN"],
            "cluster": cluster
        },
        "user": {
            "uid": str(uuid.uuid4())
        },
        "audio": {
            "voice_type": voice_type,
            "encoding": "wav",
            "sample_rate": 16000,
            "speed_ratio": 1.0,
            "volume_ratio": 1.0,
            "pitch_ratio": 1.0,
        },
        "request": {
            "reqid": str(uuid.uuid4()),
            "text": test_text,
            "text_type": "plain",
            "operation": "query",
            "with_frontend": 1,
            "frontend_type": "unitTson"
        }
    }
    
    headers = {"Authorization": f"Bearer;{DOUYIN_CONFIG['ACCESS_TOKEN']}"}
    
    try:
        resp = requests.post(API_URL, json.dumps(request_json), headers=headers, timeout=10)
        
        if resp.status_code == 200:
            resp_data = resp.json()
            if "data" in resp_data:
                audio_data = base64.b64decode(resp_data["data"])
                return True, f"成功 ({len(audio_data)} 字节)"
            else:
                return False, f"API响应无数据: {resp_data.get('message', '未知错误')}"
        else:
            try:
                error_data = resp.json()
                return False, f"HTTP {resp.status_code}: {error_data.get('message', resp.text)}"
            except:
                return False, f"HTTP {resp.status_code}: {resp.text}"
                
    except requests.exceptions.Timeout:
        return False, "请求超时"
    except Exception as e:
        return False, f"请求异常: {e}"

def main():
    print("=" * 60)
    print("豆包TTS音色测试工具")
    print("=" * 60)
    
    print(f"API端点: {API_URL}")
    print(f"测试文本: 你好，这是一个测试")
    print(f"总计测试音色: {len(TEST_VOICES)} 个")
    print("=" * 60)
    
    available_voices = []
    unavailable_voices = []
    
    for i, voice in enumerate(TEST_VOICES, 1):
        voice_name = voice["name"]
        voice_type = voice["voice_type"]
        
        print(f"[{i:2d}/{len(TEST_VOICES)}] 测试 {voice_name} ({voice_type})")
        
        success, message = test_voice(voice_type, voice_name)
        
        if success:
            print(f"  ✓ {message}")
            available_voices.append(voice)
        else:
            print(f"  ✗ {message}")
            unavailable_voices.append(voice)
        
        # 避免请求过快
        time.sleep(0.5)
    
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    print(f"\n✅ 可用音色 ({len(available_voices)} 个):")
    for voice in available_voices:
        print(f"  - {voice['name']}: {voice['voice_type']}")
    
    print(f"\n❌ 不可用音色 ({len(unavailable_voices)} 个):")
    for voice in unavailable_voices:
        print(f"  - {voice['name']}: {voice['voice_type']}")
    
    # 生成可用音色的配置代码
    if available_voices:
        print(f"\n📋 可用音色配置代码:")
        print("VOICE_CONFIGS = [")
        for i, voice in enumerate(available_voices):
            voice_code = f"V{i+1:03d}"
            print(f'    {{"voice_code": "{voice_code}", "voice_type": "{voice["voice_type"]}", "name": "{voice["name"]}"}},')
        print("]")
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
