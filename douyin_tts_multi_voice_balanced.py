#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包语音合成大模型WebSocket API调用程序 - 多音色均衡版
每种音色生成1小时音频（18000-24000字），文本长度从2-30均匀分布
"""

import asyncio
import json
import logging
import uuid
import os
import time
import glob
from datetime import datetime
import websockets
from protocols import MsgType, full_client_request, receive_message

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ==================== 配置区域 ====================
DOUYIN_CONFIG = {
    # API配置
    "APPID": "9862368305",
    "ACCESS_TOKEN": "bbxNaAzRzBSkySjouDItXl6tBUUyNd2W",
    
    # WebSocket配置
    "ENDPOINT": "wss://openspeech.bytedance.com/api/v1/tts/ws_binary",
    
    # 音频格式配置
    "ENCODING": "wav",
    
    # 文件配置
    "TEXT_FILE": "../shuffled_from_rank_random.txt",
    "PINYIN_FILE": "../shuffled_from_rank_random_pinyin.txt",
    "OUTPUT_DIR": "audio_output_balanced",
    "CSV_FILE": "douyin_balanced_audio_log.csv",
    "REQUEST_INTERVAL": 2.0,  # WebSocket请求间隔（增加到2秒）
    
    # 生成配置
    "MIN_CHARS_PER_VOICE": 18000,  # 每种音色最少字数
    "MAX_CHARS_PER_VOICE": 24000,  # 每种音色最多字数
}

# 豆包音色配置 - 50种音色
VOICE_CONFIGS = [
    {"voice_code": "D001", "voice_type": "zh_female_wanqudashu_moon_bigtts", "name": "婉曲大叔"},
    {"voice_code": "D002", "voice_type": "zh_female_daimengchuanmei_moon_bigtts", "name": "黛梦传媒"},
    {"voice_code": "D003", "voice_type": "zh_male_guozhoudege_moon_bigtts", "name": "国州德哥"},
    {"voice_code": "D004", "voice_type": "zh_male_beijingxiaoye_moon_bigtts", "name": "北京小爷"},
    {"voice_code": "D005", "voice_type": "zh_male_shaonianzixin_moon_bigtts", "name": "少年子心"},
    {"voice_code": "D006", "voice_type": "zh_female_meilinvyou_moon_bigtts", "name": "美丽女友"},
    {"voice_code": "D007", "voice_type": "zh_male_shenyeboke_moon_bigtts", "name": "深夜播客"},
    {"voice_code": "D008", "voice_type": "zh_female_sajiaonvyou_moon_bigtts", "name": "撒娇女友"},
    {"voice_code": "D009", "voice_type": "zh_female_yuanqinvyou_moon_bigtts", "name": "远亲女友"},
    {"voice_code": "D010", "voice_type": "zh_male_haoyuxiaoge_moon_bigtts", "name": "豪宇小哥"},
    {"voice_code": "D011", "voice_type": "zh_male_guangxiyuanzhou_moon_bigtts", "name": "广西远州"},
    {"voice_code": "D012", "voice_type": "zh_female_meituojieer_moon_bigtts", "name": "美托姐儿"},
    {"voice_code": "D013", "voice_type": "zh_male_yuzhouzixuan_moon_bigtts", "name": "宇宙子轩"},
    {"voice_code": "D014", "voice_type": "zh_female_linjianvhai_moon_bigtts", "name": "邻家女孩"},
    {"voice_code": "D015", "voice_type": "zh_female_gaolengyujie_moon_bigtts", "name": "高冷御姐"},
    {"voice_code": "D016", "voice_type": "zh_male_yuanboxiaoshu_moon_bigtts", "name": "渊博小叔"},
    {"voice_code": "D017", "voice_type": "zh_male_yangguangqingnian_moon_bigtts", "name": "阳光青年"},
    {"voice_code": "D018", "voice_type": "zh_male_aojiaobazong_moon_bigtts", "name": "傲娇霸总"},
    {"voice_code": "D019", "voice_type": "zh_male_jingqiangkanye_moon_bigtts", "name": "京腔侃爷"},
    {"voice_code": "D020", "voice_type": "zh_female_shuangkuaisisi_moon_bigtts", "name": "爽快思思"},
    {"voice_code": "D021", "voice_type": "zh_male_wennuanahu_moon_bigtts", "name": "温暖阿虎"},
    {"voice_code": "D022", "voice_type": "zh_female_wanwanxiaohe_moon_bigtts", "name": "婉婉小荷"},
    {"voice_code": "D023", "voice_type": "ICL_zh_female_bingruoshaonv_tob", "name": "冰若少女"},
    {"voice_code": "D024", "voice_type": "ICL_zh_female_huoponvhai_tob", "name": "活泼女孩"},
    {"voice_code": "D025", "voice_type": "ICL_zh_female_heainainai_tob", "name": "和蔼奶奶"},
    {"voice_code": "D026", "voice_type": "ICL_zh_female_linjuayi_tob", "name": "邻居阿姨"},
    {"voice_code": "D027", "voice_type": "zh_female_wenrouxiaoya_moon_bigtts", "name": "温柔小雅"},
    {"voice_code": "D028", "voice_type": "zh_female_tianmeixiaoyuan_moon_bigtts", "name": "甜美小园"},
    {"voice_code": "D029", "voice_type": "zh_female_qingchezizi_moon_bigtts", "name": "清澈紫紫"},
    {"voice_code": "D030", "voice_type": "zh_male_dongfanghaoran_moon_bigtts", "name": "东方浩然"},
    {"voice_code": "D031", "voice_type": "zh_male_jieshuoxiaoming_moon_bigtts", "name": "解说小明"},
    {"voice_code": "D032", "voice_type": "zh_female_kailangjiejie_moon_bigtts", "name": "开朗姐姐"},
    {"voice_code": "D033", "voice_type": "zh_male_linjiananhai_moon_bigtts", "name": "邻家男孩"},
    {"voice_code": "D034", "voice_type": "zh_female_tianmeiyueyue_moon_bigtts", "name": "甜美月月"},
    {"voice_code": "D035", "voice_type": "zh_female_xinlingjitang_moon_bigtts", "name": "心灵鸡汤"},
    {"voice_code": "D036", "voice_type": "zh_female_cancan_mars_bigtts", "name": "灿灿"},
    {"voice_code": "D037", "voice_type": "zh_male_tiancaitongsheng_mars_bigtts", "name": "天才童声"},
    {"voice_code": "D038", "voice_type": "zh_male_naiqimengwa_mars_bigtts", "name": "奶气萌娃"},
    {"voice_code": "D039", "voice_type": "zh_male_sunwukong_mars_bigtts", "name": "孙悟空"},
    {"voice_code": "D040", "voice_type": "zh_male_xionger_mars_bigtts", "name": "熊二"},
    {"voice_code": "D041", "voice_type": "zh_female_peiqi_mars_bigtts", "name": "佩奇"},
    {"voice_code": "D042", "voice_type": "zh_female_zhixingnvsheng_mars_bigtts", "name": "知性女声"},
    {"voice_code": "D043", "voice_type": "zh_female_qingxinnvsheng_mars_bigtts", "name": "清新女声"},
    {"voice_code": "D044", "voice_type": "zh_male_changtianyi_mars_bigtts", "name": "长天一"},
    {"voice_code": "D045", "voice_type": "zh_female_popo_mars_bigtts", "name": "婆婆"},
    {"voice_code": "D046", "voice_type": "zh_female_wuzetian_mars_bigtts", "name": "武则天"},
    {"voice_code": "D047", "voice_type": "zh_female_shaoergushi_mars_bigtts", "name": "少儿故事"},
    {"voice_code": "D048", "voice_type": "zh_male_silang_mars_bigtts", "name": "四郎"},
    {"voice_code": "D049", "voice_type": "zh_female_gujie_mars_bigtts", "name": "古姐"},
    {"voice_code": "D050", "voice_type": "zh_female_yingtaowanzi_mars_bigtts", "name": "樱桃丸子"},
]

def get_cluster(voice: str) -> str:
    """获取集群名称"""
    if voice.startswith("ICL_"):
        return "volcano_icl"
    return "volcano_tts"

class DouyinTTSWebSocket:
    """豆包TTS WebSocket核心类"""
    
    def __init__(self, config):
        self.config = config
        self.headers = {
            "Authorization": f"Bearer;{config['ACCESS_TOKEN']}",
        }
    
    async def synthesize_text(self, text, voice_type):
        """使用WebSocket合成单个文本，带重试机制"""
        max_retries = 3

        for retry_count in range(max_retries):
            try:
                # 连接WebSocket，增加超时设置
                websocket = await websockets.connect(
                    self.config["ENDPOINT"],
                    additional_headers=self.headers,
                    max_size=10 * 1024 * 1024,
                    ping_interval=20,
                    ping_timeout=10,
                    close_timeout=10
                )

                try:
                    # 确定集群
                    cluster = get_cluster(voice_type)

                    # 准备请求负载
                    request = {
                        "app": {
                            "appid": self.config["APPID"],
                            "token": self.config["ACCESS_TOKEN"],
                            "cluster": cluster,
                        },
                        "user": {
                            "uid": str(uuid.uuid4()),
                        },
                        "audio": {
                            "voice_type": voice_type,
                            "encoding": self.config["ENCODING"],
                        },
                        "request": {
                            "reqid": str(uuid.uuid4()),
                            "text": text,
                            "operation": "submit",
                            "with_timestamp": "1",
                            "extra_param": json.dumps({
                                "disable_markdown_filter": False,
                            }),
                        },
                    }

                    # 发送请求
                    await full_client_request(websocket, json.dumps(request).encode())

                    # 接收音频数据（使用官方正确的方式）
                    audio_data = bytearray()
                    while True:
                        msg = await receive_message(websocket)

                        if msg.type == MsgType.FrontEndResultServer:
                            continue
                        elif msg.type == MsgType.AudioOnlyServer:
                            audio_data.extend(msg.payload)
                            if msg.sequence < 0:  # 最后一条消息
                                break
                        elif msg.type == MsgType.ErrorServer:
                            # 解析错误消息
                            try:
                                error_data = json.loads(msg.payload.decode('utf-8'))
                                error_msg = error_data.get('message', '未知错误')
                                error_code = error_data.get('code', 'N/A')
                                raise RuntimeError(f"服务器错误 [{error_code}]: {error_msg}")
                            except json.JSONDecodeError:
                                raise RuntimeError(f"服务器错误: {msg.payload.decode('utf-8', errors='ignore')}")
                        else:
                            raise RuntimeError(f"未知消息类型: {msg.type}, 内容: {msg}")

                    # 检查是否收到音频数据
                    if not audio_data:
                        raise RuntimeError("未收到音频数据")

                    return bytes(audio_data)

                finally:
                    await websocket.close()

            except Exception as e:
                logger.error(f"WebSocket合成失败 (尝试 {retry_count + 1}/{max_retries}): {e}")
                if retry_count < max_retries - 1:
                    # 等待后重试，逐渐增加等待时间
                    wait_time = (retry_count + 1) * 2
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"所有重试失败，放弃合成")
                    return None

        return None

def load_text_files(config):
    """加载文本和拼音文件"""
    print("=== 加载文本文件 ===")
    
    # 读取文本文件
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    return texts, pinyins

def group_texts_by_length(texts, pinyins):
    """按文本长度分组"""
    length_groups = {}
    
    for i, text in enumerate(texts):
        length = len(text)
        if length not in length_groups:
            length_groups[length] = []
        
        pinyin = pinyins[i] if i < len(pinyins) else ""
        length_groups[length].append({
            'text': text,
            'pinyin': pinyin,
            'index': i
        })
    
    return length_groups

def calculate_voice_distribution(texts, config):
    """计算需要多少种音色以及每种音色的文本分配"""
    total_chars = sum(len(text) for text in texts)
    min_chars = config["MIN_CHARS_PER_VOICE"]
    max_chars = config["MAX_CHARS_PER_VOICE"]
    
    # 计算需要的音色数量
    min_voices_needed = total_chars // max_chars + (1 if total_chars % max_chars > 0 else 0)
    max_voices_possible = total_chars // min_chars
    
    # 选择一个合理的音色数量
    target_voices = min(len(VOICE_CONFIGS), max_voices_possible)
    target_chars_per_voice = total_chars // target_voices
    
    print(f"文本总字数: {total_chars}")
    print(f"最少需要音色: {min_voices_needed}")
    print(f"最多可用音色: {max_voices_possible}")
    print(f"实际使用音色: {target_voices}")
    print(f"每种音色平均字数: {target_chars_per_voice}")
    
    return target_voices, target_chars_per_voice

def distribute_texts_to_voices(length_groups, target_voices, target_chars_per_voice):
    """将文本按长度均匀分配给各个音色"""
    voice_assignments = [[] for _ in range(target_voices)]
    voice_char_counts = [0] * target_voices
    
    # 按长度从小到大处理
    for length in sorted(length_groups.keys()):
        texts_of_length = length_groups[length]
        
        # 将这个长度的文本均匀分配给各个音色
        for i, text_info in enumerate(texts_of_length):
            # 找到当前字数最少的音色
            voice_idx = voice_char_counts.index(min(voice_char_counts))
            
            # 检查是否会超过目标字数太多
            if voice_char_counts[voice_idx] + length <= target_chars_per_voice * 1.2:  # 允许20%的超出
                voice_assignments[voice_idx].append(text_info)
                voice_char_counts[voice_idx] += length
            else:
                # 如果会超出太多，找下一个最少的音色
                sorted_indices = sorted(range(target_voices), key=lambda x: voice_char_counts[x])
                assigned = False
                for voice_idx in sorted_indices:
                    if voice_char_counts[voice_idx] + length <= target_chars_per_voice * 1.2:
                        voice_assignments[voice_idx].append(text_info)
                        voice_char_counts[voice_idx] += length
                        assigned = True
                        break
                
                if not assigned:
                    # 如果都会超出，分配给字数最少的
                    voice_idx = voice_char_counts.index(min(voice_char_counts))
                    voice_assignments[voice_idx].append(text_info)
                    voice_char_counts[voice_idx] += length
    
    return voice_assignments, voice_char_counts

def get_existing_files(output_dir, voice_code):
    """获取指定音色已存在的文件"""
    pattern = os.path.join(output_dir, f"Sc{voice_code}*.wav")
    audio_files = glob.glob(pattern)
    return len(audio_files)

async def generate_voice_batch(voice_config, text_assignments, config, batch_num, total_voices):
    """生成单个音色批次的音频"""
    voice_type = voice_config["voice_type"]
    voice_name = voice_config["name"]
    voice_code = voice_config["voice_code"]

    print(f"\n=== 批次 {batch_num}/{total_voices}: {voice_name} ({voice_code}) ===")

    if not text_assignments:
        print("✗ 没有分配的文本")
        return False

    # 计算实际字数
    actual_chars = sum(len(item['text']) for item in text_assignments)
    print(f"分配文本: {len(text_assignments)} 条")
    print(f"实际字数: {actual_chars} 字")

    # 检查已存在的文件
    existing_count = get_existing_files(config["OUTPUT_DIR"], voice_code)
    if existing_count > 0:
        print(f"已存在 {existing_count} 个文件，继续生成...")

    # 创建TTS对象
    tts = DouyinTTSWebSocket(config)

    # 统计信息
    success_count = 0
    error_count = 0
    start_time = time.time()

    # 打开CSV文件进行追加
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    csv_exists = os.path.exists(csv_path)

    with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
        # 写入标题行（如果文件不存在）
        if not csv_exists:
            csvfile.write('音频名\t类型\t文本\t注音\t音色\n')

        for i, text_info in enumerate(text_assignments, 1):
            text = text_info['text']
            pinyin = text_info['pinyin']

            # 音频文件名格式：Sc + 音色代码 + 序号（7位）
            audio_name = f"Sc{voice_code}{i:07d}"
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")

            print(f"[{i}/{len(text_assignments)}] {audio_name}: {text[:30]}{'...' if len(text) > 30 else ''}")

            # 检查文件是否已存在
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                success_count += 1
                continue

            # 生成音频（synthesize_text函数内部已有重试机制）
            audio_data = await tts.synthesize_text(text, voice_type)

            if audio_data:
                try:
                    # 保存音频文件
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)

                    # 写入CSV记录
                    csvfile.write(f"{audio_name}\tc\t{text}\t{pinyin}\t{voice_name}\n")
                    csvfile.flush()

                    print(f"  ✓ 成功 ({len(audio_data)} 字节)")
                    success_count += 1

                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1

            # 请求间隔
            await asyncio.sleep(config["REQUEST_INTERVAL"])

            # 每100个显示进度
            if i % 100 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(text_assignments) - i) * avg_time
                print(f"  进度: {i}/{len(text_assignments)}, 预计剩余: {remaining/60:.2f}分钟")

    elapsed_time = time.time() - start_time
    print(f"\n批次 {batch_num} 完成:")
    print(f"  耗时: {elapsed_time/60:.2f}分钟")
    print(f"  成功: {success_count}")
    print(f"  失败: {error_count}")
    print(f"  实际生成字数: {actual_chars}")
    print(f"  成功率: {(success_count / len(text_assignments)) * 100:.1f}%")

    # 显示生成的文件信息
    if success_count > 0:
        print(f"  生成文件: Sc{voice_code}0000001.wav - Sc{voice_code}{success_count:07d}.wav")

    return error_count == 0

async def main():
    """主函数"""
    print("=" * 60)
    print("豆包语音合成大模型WebSocket API - 多音色均衡版")
    print("=" * 60)

    config = DOUYIN_CONFIG

    print("配置信息:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  拼音文件: {config['PINYIN_FILE']}")
    print(f"  输出目录: {config['OUTPUT_DIR']}")
    print(f"  每种音色字数范围: {config['MIN_CHARS_PER_VOICE']}-{config['MAX_CHARS_PER_VOICE']} 字")
    print(f"  请求间隔: {config['REQUEST_INTERVAL']}秒")

    print("=" * 60)

    # 1. 检查必要文件
    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return

    if not os.path.exists(config['PINYIN_FILE']):
        print(f"⚠️ 拼音文件不存在: {config['PINYIN_FILE']}")
        print("将继续处理，但拼音字段将为空")

    # 2. 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)

    # 3. 加载文本和拼音文件
    print(f"\n=== 加载文本文件 ===")
    texts, pinyins = load_text_files(config)
    if texts is None:
        return

    # 4. 按长度分组文本
    print(f"\n=== 分析文本长度分布 ===")
    length_groups = group_texts_by_length(texts, pinyins)

    print(f"文本长度范围: {min(length_groups.keys())}-{max(length_groups.keys())} 字")
    print(f"长度种类数: {len(length_groups)}")

    # 显示长度分布
    for length in sorted(length_groups.keys())[:10]:  # 显示前10个长度
        count = len(length_groups[length])
        print(f"  {length}字: {count} 条")

    # 5. 计算音色分配
    print(f"\n=== 计算音色分配 ===")
    target_voices, target_chars_per_voice = calculate_voice_distribution(texts, config)

    # 6. 分配文本到各个音色
    print(f"\n=== 分配文本到音色 ===")
    voice_assignments, voice_char_counts = distribute_texts_to_voices(
        length_groups, target_voices, target_chars_per_voice
    )

    # 显示分配结果
    print(f"\n音色分配结果:")
    for i in range(target_voices):
        voice_config = VOICE_CONFIGS[i]
        char_count = voice_char_counts[i]
        text_count = len(voice_assignments[i])
        print(f"  {voice_config['voice_code']} {voice_config['name']}: {text_count} 条文本, {char_count} 字")

    # 7. 初始化CSV文件
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    if not os.path.exists(csv_path):
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            csvfile.write('音频名\t类型\t文本\t注音\t音色\n')
        print(f"✓ 创建CSV文件: {csv_path}")

    # 8. 确认生成
    try:
        confirm = input(f"\n是否开始生成 {target_voices} 种音色的音频？(y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("已取消生成")
            return
    except KeyboardInterrupt:
        print("\n程序被中断")
        return

    # 9. 开始分批生成
    print(f"\n=== 开始分批生成 ===")

    for i in range(target_voices):
        voice_config = VOICE_CONFIGS[i]
        text_assignments = voice_assignments[i]

        # 生成前确认
        print(f"\n准备生成第 {i + 1}/{target_voices} 种音色:")
        print(f"  音色: {voice_config['voice_code']} - {voice_config['name']}")
        print(f"  文本数量: {len(text_assignments)} 条")
        print(f"  预计字数: {sum(len(item['text']) for item in text_assignments)} 字")

        try:
            start_choice = input(f"是否开始生成 {voice_config['name']}？(y/n): ").strip().lower()
            if start_choice not in ['y', 'yes', '是']:
                print(f"跳过 {voice_config['name']}")
                continue
        except KeyboardInterrupt:
            print("\n生成被中断")
            break

        success = await generate_voice_batch(
            voice_config, text_assignments, config, i + 1, target_voices
        )

        # 生成完成后的状态报告
        if success:
            print(f"✅ {voice_config['name']} 生成完成")
        else:
            print(f"⚠️ {voice_config['name']} 生成失败，可能部分文件未完成")

        # 如果不是最后一个音色，询问是否继续
        if i < target_voices - 1:
            try:
                continue_choice = input(f"\n是否继续生成下一个音色？(y/n): ").strip().lower()
                if continue_choice not in ['y', 'yes', '是']:
                    print("生成被中断")
                    break
            except KeyboardInterrupt:
                print("\n生成被中断")
                break
        else:
            print(f"\n🎉 所有 {target_voices} 种音色生成完成！")

    # 10. 最终统计
    print(f"\n=== 最终统计 ===")

    total_generated = 0
    total_chars_generated = 0

    for i in range(target_voices):
        voice_config = VOICE_CONFIGS[i]
        existing = get_existing_files(config["OUTPUT_DIR"], voice_config["voice_code"])
        total_generated += existing

        # 计算实际字数
        actual_chars = voice_char_counts[i]
        total_chars_generated += actual_chars

        print(f"{voice_config['voice_code']} {voice_config['name']}: {existing} 个文件, {actual_chars} 字")

    print(f"\n总计生成文件: {total_generated}")
    print(f"总计字数: {total_chars_generated} 字")
    print(f"估算总时长: {total_chars_generated / 24000:.2f} 小时")

    print(f"\n输出目录: {config['OUTPUT_DIR']}")
    print(f"CSV记录: {csv_path}")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
