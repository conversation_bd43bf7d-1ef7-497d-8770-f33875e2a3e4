#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
华为云TTS多音色生成器
支持多种音色分布生成音频文件
"""

import io
import os
import sys
import json
import pycurl
import requests
import time
import csv
from datetime import datetime

# ==================== 配置参数 ====================
CONFIG = {
    # 华为云API配置
    "AK": "EC7BD181019626B182FC676E6EBEB83D",
    "SK": "C2C7CD527D0A8D3E77C8C6BD669CFED8",
    "AUTH_URL": "https://10.150.4.10:8080/auth/v3/generateToken",
    "TTS_URL": "https://10.150.4.10:8080/hivoice/v3/tts",
    
    # 音频参数配置
    "COMPRESS_RATE": 0,  # WAV格式使用0
    "SPEED": 100,
    "VOLUME": 140,
    "PITCH": 100,
    "SAMPLE_RATE": 16000, 
    
    # 文件配置
    "TEXT_FILE": "../shuffled_from_rank_random.txt",
    "PINYIN_FILE": "../shuffled_from_rank_random_pinyin.txt",
    "OUTPUT_DIR": "audio_output",
    "CSV_FILE": "huawei_multi_voice_audio_info.csv",
    "REQUEST_INTERVAL": 0.5,  # 请求间隔（秒）
    
    # 生成范围配置
    "START_INDEX": 3001,
    "END_INDEX": 5000,
    "TOTAL_COUNT": 2000
}

# 音色字典
VOICE_DICT = {
    "0": "小艺",
    "1": "yoyo", 
    "2": "童声",
    "3": "男声",
    "4": "新童声",
    "5": "冰糖IP",
    "6": "HMS女声---一菲",
    "7": "HMS男声---小忠",
    "8": "虎墩小镖师",
    "9": "丰田小悦",
    "10": "斑布猫",
    "11": "丰田粤语小美",
    "12": "僵小鱼",
    "101": "小艺朗读",
    "102": "闲聊女声",
    "202": "闲聊少女声",
    "402": "闲聊男声"
}

# 多音色配置 - 可以根据需要调整音色和数量分配
VOICE_CONFIGS = [
    {"voice_id": "0", "voice_name": "小艺", "start": 3001, "end": 3400, "count": 400},
    {"voice_id": "1", "voice_name": "yoyo", "start": 3401, "end": 3800, "count": 400},
    {"voice_id": "2", "voice_name": "童声", "start": 3801, "end": 4200, "count": 400},
    {"voice_id": "3", "voice_name": "男声", "start": 4201, "end": 4600, "count": 400},
    {"voice_id": "6", "voice_name": "HMS女声---一菲", "start": 4601, "end": 5000, "count": 400},
]

class HuaweiTTSMultiVoice:
    """华为云TTS多音色核心类"""
    
    def __init__(self, config):
        self.config = config
        self.token = None
        self.token_time = 0
        
    def get_token(self):
        """获取华为云访问令牌"""
        # 检查token是否需要刷新（每小时刷新一次）
        current_time = time.time()
        if self.token and (current_time - self.token_time) < 3600:
            return self.token
            
        print("正在获取华为云访问令牌...")
        
        url = self.config["AUTH_URL"]
        
        headers = {
            'Content-Type': "application/json",
            'sender': "APP",
            'receiver': "AS", 
            'deviceId': "Testbywanyahua",
            'sessionId': "testsff12345",
            'interactionId': "1",
            'locate': "CN",
            'appVersion': "11-0"
        }
        
        body = {
            'ak': self.config["AK"],
            'sk': self.config["SK"]
        }
        
        payload = json.dumps(body)
        
        try:
            response = requests.post(
                url=url,
                data=payload,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                print('✓ 成功获取访问令牌')
                data = response.text.split()[-2]
                token = json.loads(data)['accessToken']
                self.token = token
                self.token_time = current_time
                return token
            else:
                print(f'✗ 获取令牌失败: {response.status_code} - {response.text}')
                return None
        except Exception as e:
            print(f'✗ 获取令牌异常: {e}')
            return None
    
    def text_to_speech(self, text, person_id, index):
        """文本转语音"""
        if not self.token:
            self.token = self.get_token()
            if not self.token:
                return None
                
        url = self.config["TTS_URL"]
        request_id = f"request_{index}_{int(time.time())}"
        
        headers = [
            "messageName: text2audio",
            "deviceId: Testbywanyahua", 
            "appName: com.huawei.vassistant",
            "appVersion: 11.0.12.20",
            "deviceCategory: phone",
            f"token: Bearer {self.token}",
            "sender: APP",
            "receiver: TTS",
            "sessionId: testsff12345",
            "interactionId: 1",
            "locate: CN",
            "language: zh",
            f"person: {person_id}"
        ]
        
        body = {
            "session": {
                "appId": "",
                "devF": "GWQ**00307",
                "dialogId": 0,
                "interactionId": 0,
                "isExperiencePlan": False,
                "isFinish": False,
                "messageId": "06cff0ec-2430-4992-a54a-3ec6fe7c984f",
                "sender": "com.huawei.vassistant",
                "sessionId": "0f026d5a-83ab-4f9b-9929-df1c8365b2bf"
            },
            "contexts": [{
                "header": {
                    "namespace": "System",
                    "name": "Device"
                },
                "payload": {
                    "deviceName": "MHA",
                    "deviceType": "M300-AL00",
                    "osType": "android",
                    "osVersion": "1.0.0",
                    "sysVersion": "11.0",
                    "romVersion": "PE_TL10C00B560",
                    "mccmnc": "",
                    "appInfo": "tts1.0.0",
                    "net": "4G",
                    "deltaPlatformVer": "-1",
                    "minApiLevel": "0",
                    "deviceBrand": "HUAWEI",
                    "screenOrientation": "portrait",
                    "voiceKitVersion": "10.1.3.200",
                    "securityLevel": "unlocked"
                }
            }],
            "events": [{
                "header": {
                    "namespace": "TTS",
                    "name": "dospeak"
                },
                "payload": {
                    "token": "",
                    "device_id": "Testbywyh",
                    "request_id": request_id,
                    "text": text,
                    "device_type": 0,
                    "compress_rate": self.config["COMPRESS_RATE"],
                    "speed": self.config["SPEED"],
                    "volume": self.config["VOLUME"],
                    "pitch": self.config["PITCH"],
                    "person": int(person_id),
                    "style": "",
                    "sample_rate": self.config["SAMPLE_RATE"]
                }
            }]
        }
        
        payload = json.dumps(body)
        buf = io.BytesIO()
        
        try:
            curl = pycurl.Curl()
            curl.setopt(pycurl.WRITEFUNCTION, buf.write)
            curl.setopt(pycurl.URL, url)
            curl.setopt(pycurl.HTTPHEADER, headers)
            curl.setopt(pycurl.POSTFIELDS, payload)
            curl.perform()
            
            res_code = curl.getinfo(pycurl.HTTP_CODE)
            res = buf.getvalue()
            
            if res_code == 200:
                return res
            else:
                try:
                    error_info = json.loads(res)
                    print(f"  ✗ API错误: {error_info.get('message', '未知错误')}")
                except:
                    print(f"  ✗ HTTP错误 {res_code}: {res[:200]}")
                return None
        except Exception as e:
            print(f"  ✗ 合成异常: {e}")
            return None
        finally:
            buf.close()
            curl.close()

def load_text_files():
    """加载文本文件与拼音文件"""
    print("加载文本文件")
    # 读取文本文件
    try:
        with open(CONFIG["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(CONFIG["PINYIN_FILE"]):
        try:
            with open(CONFIG["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    return texts, pinyins

def get_existing_files(start_index, end_index):
    """获取指定范围内已存在的音频文件"""
    existing_indices = set()
    
    for i in range(start_index, end_index + 1):
        filename = f"Sc{i:07d}.wav"
        filepath = os.path.join(CONFIG["OUTPUT_DIR"], filename)
        if os.path.exists(filepath):
            existing_indices.add(i)
    
    return existing_indices

def generate_voice_batch(voice_config, texts, pinyins, tts_client, batch_num):
    """生成单个音色批次的音频"""
    voice_id = voice_config["voice_id"]
    voice_name = voice_config["voice_name"]
    start_index = voice_config["start"]
    end_index = voice_config["end"]
    
    print(f"\n=== 批次 {batch_num}: {voice_name} (ID: {voice_id}) ===")
    print(f"范围: {start_index} - {end_index}")
    
    # 检查已存在的文件
    existing_files = get_existing_files(start_index, end_index)
    target_count = end_index - start_index + 1
    
    if len(existing_files) == target_count:
        print(f"✓ 该批次已完成，跳过 ({len(existing_files)}/{target_count})")
        return True
    
    print(f"已存在: {len(existing_files)}/{target_count} 个文件")
    
    # 确定需要生成的文件
    indices_to_generate = []
    for i in range(start_index, end_index + 1):
        if i not in existing_files:
            indices_to_generate.append(i)
    
    if not indices_to_generate:
        print("✓ 无需生成新文件")
        return True
    
    print(f"需要生成: {len(indices_to_generate)} 个文件")
    
    # 统计信息
    success_count = 0
    error_count = 0
    start_time = time.time()
    
    # 打开CSV文件进行追加
    csv_path = os.path.join(CONFIG["OUTPUT_DIR"], CONFIG["CSV_FILE"])
    with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
        
        for i, index in enumerate(indices_to_generate, 1):
            # 获取文本和拼音
            text_index = index - 1  # 转换为数组索引
            text = texts[text_index]
            pinyin = pinyins[text_index] if text_index < len(pinyins) else ""
            audio_name = f"Sc{index:07d}"
            
            print(f"[{i}/{len(indices_to_generate)}] {audio_name}: {text[:30]}{'...' if len(text) > 30 else ''}")
            
            # 检查文件是否已存在（双重检查）
            audio_path = os.path.join(CONFIG["OUTPUT_DIR"], f"{audio_name}.wav")
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                continue
            
            # 生成音频（带重试）
            audio_data = None
            for retry in range(3):
                audio_data = tts_client.text_to_speech(text, voice_id, index)
                if audio_data:
                    break
                if retry < 2:
                    print(f"  重试 {retry + 1}/3...")
                    time.sleep(2)
                    # 重新获取令牌
                    tts_client.get_token()
            
            if audio_data:
                try:
                    # 保存音频文件
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)
                    
                    # 写入CSV记录（制表符分隔格式，包含音色信息）
                    csvfile.write(f"{audio_name}\tc\t{text}\t{pinyin}\t{voice_name}\n")
                    csvfile.flush()  # 立即写入文件
                    
                    print(f"  ✓ 成功 ({len(audio_data)} 字节)")
                    success_count += 1
                    
                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1
            
            # 请求间隔
            time.sleep(CONFIG["REQUEST_INTERVAL"])
            
            # 每50个显示进度
            if i % 50 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(indices_to_generate) - i) * avg_time
                print(f"  进度: {i}/{len(indices_to_generate)}, 预计剩余: {remaining/60:.1f}分钟")
    
    elapsed_time = time.time() - start_time
    print(f"\n批次 {batch_num} 完成:")
    print(f"  耗时: {elapsed_time/60:.1f}分钟")
    print(f"  成功: {success_count}")
    print(f"  失败: {error_count}")
    
    return error_count == 0

def main():
    """主函数"""
    print("=" * 60)
    print("华为云TTS多音色生成器")
    print("=" * 60)

    print("配置信息:")
    print(f"  文本文件: {CONFIG['TEXT_FILE']}")
    print(f"  拼音文件: {CONFIG['PINYIN_FILE']}")
    print(f"  输出目录: {CONFIG['OUTPUT_DIR']}")
    print(f"  范围: {CONFIG['START_INDEX']} - {CONFIG['END_INDEX']}")
    print(f"  总数: {CONFIG['TOTAL_COUNT']} 个文件")
    print(f"  请求间隔: {CONFIG['REQUEST_INTERVAL']}秒")

    print(f"\n音色配置:")
    for i, voice in enumerate(VOICE_CONFIGS, 1):
        print(f"  {i}. {voice['voice_name']} (ID: {voice['voice_id']}): {voice['start']}-{voice['end']} ({voice['count']}条)")

    print("=" * 60)

    # 1. 检查必要文件
    if not os.path.exists(CONFIG['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {CONFIG['TEXT_FILE']}")
        return

    if not os.path.exists(CONFIG['PINYIN_FILE']):
        print(f"⚠️ 拼音文件不存在: {CONFIG['PINYIN_FILE']}")
        print("将继续处理，但拼音字段将为空")

    # 2. 创建输出目录
    os.makedirs(CONFIG["OUTPUT_DIR"], exist_ok=True)

    # 3. 加载文本和拼音文件
    print(f"\n=== 加载文本文件 ===")
    texts, pinyins = load_text_files()
    if texts is None:
        return

    # 检查文件行数是否足够
    if len(texts) < CONFIG['END_INDEX']:
        print(f"✗ 文本文件行数不足: 需要{CONFIG['END_INDEX']}行，实际{len(texts)}行")
        return

    # 4. 初始化CSV文件
    csv_path = os.path.join(CONFIG["OUTPUT_DIR"], CONFIG["CSV_FILE"])
    if not os.path.exists(csv_path):
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            csvfile.write('音频名\t类型\t文本\t注音1\t音色\n')
        print(f"✓ 创建CSV文件: {csv_path}")

    # 5. 创建TTS客户端
    tts_client = HuaweiTTSMultiVoice(CONFIG)

    # 6. 确认生成
    try:
        confirm = input(f"是否开始多音色音频生成？(y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("已取消生成")
            return
    except KeyboardInterrupt:
        print("\n程序被中断")
        return

    # 7. 开始分批生成
    print(f"\n=== 开始多音色分批生成 ===")

    for i, voice_config in enumerate(VOICE_CONFIGS, 1):
        success = generate_voice_batch(voice_config, texts, pinyins, tts_client, i)
        if not success:
            print("生成被中断或失败")
            break

    # 8. 最终统计
    print(f"\n=== 最终统计 ===")

    total_generated = 0
    for voice in VOICE_CONFIGS:
        existing = get_existing_files(voice["start"], voice["end"])
        generated = len(existing)
        total_generated += generated
        target = voice["count"]
        status = "✓完成" if generated == target else f"{generated}/{target}"
        print(f"{voice['voice_name']}: {status}")

    print(f"\n目标范围({CONFIG['START_INDEX']}-{CONFIG['END_INDEX']})内文件: {total_generated}/{CONFIG['TOTAL_COUNT']}")

    if total_generated == CONFIG['TOTAL_COUNT']:
        print("🎉 所有目标文件生成完成！")
    else:
        missing = CONFIG['TOTAL_COUNT'] - total_generated
        print(f"⚠️ 还缺少 {missing} 个文件")

    print(f"\n输出目录: {CONFIG['OUTPUT_DIR']}")
    print(f"CSV记录: {csv_path}")
    print("=" * 60)

if __name__ == "__main__":
    main()
