#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
百度TTS多音色批量生成器
每种音色生成1小时音频（18000-24000字），文本长度从2-30均匀分布
"""

import requests
import csv
import time
import os
import glob
import random
from pathlib import Path
import urllib.parse
import pandas as pd
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings('ignore')

# 百度API配置
API_KEY = "CUF1IaEMZhJ8vhZvBCFo0IVb"
SECRET_KEY = "zvwuWL1kys24jwVd8wh9CymKX5RFRM7G"

# 百度TTS配置
BAIDU_CONFIG = {
    # 文件配置
    "TEXT_FILE": "shuffled_from_rank_random.txt",
    "PINYIN_FILE": "shuffled_from_rank_random_pinyin.txt",
    "OUTPUT_DIR": "audio_output_baidu_multi",
    "CSV_FILE": "baidu_multi_voice_log.csv",
    "REQUEST_INTERVAL": 0.2,  # 请求间隔（秒）
    
    # 生成配置
    "MIN_CHARS_PER_VOICE": 18000,  # 每种音色最少字数
    "MAX_CHARS_PER_VOICE": 24000,  # 每种音色最多字数
    "TARGET_CHARS_PER_VOICE": 21000,  # 每种音色目标字数
}

# 百度音色配置 - 62种音色
VOICE_CONFIGS = [
    {"voice_code": "B001", "voice_id": "0", "name": "度小美-标准女主播"},
    {"voice_code": "B002", "voice_id": "1", "name": "度小宇-亲切男声"},
    {"voice_code": "B003", "voice_id": "3", "name": "度逍遥-情感男声"},
    {"voice_code": "B004", "voice_id": "4", "name": "度丫丫-童声"},
    {"voice_code": "B005", "voice_id": "5", "name": "度小娇-成熟女主播"},
    {"voice_code": "B006", "voice_id": "5003", "name": "度逍遥-情感男声"},
    {"voice_code": "B007", "voice_id": "5118", "name": "度小鹿-甜美女声"},
    {"voice_code": "B008", "voice_id": "106", "name": "度博文-专业男主播"},
    {"voice_code": "B009", "voice_id": "103", "name": "度米朵-可爱童声"},
    {"voice_code": "B010", "voice_id": "110", "name": "音色110"},
    {"voice_code": "B011", "voice_id": "111", "name": "音色111"},
    {"voice_code": "B012", "voice_id": "4003", "name": "音色4003"},
    {"voice_code": "B013", "voice_id": "4106", "name": "音色4106"},
    {"voice_code": "B014", "voice_id": "4115", "name": "音色4115"},
    {"voice_code": "B015", "voice_id": "5147", "name": "音色5147"},
    {"voice_code": "B016", "voice_id": "5976", "name": "音色5976"},
    {"voice_code": "B017", "voice_id": "5971", "name": "音色5971"},
    {"voice_code": "B018", "voice_id": "4164", "name": "音色4164"},
    {"voice_code": "B019", "voice_id": "4176", "name": "音色4176"},
    {"voice_code": "B020", "voice_id": "4259", "name": "音色4259"},
    {"voice_code": "B021", "voice_id": "4119", "name": "音色4119"},
    {"voice_code": "B022", "voice_id": "4105", "name": "音色4105"},
    {"voice_code": "B023", "voice_id": "4117", "name": "音色4117"},
    {"voice_code": "B024", "voice_id": "4288", "name": "音色4288"},
    {"voice_code": "B025", "voice_id": "4192", "name": "音色4192"},
    {"voice_code": "B026", "voice_id": "4100", "name": "音色4100"},
    {"voice_code": "B027", "voice_id": "4103", "name": "音色4103"},
    {"voice_code": "B028", "voice_id": "4144", "name": "音色4144"},
    {"voice_code": "B029", "voice_id": "4278", "name": "音色4278"},
    {"voice_code": "B030", "voice_id": "4143", "name": "音色4143"},
    {"voice_code": "B031", "voice_id": "4140", "name": "音色4140"},
    {"voice_code": "B032", "voice_id": "4129", "name": "音色4129"},
    {"voice_code": "B033", "voice_id": "4149", "name": "音色4149"},
    {"voice_code": "B034", "voice_id": "4254", "name": "音色4254"},
    {"voice_code": "B035", "voice_id": "4206", "name": "音色4206"},
    {"voice_code": "B036", "voice_id": "4147", "name": "音色4147"},
    {"voice_code": "B037", "voice_id": "4141", "name": "音色4141"},
    {"voice_code": "B038", "voice_id": "4226", "name": "音色4226"},
    {"voice_code": "B039", "voice_id": "6205", "name": "音色6205"},
    {"voice_code": "B040", "voice_id": "6221", "name": "音色6221"},
    {"voice_code": "B041", "voice_id": "6546", "name": "音色6546"},
    {"voice_code": "B042", "voice_id": "6602", "name": "音色6602"},
    {"voice_code": "B043", "voice_id": "6562", "name": "音色6562"},
    {"voice_code": "B044", "voice_id": "6543", "name": "音色6543"},
    {"voice_code": "B045", "voice_id": "6747", "name": "音色6747"},
    {"voice_code": "B046", "voice_id": "6748", "name": "音色6748"},
    {"voice_code": "B047", "voice_id": "6746", "name": "音色6746"},
    {"voice_code": "B048", "voice_id": "6644", "name": "音色6644"},
    {"voice_code": "B049", "voice_id": "4148", "name": "音色4148"},
    {"voice_code": "B050", "voice_id": "4277", "name": "音色4277"},
    {"voice_code": "B051", "voice_id": "4114", "name": "音色4114"},
    {"voice_code": "B052", "voice_id": "5153", "name": "音色5153"},
    {"voice_code": "B053", "voice_id": "6561", "name": "音色6561"},
    {"voice_code": "B054", "voice_id": "4179", "name": "音色4179"},
    {"voice_code": "B055", "voice_id": "4146", "name": "音色4146"},
    {"voice_code": "B056", "voice_id": "6567", "name": "音色6567"},
    {"voice_code": "B057", "voice_id": "4156", "name": "音色4156"},
    {"voice_code": "B058", "voice_id": "4189", "name": "音色4189"},
    {"voice_code": "B059", "voice_id": "4194", "name": "音色4194"},
    {"voice_code": "B060", "voice_id": "4193", "name": "音色4193"},
    {"voice_code": "B061", "voice_id": "4195", "name": "音色4195"},
    {"voice_code": "B062", "voice_id": "4196", "name": "音色4196"},
]

print("✅ 配置加载完成")

class BaiduTTSMultiVoice:
    """百度TTS多音色批量生成器"""
    
    def __init__(self, api_key=API_KEY, secret_key=SECRET_KEY):
        self.api_key = api_key
        self.secret_key = secret_key
        self.access_token = None
        self.success_count = 0
        self.error_count = 0

    def get_access_token(self):
        """获取百度API访问令牌"""
        if self.access_token:
            return self.access_token
            
        url = "https://aip.baidubce.com/oauth/2.0/token"
        params = {
            "grant_type": "client_credentials",
            "client_id": self.api_key,
            "client_secret": self.secret_key
        }

        try:
            response = requests.post(url, params=params, verify=False)
            result = response.json()
            if "access_token" in result:
                self.access_token = result["access_token"]
                print(f"✓ 成功获取访问令牌: {self.access_token[:20]}...")
                return self.access_token
            else:
                print(f"✗ 获取access_token失败: {result}")
                return None
        except Exception as e:
            print(f"✗ 获取access_token异常: {e}")
            return None

    def text_to_speech(self, text, voice_id):
        """将文本转换为语音"""
        if not self.get_access_token():
            return None

        url = "https://tsn.baidu.com/text2audio"

        # 构建请求参数
        payload = {
            'tok': self.access_token,
            'cuid': 'cYSumKzAEzMjEKm2A0oQ9gqHm1rnf0xg',
            'ctp': '1',
            'lan': 'zh',
            'spd': '5',  # 语速
            'pit': '5',  # 音调
            'vol': '5',  # 音量
            'per': str(voice_id),  # 发音人
            'aue': '6',  # 音频格式 (3=mp3, 6=wav)
            'tex': text
        }

        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': '*/*'
        }

        try:
            # 将参数编码为URL格式
            data = urllib.parse.urlencode(payload).encode('utf-8')

            response = requests.post(url, headers=headers, data=data, verify=False)

            # 检查响应
            if response.status_code == 200:
                # 检查是否返回的是音频数据
                content_type = response.headers.get('content-type', '')
                if 'audio' in content_type:
                    self.success_count += 1
                    return response.content
                else:
                    # 可能是错误信息
                    try:
                        error_info = response.json()
                        print(f"  ✗ TTS API错误: {error_info}")
                    except:
                        print(f"  ✗ TTS API返回非音频内容: {response.text[:200]}")
                    self.error_count += 1
                    return None
            else:
                print(f"  ✗ HTTP错误: {response.status_code}")
                self.error_count += 1
                return None

        except Exception as e:
            print(f"  ✗ TTS转换异常: {e}")
            self.error_count += 1
            return None

def load_text_files(config):
    """加载文本和拼音文件"""
    print("=== 加载文本文件 ===")
    
    # 读取文本文件
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    return texts, pinyins

def select_texts_for_target_chars(texts, target_chars):
    """
    根据目标字数选择文本，确保文本长度从2-30字符均匀分布
    
    Args:
        texts: 文本列表
        target_chars: 目标字符数
    
    Returns:
        selected_texts: 选中的文本列表
        selected_indices: 选中文本的原始索引
        current_chars: 实际选中的字符数
    """
    # 按文本长度分组
    length_groups = {}
    for i, text in enumerate(texts):
        text_length = len(text)
        # 只考虑长度在2-30之间的文本
        if 2 <= text_length <= 30:
            if text_length not in length_groups:
                length_groups[text_length] = []
            length_groups[text_length].append((i, text))
    
    print(f"  文本长度分布: {sorted(length_groups.keys())}")
    print(f"  各长度文本数量: {[(length, len(group)) for length, group in sorted(length_groups.items())]}")
    
    # 计算每个长度需要的文本数量（均匀分布）
    available_lengths = sorted(length_groups.keys())
    if not available_lengths:
        print("  ⚠️ 没有找到长度在2-30之间的文本")
        return [], [], 0
    
    # 估算每个长度大约需要多少个文本
    total_length_categories = len(available_lengths)
    avg_chars_per_category = target_chars // total_length_categories
    
    selected_texts = []
    selected_indices = []
    current_chars = 0
    length_stats = {}
    
    # 为每个长度类别选择文本
    for text_length in available_lengths:
        group = length_groups[text_length]
        
        # 计算这个长度需要多少个文本
        target_count_for_length = max(1, avg_chars_per_category // text_length)
        actual_count = min(target_count_for_length, len(group))
        
        # 随机选择这个长度的文本
        selected_from_group = random.sample(group, actual_count)
        
        for idx, text in selected_from_group:
            if current_chars + text_length <= target_chars:
                selected_texts.append(text)
                selected_indices.append(idx)
                current_chars += text_length
                
                # 统计
                if text_length not in length_stats:
                    length_stats[text_length] = 0
                length_stats[text_length] += 1
            else:
                break
        
        # 如果已经达到目标字数，停止
        if current_chars >= target_chars * 0.95:  # 达到95%就停止
            break
    
    # 如果还没达到目标字数，随机补充一些文本
    if current_chars < target_chars * 0.9:
        remaining_chars = target_chars - current_chars
        all_remaining = []
        
        for length, group in length_groups.items():
            for idx, text in group:
                if idx not in selected_indices and len(text) <= remaining_chars:
                    all_remaining.append((idx, text, len(text)))
        
        # 按长度排序，优先选择能填满剩余空间的文本
        all_remaining.sort(key=lambda x: abs(x[2] - remaining_chars))
        
        for idx, text, text_len in all_remaining:
            if current_chars + text_len <= target_chars:
                selected_texts.append(text)
                selected_indices.append(idx)
                current_chars += text_len
                
                if text_len not in length_stats:
                    length_stats[text_len] = 0
                length_stats[text_len] += 1
                
                if current_chars >= target_chars * 0.95:
                    break
    
    # 显示长度分布统计
    print(f"  选中文本长度分布: {dict(sorted(length_stats.items()))}")
    print(f"  长度覆盖范围: {min(length_stats.keys()) if length_stats else 0}-{max(length_stats.keys()) if length_stats else 0}")
    print(f"  总文本数: {len(selected_texts)}, 总字数: {current_chars}")
    
    return selected_texts, selected_indices, current_chars

def get_existing_files(output_dir, voice_code):
    """获取指定音色已存在的文件"""
    pattern = os.path.join(output_dir, f"Sc{voice_code}*.wav")
    audio_files = glob.glob(pattern)
    return len(audio_files)

def generate_voice_batch_baidu(voice_config, texts, pinyins, config, batch_num):
    """使用百度TTS生成单个音色批次的音频"""
    voice_id = voice_config["voice_id"]
    voice_name = voice_config["name"]
    voice_code = voice_config["voice_code"]
    target_chars = config["TARGET_CHARS_PER_VOICE"]

    print(f"\n=== 批次 {batch_num}: {voice_name} ({voice_code}) ===")
    print(f"音色ID: {voice_id}")
    print(f"目标字数: {target_chars} 字")

    # 检查已存在的文件
    existing_count = get_existing_files(config["OUTPUT_DIR"], voice_code)
    if existing_count > 0:
        print(f"已存在 {existing_count} 个文件，继续生成...")

    # 选择文本达到目标字数
    selected_texts, selected_indices, actual_chars = select_texts_for_target_chars(texts, target_chars)

    print(f"选择文本: {len(selected_texts)} 条")
    print(f"实际字数: {actual_chars} 字")

    if not selected_texts:
        print("✗ 没有可用的文本")
        return False

    # 创建TTS对象
    tts = BaiduTTSMultiVoice()

    # 统计信息
    success_count = 0
    error_count = 0
    start_time = time.time()

    # 打开CSV文件进行追加
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    csv_exists = os.path.exists(csv_path)

    with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
        # 使用制表符分隔的CSV写入器
        writer = csv.writer(csvfile, delimiter='\t')

        # 写入标题行（如果文件不存在）
        if not csv_exists:
            writer.writerow(['音频名', '类型', '文本', '注音1'])

        for i, (text_idx, text) in enumerate(zip(selected_indices, selected_texts), 1):
            pinyin = pinyins[text_idx] if text_idx < len(pinyins) else ""

            # 音频文件名格式：Sc + 音色代码 + 序号
            audio_name = f"Sc{voice_code}{i:07d}"
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")

            print(f"[{i}/{len(selected_texts)}] {audio_name}: {text[:30]}{'...' if len(text) > 30 else ''}")

            # 检查文件是否已存在
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                success_count += 1
                continue

            # 生成音频（使用百度TTS，带重试）
            audio_data = None
            for retry in range(3):
                audio_data = tts.text_to_speech(text, voice_id)
                if audio_data:
                    break
                if retry < 2:
                    print(f"  重试 {retry + 1}/3...")
                    time.sleep(2)

            if audio_data:
                try:
                    # 保存音频文件
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)

                    # 写入CSV记录
                    writer.writerow([audio_name, 'c', text, pinyin])
                    csvfile.flush()

                    print(f"  ✓ 成功 ({len(audio_data)} 字节)")
                    success_count += 1

                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1

            # 请求间隔
            time.sleep(config["REQUEST_INTERVAL"])

            # 每100个显示进度
            if i % 100 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(selected_texts) - i) * avg_time
                success_rate = (tts.success_count / (tts.success_count + tts.error_count) * 100) if (tts.success_count + tts.error_count) > 0 else 0
                print(f"  进度: {i}/{len(selected_texts)}, 预计剩余: {remaining/60:.1f}分钟, 成功率: {success_rate:.1f}%")

    elapsed_time = time.time() - start_time
    print(f"\n批次 {batch_num} 完成:")
    print(f"  耗时: {elapsed_time/60:.1f}分钟")
    print(f"  成功: {success_count}")
    print(f"  失败: {error_count}")
    print(f"  实际生成字数: {actual_chars}")
    print(f"  成功率: {(success_count/(success_count+error_count)*100):.1f}%" if success_count+error_count > 0 else "0%")

    return error_count == 0

def main():
    """主函数 - 百度TTS多音色版"""
    print("=" * 60)
    print("百度TTS多音色批量生成器")
    print("每种音色生成1小时音频，文本长度2-30均匀分布")
    print("=" * 60)

    config = BAIDU_CONFIG

    print("配置信息:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  拼音文件: {config['PINYIN_FILE']}")
    print(f"  输出目录: {config['OUTPUT_DIR']}")
    print(f"  目标字数: {config['TARGET_CHARS_PER_VOICE']} 字/音色")
    print(f"  请求间隔: {config['REQUEST_INTERVAL']}秒")

    print(f"\n音色配置 (前10个):")
    for i, voice in enumerate(VOICE_CONFIGS[:10], 1):
        print(f"  {i}. {voice['name']} ({voice['voice_code']}) - ID: {voice['voice_id']}")
    print(f"  ... 总共 {len(VOICE_CONFIGS)} 种音色")

    # 余额估算
    total_chars = len(VOICE_CONFIGS) * config['TARGET_CHARS_PER_VOICE']
    estimated_cost = total_chars * 0.0001  # 按0.1元/千字符估算
    print(f"\n💰 费用估算:")
    print(f"  总字符数: {total_chars:,} 字符")
    print(f"  预估费用: {estimated_cost:.2f} 元 (按0.1元/千字符)")
    print(f"  您的余额: 40000+ 元 (完全足够)")

    print("=" * 60)

    # 1. 检查必要文件
    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return

    # 2. 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)

    # 3. 加载文本和拼音文件
    print(f"\n=== 加载文本文件 ===")
    texts, pinyins = load_text_files(config)
    if texts is None:
        return

    # 计算总字数
    total_chars = sum(len(text) for text in texts)
    print(f"文本总字数: {total_chars} 字")

    # 4. 初始化CSV文件
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    if not os.path.exists(csv_path):
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile, delimiter='\t')
            writer.writerow(['音频名', '类型', '文本', '注音1'])
        print(f"✓ 创建CSV文件: {csv_path}")

    # 5. 检查现有文件
    print(f"\n=== 检查现有文件 ===")
    total_existing = 0
    for voice in VOICE_CONFIGS:
        existing = get_existing_files(config["OUTPUT_DIR"], voice["voice_code"])
        total_existing += existing
        if existing > 0:
            print(f"  {voice['name']}: {existing} 个文件")

    if total_existing > 0:
        print(f"总计已存在: {total_existing} 个文件")

    # 6. 开始分批生成
    print(f"\n=== 开始分批生成 ===")

    for i, voice_config in enumerate(VOICE_CONFIGS, 1):
        success = generate_voice_batch_baidu(voice_config, texts, pinyins, config, i)
        if not success:
            print(f"⚠️ {voice_config['name']}生成失败，可能部分文件未完成")

    # 7. 最终统计
    print(f"\n=== 最终统计 ===")

    total_generated = 0
    for voice in VOICE_CONFIGS:
        existing = get_existing_files(config["OUTPUT_DIR"], voice["voice_code"])
        total_generated += existing
        print(f"{voice['name']}: {existing} 个文件")

    print(f"\n总计生成文件: {total_generated}")
    print(f"输出目录: {config['OUTPUT_DIR']}")
    print(f"CSV记录: {csv_path}")
    print("=" * 60)

print("✅ 百度TTS多音色版准备完成")

if __name__ == "__main__":
    main()
