# ============================================================================
# 豆包TTS HTTP API多音色版本
# 基于您提供的HTTP API方法，修改为多音色批量生成
# 复制此代码到您的TTS.ipynb中使用
# ============================================================================

"""
豆包语音合成大模型 - HTTP API多音色版
使用HTTP API替代WebSocket，避免1007错误
多音色批量生成，每种音色生成指定数量的音频
"""

import requests
import base64
import time
import csv
import os
import uuid
import json
import glob

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print("✅ 环境设置完成 - HTTP API多音色版")

# ==================== 配置区域 ====================
DOUYIN_CONFIG = {
    # API配置 - 使用您的新配置
    "APPID": "9862368305",
    "ACCESS_TOKEN": "AKLTNmQ3YmQ0MmRjZThkNDE0OGI0NWJkZjVlZmZiYWRlOTk",  # AccesskeyID
    "CLUSTER": "volcano_tts",
    
    # 音频格式配置
    "ENCODING": "wav",
    "SAMPLE_RATE": 16000,
    "SPEED_RATIO": 1.0,
    "VOLUME_RATIO": 1.0,
    "PITCH_RATIO": 1.0,
    "SILENCE_DURATION": 200,
    
    # 文件配置
    "TEXT_FILE": "shuffled_text_dedup.txt",
    "PINYIN_FILE": "shuffled_text_dedup_pinyin.txt",
    "OUTPUT_DIR": "audio_output_http_multi",
    "CSV_FILE": "douyin_http_multi_voice_log.csv",
    "REQUEST_INTERVAL": 0.5,  # HTTP请求间隔
    
    # 生成配置
    "TARGET_CHARS_PER_VOICE": 21000,  # 每种音色目标字数
}

# 豆包音色配置 - 50种音色
VOICE_CONFIGS = [
    {"voice_code": "D001", "voice_type": "zh_female_wanqudashu_moon_bigtts", "name": "婉曲大叔"},
    {"voice_code": "D002", "voice_type": "zh_female_daimengchuanmei_moon_bigtts", "name": "黛梦传媒"},
    {"voice_code": "D003", "voice_type": "zh_male_guozhoudege_moon_bigtts", "name": "国州德哥"},
    {"voice_code": "D004", "voice_type": "zh_male_beijingxiaoye_moon_bigtts", "name": "北京小爷"},
    {"voice_code": "D005", "voice_type": "zh_male_shaonianzixin_moon_bigtts", "name": "少年子心"},
    {"voice_code": "D006", "voice_type": "zh_female_meilinvyou_moon_bigtts", "name": "美丽女友"},
    {"voice_code": "D007", "voice_type": "zh_male_shenyeboke_moon_bigtts", "name": "深夜播客"},
    {"voice_code": "D008", "voice_type": "zh_female_sajiaonvyou_moon_bigtts", "name": "撒娇女友"},
    {"voice_code": "D009", "voice_type": "zh_female_yuanqinvyou_moon_bigtts", "name": "远亲女友"},
    {"voice_code": "D010", "voice_type": "zh_male_haoyuxiaoge_moon_bigtts", "name": "豪宇小哥"},
    {"voice_code": "D011", "voice_type": "zh_male_guangxiyuanzhou_moon_bigtts", "name": "广西远州"},
    {"voice_code": "D012", "voice_type": "zh_female_meituojieer_moon_bigtts", "name": "美托姐儿"},
    {"voice_code": "D013", "voice_type": "zh_male_yuzhouzixuan_moon_bigtts", "name": "宇宙子轩"},
    {"voice_code": "D014", "voice_type": "zh_female_linjianvhai_moon_bigtts", "name": "邻家女孩"},
    {"voice_code": "D015", "voice_type": "zh_female_gaolengyujie_moon_bigtts", "name": "高冷御姐"},
    {"voice_code": "D016", "voice_type": "zh_male_yuanboxiaoshu_moon_bigtts", "name": "渊博小叔"},
    {"voice_code": "D017", "voice_type": "zh_male_yangguangqingnian_moon_bigtts", "name": "阳光青年"},
    {"voice_code": "D018", "voice_type": "zh_male_aojiaobazong_moon_bigtts", "name": "傲娇霸总"},
    {"voice_code": "D019", "voice_type": "zh_male_jingqiangkanye_moon_bigtts", "name": "京腔侃爷"},
    {"voice_code": "D020", "voice_type": "zh_female_shuangkuaisisi_moon_bigtts", "name": "爽快思思"},
]

# API配置
HOST = "openspeech.bytedance.com"
API_URL = f"https://{HOST}/api/v1/tts"

print("✅ 配置加载完成")

def get_cluster(voice_type):
    """获取集群名称"""
    if voice_type.startswith("ICL_"):
        return "volcano_icl"
    return "volcano_tts"

class DouyinTTSHTTPMultiVoice:
    """豆包TTS HTTP API多音色核心类"""
    
    def __init__(self, config):
        self.config = config
        self.headers = {"Authorization": f"Bearer;{config['ACCESS_TOKEN']}"}
        self.success_count = 0
        self.error_count = 0
        
    def synthesize_text(self, text, voice_type):
        """使用HTTP API合成单个文本"""
        # 确定集群
        cluster = get_cluster(voice_type)
        
        request_json = {
            "app": {
                "appid": self.config["APPID"],
                "token": self.config["ACCESS_TOKEN"],  # 使用实际token
                "cluster": cluster
            },
            "user": {
                "uid": str(uuid.uuid4())  # 使用随机UUID
            },
            "audio": {
                "voice_type": voice_type,
                "encoding": self.config["ENCODING"],
                "sample_rate": self.config["SAMPLE_RATE"],
                "speed_ratio": self.config["SPEED_RATIO"],
                "volume_ratio": self.config["VOLUME_RATIO"],
                "pitch_ratio": self.config["PITCH_RATIO"],
            },
            "request": {
                "reqid": str(uuid.uuid4()),
                "text": text,
                "text_type": "plain",
                "operation": "query",
                "with_frontend": 1,
                "frontend_type": "unitTson"
            }
        }
        
        try:
            resp = requests.post(API_URL, json.dumps(request_json), headers=self.headers, timeout=30)
            
            if resp.status_code == 200:
                resp_data = resp.json()
                if "data" in resp_data:
                    audio_data = base64.b64decode(resp_data["data"])
                    self.success_count += 1
                    return audio_data
                else:
                    print(f"  ✗ API响应无数据: {resp_data}")
                    self.error_count += 1
                    return None
            else:
                print(f"  ✗ HTTP错误 {resp.status_code}: {resp.text}")
                self.error_count += 1
                return None
                
        except requests.exceptions.Timeout:
            print(f"  ✗ 请求超时")
            self.error_count += 1
            return None
        except Exception as e:
            print(f"  ✗ 请求异常: {e}")
            self.error_count += 1
            return None

def load_text_files(config):
    """加载文本和拼音文件"""
    print("=== 加载文本文件 ===")
    
    # 读取文本文件
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    return texts, pinyins

def select_texts_for_target_chars(texts, target_chars):
    """根据目标字数选择文本"""
    selected_texts = []
    selected_indices = []
    current_chars = 0
    
    for i, text in enumerate(texts):
        text_length = len(text)
        if current_chars + text_length <= target_chars:
            selected_texts.append(text)
            selected_indices.append(i)
            current_chars += text_length
        else:
            # 如果加上这个文本会超过目标字数，检查是否接近目标
            if target_chars - current_chars > text_length // 2:
                selected_texts.append(text)
                selected_indices.append(i)
                current_chars += text_length
            break
    
    return selected_texts, selected_indices, current_chars

def get_existing_files(output_dir, voice_code):
    """获取指定音色已存在的文件"""
    pattern = os.path.join(output_dir, f"Sc{voice_code}*.wav")
    audio_files = glob.glob(pattern)
    return len(audio_files)

def generate_voice_batch_http(voice_config, texts, pinyins, config, batch_num):
    """使用HTTP API生成单个音色批次的音频"""
    voice_type = voice_config["voice_type"]
    voice_name = voice_config["name"]
    voice_code = voice_config["voice_code"]
    target_chars = config["TARGET_CHARS_PER_VOICE"]
    
    print(f"\n=== 批次 {batch_num}: {voice_name} ({voice_code}) ===")
    print(f"音色类型: {voice_type}")
    print(f"目标字数: {target_chars} 字")
    
    # 检查已存在的文件
    existing_count = get_existing_files(config["OUTPUT_DIR"], voice_code)
    if existing_count > 0:
        print(f"已存在 {existing_count} 个文件，继续生成...")
    
    # 选择文本达到目标字数
    selected_texts, selected_indices, actual_chars = select_texts_for_target_chars(texts, target_chars)
    
    print(f"选择文本: {len(selected_texts)} 条")
    print(f"实际字数: {actual_chars} 字")
    
    if not selected_texts:
        print("✗ 没有可用的文本")
        return False
    
    # 创建TTS对象
    tts = DouyinTTSHTTPMultiVoice(config)
    
    # 统计信息
    success_count = 0
    error_count = 0
    start_time = time.time()
    
    # 打开CSV文件进行追加
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    csv_exists = os.path.exists(csv_path)
    
    with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
        # 使用制表符分隔的CSV写入器
        writer = csv.writer(csvfile, delimiter='\t')
        
        # 写入标题行（如果文件不存在）
        if not csv_exists:
            writer.writerow(['音频名', '类型', '文本', '注音', '音色'])
        
        for i, (text_idx, text) in enumerate(zip(selected_indices, selected_texts), 1):
            pinyin = pinyins[text_idx] if text_idx < len(pinyins) else ""
            
            # 音频文件名格式：Sc + 音色代码 + 序号
            audio_name = f"Sc{voice_code}{i:07d}"
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")
            
            print(f"[{i}/{len(selected_texts)}] {audio_name}: {text[:30]}{'...' if len(text) > 30 else ''}")
            
            # 检查文件是否已存在
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                success_count += 1
                continue
            
            # 生成音频（使用HTTP API，带重试）
            audio_data = None
            for retry in range(3):
                audio_data = tts.synthesize_text(text, voice_type)
                if audio_data:
                    break
                if retry < 2:
                    print(f"  重试 {retry + 1}/3...")
                    time.sleep(2)
            
            if audio_data:
                try:
                    # 保存音频文件
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)
                    
                    # 写入CSV记录
                    writer.writerow([audio_name, 'c', text, pinyin, voice_name])
                    csvfile.flush()
                    
                    print(f"  ✓ 成功 ({len(audio_data)} 字节)")
                    success_count += 1
                    
                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1
            
            # 请求间隔
            time.sleep(config["REQUEST_INTERVAL"])
            
            # 每10个显示进度
            if i % 10 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(selected_texts) - i) * avg_time
                success_rate = (tts.success_count / (tts.success_count + tts.error_count) * 100) if (tts.success_count + tts.error_count) > 0 else 0
                print(f"  进度: {i}/{len(selected_texts)}, 预计剩余: {remaining/60:.1f}分钟, 成功率: {success_rate:.1f}%")
    
    elapsed_time = time.time() - start_time
    print(f"\n批次 {batch_num} 完成:")
    print(f"  耗时: {elapsed_time/60:.1f}分钟")
    print(f"  成功: {success_count}")
    print(f"  失败: {error_count}")
    print(f"  实际生成字数: {actual_chars}")
    print(f"  成功率: {(success_count/(success_count+error_count)*100):.1f}%" if success_count+error_count > 0 else "0%")
    
    return error_count == 0

def main():
    """主函数 - HTTP API多音色版"""
    print("=" * 60)
    print("豆包语音合成大模型 - HTTP API多音色版")
    print("使用HTTP API替代WebSocket，避免1007错误")
    print("=" * 60)

    config = DOUYIN_CONFIG

    print("配置信息:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  拼音文件: {config['PINYIN_FILE']}")
    print(f"  输出目录: {config['OUTPUT_DIR']}")
    print(f"  目标字数: {config['TARGET_CHARS_PER_VOICE']} 字/音色")
    print(f"  HTTP API端点: {API_URL}")
    print(f"  请求间隔: {config['REQUEST_INTERVAL']}秒")

    print(f"\n音色配置 (前10个):")
    for i, voice in enumerate(VOICE_CONFIGS[:10], 1):
        print(f"  {i}. {voice['name']} ({voice['voice_code']}) - {voice['voice_type']}")
    print(f"  ... 总共 {len(VOICE_CONFIGS)} 种音色")

    print("=" * 60)

    # 1. 检查必要文件
    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return

    # 2. 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)

    # 3. 加载文本和拼音文件
    print(f"\n=== 加载文本文件 ===")
    texts, pinyins = load_text_files(config)
    if texts is None:
        return

    # 计算总字数
    total_chars = sum(len(text) for text in texts)
    print(f"文本总字数: {total_chars} 字")

    # 4. 初始化CSV文件
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    if not os.path.exists(csv_path):
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile, delimiter='\t')
            writer.writerow(['音频名', '类型', '文本', '注音', '音色'])
        print(f"✓ 创建CSV文件: {csv_path}")

    # 5. 检查现有文件
    print(f"\n=== 检查现有文件 ===")
    total_existing = 0
    for voice in VOICE_CONFIGS:
        existing = get_existing_files(config["OUTPUT_DIR"], voice["voice_code"])
        total_existing += existing
        if existing > 0:
            print(f"  {voice['name']}: {existing} 个文件")

    if total_existing > 0:
        print(f"总计已存在: {total_existing} 个文件")

    # 6. 开始分批生成
    print(f"\n=== 开始分批生成 ===")

    for i, voice_config in enumerate(VOICE_CONFIGS, 1):
        success = generate_voice_batch_http(voice_config, texts, pinyins, config, i)
        if not success:
            print(f"⚠️ {voice_config['name']}生成失败，可能部分文件未完成")

    # 7. 最终统计
    print(f"\n=== 最终统计 ===")

    total_generated = 0
    for voice in VOICE_CONFIGS:
        existing = get_existing_files(config["OUTPUT_DIR"], voice["voice_code"])
        total_generated += existing
        print(f"{voice['name']}: {existing} 个文件")

    print(f"\n总计生成文件: {total_generated}")
    print(f"输出目录: {config['OUTPUT_DIR']}")
    print(f"CSV记录: {csv_path}")
    print("=" * 60)

print("✅ HTTP API多音色版准备完成")

# 运行程序
print("\n🚀 开始运行豆包TTS HTTP API多音色版...")
main()

print("\n🎉 HTTP API多音色版运行完成！")
