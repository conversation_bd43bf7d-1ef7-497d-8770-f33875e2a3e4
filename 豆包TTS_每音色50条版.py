#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包语音合成大模型 - 每音色50条版
从1750条文本中，每种音色生成50条音频
"""

import requests
import base64
import time
import csv
import os
import uuid
import json
import random

print("✅ 环境设置完成 - 每音色50条版")

# ==================== 配置区域 ====================
DOUYIN_CONFIG = {
    # API配置
    "APPID": "1500263695",
    "ACCESS_TOKEN": "lsZJKq3sy6GngicgAcZN8mWQmqZ1cKGN", 
    "CLUSTER": "volcano_tts",
    
    # 音频格式配置
    "ENCODING": "wav",      
    "RATE": "16000",        
    "SPEED_RATIO": 1.0,
    "VOLUME_RATIO": 1.0,
    "PITCH_RATIO": 1.0,
    
    # 文件配置
    "TEXT_FILE": "text_with_English.txt",
    "PINYIN_FILE": "text_with_English_pinyin.txt",
    "OUTPUT_DIR": "audio_output_50_per_voice",
    "CSV_FILE": "doubao_50_per_voice_log.csv",
    "REQUEST_INTERVAL": 0.5,
    
    # 范围生成配置
    "START_INDEX": 251,  # 从第251条开始
    "END_INDEX": 2000,   # 到第2000条结束
    "TARGET_TEXTS_PER_VOICE": 50,  # 每种音色50条
}

# 音色配置
VOICE_CONFIGS = [
    # 基础音色
    {"voice_code": "D236", "voice_type": "BV115_streaming", "name": "BV115"},
    {"voice_code": "D237", "voice_type": "BV119_streaming", "name": "BV119"},
    {"voice_code": "D238", "voice_type": "BV700_streaming", "name": "BV700"},
    {"voice_code": "D239", "voice_type": "BV701_streaming", "name": "BV701"},
    
    # ICL音色
    {"voice_code": "I001", "voice_type": "ICL_zh_female_zhixingwenwan_tob", "name": "知性温婉"},
    {"voice_code": "I002", "voice_type": "ICL_zh_male_lvchaxiaoge_tob", "name": "旅途小哥"},
    {"voice_code": "I003", "voice_type": "ICL_zh_female_jiaoruoluoli_tob", "name": "娇弱萝莉"},
    {"voice_code": "I004", "voice_type": "ICL_zh_male_lengdanshuli_tob", "name": "冷淡梳理"},
]

# API配置
API_URL = "https://openspeech.bytedance.com/api/v1/tts"

def get_cluster(voice_type):
    """获取集群名称"""
    if voice_type.startswith("ICL_"):
        return "volcano_icl"
    return "volcano_tts"

class DouyinTTS50PerVoice:
    """豆包TTS每音色50条核心类"""
    
    def __init__(self, config):
        self.config = config
        self.headers = {"Authorization": f"Bearer;{config['ACCESS_TOKEN']}"}
        self.success_count = 0
        self.error_count = 0
        
    def synthesize_text(self, text, voice_type):
        """使用HTTP API合成单个文本"""
        cluster = get_cluster(voice_type)
        
        request_json = {
            "app": {
                "appid": self.config["APPID"],
                "token": self.config["ACCESS_TOKEN"],
                "cluster": cluster
            },
            "user": {
                "uid": str(uuid.uuid4())
            },
            "audio": {
                "voice_type": voice_type,
                "encoding": self.config["ENCODING"],
                "rate": self.config["RATE"],
                "speed_ratio": self.config["SPEED_RATIO"],
                "volume_ratio": self.config["VOLUME_RATIO"],
                "pitch_ratio": self.config["PITCH_RATIO"],
            },
            "request": {
                "reqid": str(uuid.uuid4()),
                "text": text,
                "text_type": "plain",
                "operation": "query",
                "with_frontend": 1,
                "frontend_type": "unitTson"
            }
        }
        
        try:
            resp = requests.post(API_URL, json=request_json, headers=self.headers, timeout=30)
            
            if resp.status_code == 200:
                resp_data = resp.json()
                if "data" in resp_data:
                    audio_data = base64.b64decode(resp_data["data"])
                    self.success_count += 1
                    return audio_data
                else:
                    print(f"  ✗ API响应无数据: {resp_data}")
                    self.error_count += 1
                    return None
            else:
                try:
                    error_data = resp.json()
                    print(f"  ✗ HTTP错误 {resp.status_code}: {error_data}")
                except:
                    print(f"  ✗ HTTP错误 {resp.status_code}: {resp.text}")
                self.error_count += 1
                return None
                
        except Exception as e:
            print(f"  ✗ 请求异常: {e}")
            self.error_count += 1
            return None

def load_text_files_range(config):
    """加载指定范围的文本和拼音文件"""
    print("=== 加载指定范围的文本文件 ===")
    
    start_idx = config["START_INDEX"]
    end_idx = config["END_INDEX"]
    
    print(f"目标范围: 第 {start_idx} 到第 {end_idx-1} 条文本")
    
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            all_texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件总行数: {len(all_texts)}")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    all_pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                all_pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件总行数: {len(all_pinyins)}")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    if start_idx >= len(all_texts) or end_idx > len(all_texts):
        print(f"✗ 范围超出文件长度: 文件有 {len(all_texts)} 行，请求范围 {start_idx}-{end_idx}")
        return None, None
    
    range_texts = all_texts[start_idx:end_idx]
    range_pinyins = all_pinyins[start_idx:end_idx] if len(all_pinyins) >= end_idx else []
    
    print(f"✓ 提取范围文本: {len(range_texts)} 条")
    print(f"✓ 提取范围拼音: {len(range_pinyins)} 条")
    
    return range_texts, range_pinyins

def select_texts_for_voice(texts, target_count, voice_index):
    """
    为单个音色从全部文本中随机选择指定数量的文本
    每种音色都能从全部1750条中选择50条
    """
    if len(texts) <= target_count:
        return texts, list(range(len(texts)))
    
    # 设置随机种子，确保每个音色的选择是可重现的但不同的
    random.seed(voice_index * 1000 + 42)  # 每个音色使用不同的种子
    
    # 从全部文本中随机选择指定数量
    all_indices = list(range(len(texts)))
    selected_indices = random.sample(all_indices, target_count)
    selected_indices.sort()  # 保持顺序
    
    selected_texts = [texts[i] for i in selected_indices]
    
    print(f"  音色 {voice_index+1} 从 {len(texts)} 条文本中随机选择了 {target_count} 条")
    print(f"  选择的索引范围: {min(selected_indices)} 到 {max(selected_indices)}")
    
    return selected_texts, selected_indices

def test_voice_before_batch(voice_config, config):
    """在批量生成前测试音色是否可用"""
    print(f"测试音色: {voice_config['name']} ({voice_config['voice_type']})")
    
    tts = DouyinTTS50PerVoice(config)
    test_text = "你好"
    
    audio_data = tts.synthesize_text(test_text, voice_config['voice_type'])
    
    if audio_data:
        print(f"  ✓ 音色可用 ({len(audio_data)} 字节)")
        return True
    else:
        print(f"  ✗ 音色不可用")
        return False

def generate_voice_batch_50(voice_config, texts, pinyins, config, batch_num):
    """为单个音色生成50条音频"""
    voice_type = voice_config["voice_type"]
    voice_name = voice_config["name"]
    voice_code = voice_config["voice_code"]
    target_count = config["TARGET_TEXTS_PER_VOICE"]
    
    print(f"\n=== 批次 {batch_num}: {voice_name} ({voice_code}) ===")
    print(f"音色类型: {voice_type}")
    print(f"目标文本数: {target_count} 条")
    
    # 先测试音色是否可用
    if not test_voice_before_batch(voice_config, config):
        print(f"✗ 音色测试失败，跳过此音色")
        return False
    
    # 从全部文本中随机选择50条
    voice_index = batch_num - 1
    selected_texts, selected_indices = select_texts_for_voice(texts, target_count, voice_index)
    
    print(f"选择文本: {len(selected_texts)} 条")
    
    if not selected_texts:
        print("✗ 没有可用的文本")
        return False
    
    # 创建TTS对象
    tts = DouyinTTS50PerVoice(config)
    
    success_count = 0
    error_count = 0
    start_time = time.time()
    
    # 打开CSV文件进行追加
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    csv_exists = os.path.exists(csv_path)
    
    with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile, delimiter='\t')
        
        if not csv_exists:
            writer.writerow(['音频名', '类型', '文本', '注音1', '音色'])
        
        for i, (text_idx, text) in enumerate(zip(selected_indices, selected_texts), 1):
            # 获取对应的拼音
            pinyin = ""
            if text_idx < len(pinyins):
                pinyin = pinyins[text_idx]
            
            # 音频文件名：音色代码 + 原始文本索引 + 序号
            original_idx = config["START_INDEX"] + text_idx
            audio_name = f"{voice_code}_{original_idx:07d}_{i:03d}"
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")
            
            print(f"[{i}/{len(selected_texts)}] {audio_name}: {text[:30]}{'...' if len(text) > 30 else ''}")
            
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                success_count += 1
                continue

            # 生成音频
            audio_data = None
            for retry in range(3):
                audio_data = tts.synthesize_text(text, voice_type)
                if audio_data:
                    break
                if retry < 2:
                    print(f"  重试 {retry + 1}/3...")
                    time.sleep(2)

            if audio_data:
                try:
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)

                    writer.writerow([audio_name, 'c', text, pinyin, voice_name])
                    csvfile.flush()

                    print(f"  ✓ 成功 ({len(audio_data)} 字节)")
                    success_count += 1

                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1

            time.sleep(config["REQUEST_INTERVAL"])
            
            # 每10个显示进度
            if i % 10 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(selected_texts) - i) * avg_time
                print(f"  进度: {i}/{len(selected_texts)}, 预计剩余: {remaining/60:.1f}分钟")
    
    elapsed_time = time.time() - start_time
    print(f"\n批次 {batch_num} 完成:")
    print(f"  耗时: {elapsed_time/60:.1f}分钟")
    print(f"  成功: {success_count}")
    print(f"  失败: {error_count}")
    print(f"  成功率: {(success_count/(success_count+error_count)*100):.1f}%" if success_count+error_count > 0 else "0%")
    
    return success_count > 0

def main():
    """主函数 - 每音色50条版"""
    print("=" * 60)
    print("豆包语音合成大模型 - 每音色50条版")
    print("从1750条文本中，每种音色生成50条音频")
    print("=" * 60)

    config = DOUYIN_CONFIG

    print("配置信息:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  输出目录: {config['OUTPUT_DIR']}")
    print(f"  生成范围: 第 {config['START_INDEX']} 到第 {config['END_INDEX']-1} 条 (共{config['END_INDEX']-config['START_INDEX']}条)")
    print(f"  每音色文本数: {config['TARGET_TEXTS_PER_VOICE']} 条")
    print(f"  总音色数: {len(VOICE_CONFIGS)} 个")
    print(f"  预计总音频数: {len(VOICE_CONFIGS) * config['TARGET_TEXTS_PER_VOICE']} 个")

    print(f"\n音色配置:")
    for i, voice in enumerate(VOICE_CONFIGS, 1):
        voice_type = voice['voice_type']
        cluster = get_cluster(voice_type)
        print(f"  {i}. {voice['name']} ({voice['voice_code']}) - {voice_type} [{cluster}]")

    print("=" * 60)

    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return

    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)

    texts, pinyins = load_text_files_range(config)
    if texts is None:
        return

    # 初始化CSV文件
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    if not os.path.exists(csv_path):
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile, delimiter='\t')
            writer.writerow(['音频名', '类型', '文本', '注音1', '音色'])
        print(f"✓ 创建CSV文件: {csv_path}")

    print(f"\n=== 开始分批生成 ===")
    
    successful_voices = 0
    failed_voices = 0
    total_generated = 0

    for i, voice_config in enumerate(VOICE_CONFIGS, 1):
        success = generate_voice_batch_50(voice_config, texts, pinyins, config, i)
        if success:
            successful_voices += 1
            total_generated += config['TARGET_TEXTS_PER_VOICE']
            print(f"✓ {voice_config['name']} 生成成功")
        else:
            failed_voices += 1
            print(f"✗ {voice_config['name']} 生成失败")
        
        if i < len(VOICE_CONFIGS):
            print(f"等待 {config['REQUEST_INTERVAL']} 秒后处理下一个音色...")
            time.sleep(config['REQUEST_INTERVAL'])

    print(f"\n=== 最终统计 ===")
    print(f"成功音色: {successful_voices}/{len(VOICE_CONFIGS)}")
    print(f"失败音色: {failed_voices}/{len(VOICE_CONFIGS)}")
    print(f"预计生成音频: {total_generated} 个")
    print(f"输出目录: {config['OUTPUT_DIR']}")
    print("=" * 60)

if __name__ == "__main__":
    print("\n🚀 开始运行豆包TTS每音色50条版...")
    main()
    print("\n🎉 每音色50条版运行完成！")
