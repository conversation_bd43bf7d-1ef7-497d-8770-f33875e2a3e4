#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云TTS拼音修复工具
从文本文件中匹配对应的拼音，填充到CSV文件的注音1列中
"""

import csv
import os
from typing import Dict, List, Optional

# 配置信息
CONFIG = {
    "CSV_FILE": "ali_audio_loh.csv",  # 需要修复的CSV文件
    "TEXT_FILE": "shuffled_text_aliyun.txt",  # 文本文件
    "PINYIN_FILE": "shuffled_text_aliyun_pinyin.txt",  # 拼音文件
    "OUTPUT_CSV": "ali_audio_loh_fixed.csv",  # 修复后的CSV文件
}

def load_text_and_pinyin_mapping(text_file: str, pinyin_file: str) -> Dict[str, str]:
    """
    加载文本和拼音文件，创建文本到拼音的映射
    
    Args:
        text_file: 文本文件路径
        pinyin_file: 拼音文件路径
    
    Returns:
        文本到拼音的映射字典
    """
    print("=== 加载文本和拼音文件 ===")
    
    # 读取文本文件
    texts = []
    try:
        with open(text_file, 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return {}
    
    # 读取拼音文件
    pinyins = []
    try:
        with open(pinyin_file, 'r', encoding='utf-8') as f:
            pinyins = [line.strip() for line in f]
        print(f"✓ 拼音文件: {len(pinyins)} 行")
    except Exception as e:
        print(f"✗ 读取拼音文件失败: {e}")
        return {}
    
    # 检查文件行数是否一致
    if len(texts) != len(pinyins):
        print(f"⚠️ 警告: 文本文件({len(texts)}行)和拼音文件({len(pinyins)}行)行数不一致")
    
    # 创建文本到拼音的映射
    text_to_pinyin = {}
    min_lines = min(len(texts), len(pinyins))
    
    for i in range(min_lines):
        text = texts[i].strip()
        pinyin = pinyins[i].strip()
        
        if text and pinyin:  # 跳过空行
            text_to_pinyin[text] = pinyin
    
    print(f"✓ 创建文本-拼音映射: {len(text_to_pinyin)} 条记录")
    
    # 显示一些示例
    print("\n映射示例 (前5条):")
    for i, (text, pinyin) in enumerate(list(text_to_pinyin.items())[:5]):
        print(f"  {i+1}. {text} -> {pinyin}")
    
    return text_to_pinyin

def read_csv_file(csv_file: str) -> List[List[str]]:
    """
    读取CSV文件
    
    Args:
        csv_file: CSV文件路径
    
    Returns:
        CSV数据列表
    """
    print(f"\n=== 读取CSV文件: {csv_file} ===")
    
    if not os.path.exists(csv_file):
        print(f"✗ CSV文件不存在: {csv_file}")
        return []
    
    csv_data = []
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            # 尝试自动检测分隔符
            sample = f.read(1024)
            f.seek(0)
            
            # 检测是否使用制表符分隔
            if '\t' in sample and sample.count('\t') > sample.count(','):
                delimiter = '\t'
                print("检测到制表符分隔格式")
            else:
                delimiter = ','
                print("使用逗号分隔格式")
            
            reader = csv.reader(f, delimiter=delimiter)
            csv_data = list(reader)
        
        print(f"✓ 读取CSV文件: {len(csv_data)} 行")
        
        if csv_data:
            print(f"✓ 列数: {len(csv_data[0])}")
            print(f"✓ 标题行: {csv_data[0]}")
        
        return csv_data
        
    except Exception as e:
        print(f"✗ 读取CSV文件失败: {e}")
        return []

def fix_pinyin_in_csv(csv_data: List[List[str]], text_to_pinyin: Dict[str, str]) -> List[List[str]]:
    """
    修复CSV文件中的拼音信息
    
    Args:
        csv_data: CSV数据
        text_to_pinyin: 文本到拼音的映射
    
    Returns:
        修复后的CSV数据
    """
    print(f"\n=== 修复CSV文件中的拼音信息 ===")
    
    if not csv_data:
        print("✗ 没有CSV数据")
        return []
    
    # 检查CSV格式
    header = csv_data[0]
    print(f"CSV标题行: {header}")
    
    # 查找文本列和注音1列的索引
    text_col_index = None
    pinyin_col_index = None
    
    for i, col_name in enumerate(header):
        if '文本' in col_name:
            text_col_index = i
            print(f"找到文本列: 第{i+1}列 ({col_name})")
        elif '注音' in col_name or 'pinyin' in col_name.lower():
            pinyin_col_index = i
            print(f"找到拼音列: 第{i+1}列 ({col_name})")
    
    if text_col_index is None:
        print("✗ 未找到文本列")
        return csv_data
    
    if pinyin_col_index is None:
        print("✗ 未找到拼音列")
        return csv_data
    
    # 修复拼音信息
    fixed_data = [header]  # 保留标题行
    match_count = 0
    no_match_count = 0
    
    for i, row in enumerate(csv_data[1:], 1):  # 跳过标题行
        if len(row) <= max(text_col_index, pinyin_col_index):
            # 如果行数据不完整，补充空列
            while len(row) <= max(text_col_index, pinyin_col_index):
                row.append("")
        
        text = row[text_col_index].strip()
        
        # 在映射中查找对应的拼音
        if text in text_to_pinyin:
            row[pinyin_col_index] = text_to_pinyin[text]
            match_count += 1
            if i <= 10:  # 显示前10个匹配的示例
                print(f"  匹配 {i}: {text} -> {text_to_pinyin[text]}")
        else:
            # 如果没有找到完全匹配，保留原有拼音或设为空
            if not row[pinyin_col_index].strip():
                row[pinyin_col_index] = ""
            no_match_count += 1
            if no_match_count <= 5:  # 显示前5个未匹配的示例
                print(f"  未匹配 {i}: {text}")
        
        fixed_data.append(row)
    
    print(f"\n修复统计:")
    print(f"  成功匹配: {match_count}")
    print(f"  未找到匹配: {no_match_count}")
    print(f"  总计处理: {match_count + no_match_count}")
    print(f"  匹配率: {(match_count/(match_count+no_match_count)*100):.1f}%" if match_count+no_match_count > 0 else "0%")
    
    return fixed_data

def save_csv_file(csv_data: List[List[str]], output_file: str):
    """
    保存修复后的CSV文件
    
    Args:
        csv_data: CSV数据
        output_file: 输出文件路径
    """
    print(f"\n=== 保存修复后的CSV文件: {output_file} ===")
    
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f, delimiter='\t')  # 使用制表符分隔
            writer.writerows(csv_data)
        
        print(f"✓ 成功保存: {len(csv_data)} 行")
        print(f"✓ 文件路径: {output_file}")
        
    except Exception as e:
        print(f"✗ 保存文件失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("阿里云TTS拼音修复工具")
    print("从文本文件中匹配对应的拼音，填充到CSV文件中")
    print("=" * 60)
    
    config = CONFIG
    
    print("配置信息:")
    print(f"  CSV文件: {config['CSV_FILE']}")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  拼音文件: {config['PINYIN_FILE']}")
    print(f"  输出文件: {config['OUTPUT_CSV']}")
    print("=" * 60)
    
    # 1. 检查文件是否存在
    for file_key in ['CSV_FILE', 'TEXT_FILE', 'PINYIN_FILE']:
        file_path = config[file_key]
        if not os.path.exists(file_path):
            print(f"✗ 文件不存在: {file_path}")
            return
    
    # 2. 加载文本和拼音映射
    text_to_pinyin = load_text_and_pinyin_mapping(
        config['TEXT_FILE'], 
        config['PINYIN_FILE']
    )
    
    if not text_to_pinyin:
        print("✗ 无法创建文本-拼音映射，程序退出")
        return
    
    # 3. 读取CSV文件
    csv_data = read_csv_file(config['CSV_FILE'])
    
    if not csv_data:
        print("✗ 无法读取CSV文件，程序退出")
        return
    
    # 4. 修复拼音信息
    fixed_csv_data = fix_pinyin_in_csv(csv_data, text_to_pinyin)
    
    if not fixed_csv_data:
        print("✗ 拼音修复失败，程序退出")
        return
    
    # 5. 保存修复后的文件
    save_csv_file(fixed_csv_data, config['OUTPUT_CSV'])
    
    print(f"\n🎉 拼音修复完成！")
    print(f"原文件: {config['CSV_FILE']}")
    print(f"修复后: {config['OUTPUT_CSV']}")
    print("=" * 60)

if __name__ == "__main__":
    main()
