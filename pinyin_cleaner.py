#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拼音清理工具
清理拼音文件中的编码问题、不可见字符、格式不一致等问题
"""

import os
import re
import unicodedata
from typing import Set, List

# 配置信息
CONFIG = {
    "INPUT_FILE": "pinyin_complete.txt",
    "OUTPUT_FILE": "pinyin_complete_cleaned.txt",
    "BACKUP_FILE": "pinyin_complete_backup.txt",
}

def clean_text_encoding(text: str) -> str:
    """
    清理文本编码问题
    """
    # Unicode标准化
    text = unicodedata.normalize('NFC', text)
    
    # 移除BOM标记
    if text.startswith('\ufeff'):
        text = text[1:]
    
    return text

def remove_invisible_characters(text: str) -> str:
    """
    移除不可见字符
    """
    # 保留的空白字符
    allowed_whitespace = {' ', '\t', '\n', '\r'}
    
    cleaned_chars = []
    for char in text:
        # 检查字符类别
        category = unicodedata.category(char)
        
        # 保留正常字符和允许的空白字符
        if (category[0] not in ['C'] or char in allowed_whitespace):
            cleaned_chars.append(char)
        else:
            # 记录被移除的不可见字符
            print(f"移除不可见字符: U+{ord(char):04X} ({unicodedata.name(char, 'UNKNOWN')})")
    
    return ''.join(cleaned_chars)

def normalize_whitespace(text: str) -> str:
    """
    标准化空白字符
    """
    # 替换各种空白字符为标准空格
    whitespace_chars = [
        '\u00A0',  # 不间断空格
        '\u2000', '\u2001', '\u2002', '\u2003', '\u2004', '\u2005',  # 各种空格
        '\u2006', '\u2007', '\u2008', '\u2009', '\u200A', '\u200B',
        '\u3000',  # 全角空格
        '\u180E',  # 蒙古文空格
        '\u200C', '\u200D',  # 零宽字符
    ]
    
    for ws_char in whitespace_chars:
        text = text.replace(ws_char, ' ')
    
    # 标准化连续空格
    text = re.sub(r' +', ' ', text)
    
    # 清理行首行尾空格
    lines = text.split('\n')
    cleaned_lines = [line.strip() for line in lines]
    
    return '\n'.join(cleaned_lines)

def normalize_pinyin_format(text: str) -> str:
    """
    标准化拼音格式
    """
    # 统一转为小写
    text = text.lower()
    
    # 标准化声调数字
    # 确保声调数字紧跟拼音字母
    text = re.sub(r'([a-zü]+)\s*([1-5])', r'\1\2', text)
    
    # 移除多余的标点符号（保留空格和换行）
    text = re.sub(r'[^\w\s\n\r]', ' ', text)
    
    # 再次清理多余空格
    text = re.sub(r' +', ' ', text)
    
    return text

def validate_pinyin_format(text: str) -> List[str]:
    """
    验证拼音格式并返回问题列表
    """
    issues = []
    
    # 检查是否有非ASCII字符（除了ü）
    non_ascii = re.findall(r'[^\x00-\x7F]', text)
    if non_ascii:
        unique_non_ascii = list(set(non_ascii))
        if unique_non_ascii != ['ü']:
            issues.append(f"包含非ASCII字符: {unique_non_ascii}")
    
    # 检查拼音格式
    lines = text.split('\n')
    invalid_lines = []
    
    for i, line in enumerate(lines[:100], 1):  # 检查前100行
        if not line.strip():
            continue
        
        # 提取拼音
        pinyins = re.findall(r'[a-zü]+\d*', line)
        if not pinyins:
            invalid_lines.append(f"第{i}行: 无有效拼音")
        else:
            for pinyin in pinyins:
                if len(pinyin) < 2:
                    invalid_lines.append(f"第{i}行: 拼音过短 '{pinyin}'")
                elif not re.match(r'^[a-zü]+[1-5]?$', pinyin):
                    invalid_lines.append(f"第{i}行: 拼音格式异常 '{pinyin}'")
    
    if invalid_lines:
        issues.extend(invalid_lines[:10])  # 只显示前10个问题
    
    return issues

def clean_pinyin_file(input_file: str, output_file: str, backup_file: str = None) -> dict:
    """
    清理拼音文件
    """
    print(f"=== 清理拼音文件: {input_file} ===")
    
    if not os.path.exists(input_file):
        print(f"✗ 输入文件不存在: {input_file}")
        return {"success": False, "error": "文件不存在"}
    
    try:
        # 创建备份
        if backup_file:
            import shutil
            shutil.copy2(input_file, backup_file)
            print(f"✓ 创建备份: {backup_file}")
        
        # 尝试不同编码读取文件
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
        content = None
        used_encoding = None
        
        for encoding in encodings:
            try:
                with open(input_file, 'r', encoding=encoding) as f:
                    content = f.read()
                used_encoding = encoding
                print(f"✓ 使用编码: {encoding}")
                break
            except UnicodeDecodeError:
                continue
        
        if content is None:
            return {"success": False, "error": "无法读取文件"}
        
        original_size = len(content)
        original_lines = len(content.split('\n'))
        
        print(f"原始文件: {original_size} 字符, {original_lines} 行")
        
        # 清理步骤
        print("\n执行清理步骤:")
        
        # 1. 编码标准化
        print("1. 编码标准化...")
        content = clean_text_encoding(content)
        
        # 2. 移除不可见字符
        print("2. 移除不可见字符...")
        content = remove_invisible_characters(content)
        
        # 3. 标准化空白字符
        print("3. 标准化空白字符...")
        content = normalize_whitespace(content)
        
        # 4. 标准化拼音格式
        print("4. 标准化拼音格式...")
        content = normalize_pinyin_format(content)
        
        # 5. 验证格式
        print("5. 验证拼音格式...")
        issues = validate_pinyin_format(content)
        if issues:
            print("⚠️ 发现格式问题:")
            for issue in issues:
                print(f"  {issue}")
        else:
            print("✓ 格式验证通过")
        
        # 保存清理后的文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        cleaned_size = len(content)
        cleaned_lines = len(content.split('\n'))
        
        print(f"\n✓ 清理完成:")
        print(f"  原始: {original_size} 字符, {original_lines} 行")
        print(f"  清理后: {cleaned_size} 字符, {cleaned_lines} 行")
        print(f"  输出文件: {output_file}")
        
        # 提取拼音统计
        pinyins = set()
        for line in content.split('\n'):
            line_pinyins = re.findall(r'[a-zü]+\d*', line)
            pinyins.update(p for p in line_pinyins if len(p) >= 2)
        
        print(f"  拼音数量: {len(pinyins)} 个")
        
        return {
            "success": True,
            "original_size": original_size,
            "cleaned_size": cleaned_size,
            "original_lines": original_lines,
            "cleaned_lines": cleaned_lines,
            "pinyin_count": len(pinyins),
            "issues": issues,
            "encoding": used_encoding
        }
        
    except Exception as e:
        print(f"✗ 清理失败: {e}")
        return {"success": False, "error": str(e)}

def compare_before_after(original_file: str, cleaned_file: str):
    """
    对比清理前后的差异
    """
    print(f"\n=== 对比清理前后差异 ===")
    
    try:
        # 读取原始文件
        with open(original_file, 'r', encoding='utf-8', errors='ignore') as f:
            original_content = f.read()
        
        # 读取清理后文件
        with open(cleaned_file, 'r', encoding='utf-8') as f:
            cleaned_content = f.read()
        
        # 提取拼音
        original_pinyins = set()
        for line in original_content.split('\n'):
            line_pinyins = re.findall(r'[a-zü]+\d*', line.lower())
            original_pinyins.update(p for p in line_pinyins if len(p) >= 2)
        
        cleaned_pinyins = set()
        for line in cleaned_content.split('\n'):
            line_pinyins = re.findall(r'[a-zü]+\d*', line)
            cleaned_pinyins.update(p for p in line_pinyins if len(p) >= 2)
        
        # 对比结果
        lost_pinyins = original_pinyins - cleaned_pinyins
        new_pinyins = cleaned_pinyins - original_pinyins
        
        print(f"拼音对比:")
        print(f"  原始拼音: {len(original_pinyins)} 个")
        print(f"  清理后拼音: {len(cleaned_pinyins)} 个")
        print(f"  丢失拼音: {len(lost_pinyins)} 个")
        print(f"  新增拼音: {len(new_pinyins)} 个")
        
        if lost_pinyins:
            print(f"  丢失示例: {list(lost_pinyins)[:10]}")
        
        if new_pinyins:
            print(f"  新增示例: {list(new_pinyins)[:10]}")
        
    except Exception as e:
        print(f"✗ 对比失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("拼音清理工具")
    print("清理编码问题、不可见字符、格式不一致等问题")
    print("=" * 60)
    
    config = CONFIG
    
    print("配置信息:")
    print(f"  输入文件: {config['INPUT_FILE']}")
    print(f"  输出文件: {config['OUTPUT_FILE']}")
    print(f"  备份文件: {config['BACKUP_FILE']}")
    print("=" * 60)
    
    # 清理文件
    result = clean_pinyin_file(
        config['INPUT_FILE'], 
        config['OUTPUT_FILE'], 
        config['BACKUP_FILE']
    )
    
    if result.get('success', False):
        # 对比清理前后
        compare_before_after(config['INPUT_FILE'], config['OUTPUT_FILE'])
        
        print(f"\n🎉 拼音文件清理完成！")
        print(f"清理后文件: {config['OUTPUT_FILE']}")
        print(f"备份文件: {config['BACKUP_FILE']}")
        
        if result.get('issues'):
            print(f"\n⚠️ 仍有 {len(result['issues'])} 个格式问题需要手动检查")
    else:
        print(f"\n❌ 清理失败: {result.get('error', '未知错误')}")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
