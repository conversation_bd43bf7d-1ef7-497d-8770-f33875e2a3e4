# 豆包TTS问题解决方案指南

## 🚨 当前问题
持续出现WebSocket 1007错误：`invalid continuation byte at position 1`

## 🔍 可能原因
1. **API密钥权限不足** - 没有TTS服务权限
2. **账户配额问题** - 免费额度用完或付费账户问题
3. **音色权限问题** - 某些音色需要特殊权限
4. **网络环境问题** - 防火墙或代理设置

## 💡 解决方案

### 方案1: 检查豆包账户状态
1. **登录豆包控制台**
   - 访问：https://console.volcengine.com/speech/
   - 检查TTS服务状态
   - 确认账户余额和配额

2. **验证API权限**
   - 确认APPID和ACCESS_TOKEN正确
   - 检查是否有TTS服务权限
   - 确认音色列表和权限

3. **联系技术支持**
   - 提供错误日志
   - 说明使用场景
   - 请求技术协助

### 方案2: 使用备用TTS服务

#### A. 微软Edge TTS（免费）
```bash
pip install edge-tts
```

```python
import edge_tts
import asyncio

async def edge_tts_synthesis(text, voice, output_path):
    communicate = edge_tts.Communicate(text, voice)
    await communicate.save(output_path)

# 中文音色
voices = [
    "zh-CN-XiaoxiaoNeural",  # 晓晓
    "zh-CN-YunxiNeural",     # 云希
    "zh-CN-YunjianNeural",   # 云健
    "zh-CN-XiaoyiNeural",   # 晓伊
    "zh-CN-YunyangNeural",   # 云扬
]
```

#### B. 百度语音合成
```python
from aip import AipSpeech

APP_ID = 'your_app_id'
API_KEY = 'your_api_key'
SECRET_KEY = 'your_secret_key'

client = AipSpeech(APP_ID, API_KEY, SECRET_KEY)

def baidu_tts(text, output_path):
    result = client.synthesis(text, 'zh', 1, {
        'vol': 5,    # 音量
        'spd': 5,    # 语速
        'pit': 5,    # 音调
        'per': 0     # 发音人
    })
    
    if not isinstance(result, dict):
        with open(output_path, 'wb') as f:
            f.write(result)
```

#### C. 科大讯飞语音合成
```python
import websocket
import json
import base64

def xunfei_tts(text, output_path):
    # 科大讯飞TTS实现
    pass
```

### 方案3: 本地TTS解决方案

#### A. pyttsx3（离线）
```python
import pyttsx3

def local_tts(text, output_path):
    engine = pyttsx3.init()
    
    # 设置语音参数
    voices = engine.getProperty('voices')
    engine.setProperty('voice', voices[0].id)
    engine.setProperty('rate', 150)  # 语速
    engine.setProperty('volume', 0.9)  # 音量
    
    engine.save_to_file(text, output_path)
    engine.runAndWait()
```

#### B. gTTS（需要网络）
```python
from gtts import gTTS

def gtts_synthesis(text, output_path):
    tts = gTTS(text=text, lang='zh', slow=False)
    tts.save(output_path)
```

## 🛠️ 推荐实施步骤

### 第一步：立即可用方案
使用Edge TTS作为临时解决方案：

```python
import edge_tts
import asyncio
import os

async def generate_with_edge_tts():
    voices = [
        "zh-CN-XiaoxiaoNeural",
        "zh-CN-YunxiNeural", 
        "zh-CN-YunjianNeural",
        "zh-CN-XiaoyiNeural",
        "zh-CN-YunyangNeural",
    ]
    
    texts = ["你好", "测试", "中国", "北京", "上海"]
    
    for i, voice in enumerate(voices, 1):
        for j, text in enumerate(texts, 1):
            filename = f"ScD{i:03d}{j:07d}.wav"
            await edge_tts.Communicate(text, voice).save(filename)
            print(f"生成: {filename}")

# 运行
asyncio.run(generate_with_edge_tts())
```

### 第二步：解决豆包问题
1. 联系豆包技术支持
2. 检查账户权限和配额
3. 尝试不同的音色类型

### 第三步：建立多TTS架构
创建支持多个TTS服务的统一接口：

```python
class MultiTTSEngine:
    def __init__(self):
        self.engines = {
            'edge': EdgeTTSEngine(),
            'baidu': BaiduTTSEngine(),
            'douyin': DouyinTTSEngine(),
        }
    
    async def synthesize(self, text, voice, engine='edge'):
        return await self.engines[engine].synthesize(text, voice)
```

## 📞 紧急联系方式

### 豆包技术支持
- 官方文档：https://www.volcengine.com/docs/6561
- 技术支持：通过控制台提交工单
- 社区论坛：https://developer.volcengine.com/

### 备用服务商
1. **微软Azure Speech** - 高质量，多音色
2. **Google Cloud TTS** - 支持多语言
3. **Amazon Polly** - AWS生态
4. **阿里云语音合成** - 国内服务
5. **腾讯云语音合成** - 国内服务

## 🎯 建议行动

### 立即执行（今天）
1. 使用Edge TTS生成测试音频
2. 联系豆包技术支持
3. 检查豆包账户状态

### 短期计划（3天内）
1. 实施备用TTS方案
2. 完善多TTS架构
3. 测试不同服务质量

### 长期规划（1周内）
1. 建立稳定的TTS生产环境
2. 实现自动故障转移
3. 优化音频质量和生成效率

---

**重要提醒**: 不要让技术问题阻止项目进展。先用可用的方案（如Edge TTS）继续工作，同时并行解决豆包的问题。
