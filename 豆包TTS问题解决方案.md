# 豆包TTS WebSocket错误解决方案

## 🚨 问题描述
遇到持续性的WebSocket错误：
```
ERROR: 服务器错误: 接收消息失败: sent 1007 (invalid frame payload data) invalid continuation byte at position 1; no close frame received
```

## 🔍 问题分析

### 错误代码1007含义
- **WebSocket错误码1007**: 表示接收到的数据不符合预期的数据类型
- **"invalid continuation byte"**: UTF-8编码问题或数据损坏

### 可能原因
1. **API密钥问题**: 密钥过期、权限不足或格式错误
2. **网络环境**: 防火墙、代理或网络限制
3. **协议版本**: WebSocket协议版本不兼容
4. **请求格式**: JSON格式或编码问题
5. **服务端限制**: API调用频率限制或服务维护

## 🛠️ 解决方案

### 方案1: 检查API配置
```python
# 验证API配置
APPID = "9862368305"
ACCESS_TOKEN = "bbxNaAzRzBSkySjouDItXl6tBUUyNd2W"

# 检查项目：
# 1. APPID是否正确
# 2. ACCESS_TOKEN是否有效
# 3. 是否有TTS服务权限
```

### 方案2: 使用HTTP API替代WebSocket
由于WebSocket持续失败，建议切换到HTTP REST API：

```python
import requests
import json

def tts_http_api(text, voice_type):
    """使用HTTP API进行TTS合成"""
    url = "https://openspeech.bytedance.com/api/v1/tts"
    
    headers = {
        "Authorization": f"Bearer;{ACCESS_TOKEN}",
        "Content-Type": "application/json"
    }
    
    data = {
        "app": {
            "appid": APPID,
            "token": ACCESS_TOKEN,
            "cluster": "volcano_tts"
        },
        "user": {
            "uid": "test_user"
        },
        "audio": {
            "voice_type": voice_type,
            "encoding": "wav"
        },
        "request": {
            "reqid": str(uuid.uuid4()),
            "text": text,
            "operation": "submit"
        }
    }
    
    response = requests.post(url, headers=headers, json=data)
    return response
```

### 方案3: 网络环境检查
```bash
# 检查网络连接
ping openspeech.bytedance.com

# 检查DNS解析
nslookup openspeech.bytedance.com

# 测试WebSocket连接
curl -i -N -H "Connection: Upgrade" \
     -H "Upgrade: websocket" \
     -H "Sec-WebSocket-Key: test" \
     -H "Sec-WebSocket-Version: 13" \
     https://openspeech.bytedance.com/api/v1/tts/ws_binary
```

### 方案4: 使用其他TTS服务
如果豆包TTS持续有问题，可以考虑：

1. **Azure Speech Services**
2. **Google Cloud Text-to-Speech**
3. **Amazon Polly**
4. **百度语音合成**
5. **科大讯飞语音合成**

## 🔧 临时解决方案

### 使用本地TTS引擎
```python
import pyttsx3

def local_tts(text, output_path):
    """使用本地TTS引擎"""
    engine = pyttsx3.init()
    
    # 设置语音参数
    voices = engine.getProperty('voices')
    engine.setProperty('voice', voices[0].id)  # 选择语音
    engine.setProperty('rate', 150)  # 语速
    
    # 保存到文件
    engine.save_to_file(text, output_path)
    engine.runAndWait()
```

### 使用edge-tts（微软Edge浏览器TTS）
```bash
pip install edge-tts
```

```python
import edge_tts
import asyncio

async def edge_tts_synthesis(text, voice, output_path):
    """使用Edge TTS合成"""
    communicate = edge_tts.Communicate(text, voice)
    await communicate.save(output_path)

# 使用示例
asyncio.run(edge_tts_synthesis("你好", "zh-CN-XiaoxiaoNeural", "output.wav"))
```

## 📞 联系技术支持

如果问题持续存在，建议：

1. **联系豆包技术支持**
   - 提供错误日志
   - 说明使用场景
   - 请求技术协助

2. **检查服务状态**
   - 查看豆包官方状态页面
   - 确认服务是否正常

3. **更新SDK版本**
   - 检查是否有新版本的SDK
   - 更新到最新版本

## 🎯 推荐行动计划

### 短期方案（立即执行）
1. 验证API密钥和权限
2. 尝试HTTP API替代WebSocket
3. 测试网络连接

### 中期方案（1-2天内）
1. 联系豆包技术支持
2. 准备备用TTS服务
3. 测试其他语音合成方案

### 长期方案（1周内）
1. 建立多TTS服务架构
2. 实现自动故障转移
3. 完善错误处理机制

## 📝 测试检查清单

- [ ] API密钥是否有效
- [ ] 网络连接是否正常
- [ ] 防火墙设置是否正确
- [ ] 是否有代理服务器
- [ ] 服务端是否正常
- [ ] 请求格式是否正确
- [ ] 编码是否正确
- [ ] 频率限制是否触发

---

**建议**: 由于WebSocket问题持续存在，强烈建议先切换到HTTP API或其他TTS服务，确保项目能够正常进行。
