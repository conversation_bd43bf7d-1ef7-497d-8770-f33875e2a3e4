#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包语音合成大模型 - 智能音色版
1. 音色码按D+三位数字格式
2. 音频名按SaD0050000002格式
3. ICL音色改用标准集群
4. 不可用音色自动替换为备用音色
5. 每种音色50条，文本和音色都不重复
6. 输出最后处理的文本索引
"""

import requests
import base64
import time
import csv
import os
import uuid
import json
import random

print("✅ 环境设置完成 - 智能音色版")

# ==================== 配置区域 ====================
DOUYIN_CONFIG = {
    # API配置
    "APPID": "1500263695",
    "ACCESS_TOKEN": "lsZJKq3sy6GngicgAcZN8mWQmqZ1cKGN", 
    "CLUSTER": "volcano_tts",
    
    # 音频格式配置
    "ENCODING": "wav",      
    "RATE": "16000",        
    "SPEED_RATIO": 1.0,
    "VOLUME_RATIO": 1.0,
    "PITCH_RATIO": 1.0,
    
    # 文件配置
    "TEXT_FILE": "text_with_English.txt",
    "PINYIN_FILE": "text_with_English_pinyin.txt",
    "OUTPUT_DIR": "audio_output_smart_voice",
    "CSV_FILE": "doubao_smart_voice_log.csv",
    "REQUEST_INTERVAL": 0.5,
    
    # 范围生成配置
    "START_INDEX": 251,  # 从第251条开始
    "END_INDEX": 2000,   # 到第2000条结束
    "TARGET_TEXTS_PER_VOICE": 50,  # 每种音色50条
}

# 主要音色配置 - 按照D+三位数字格式
PRIMARY_VOICE_CONFIGS = [
    # 基础音色（通常可用）
    {"voice_code": "D005", "voice_type": "BV115_streaming", "name": "BV115"},
    {"voice_code": "D006", "voice_type": "BV119_streaming", "name": "BV119"},
    {"voice_code": "D007", "voice_type": "BV700_streaming", "name": "BV700"},
    {"voice_code": "D008", "voice_type": "BV701_streaming", "name": "BV701"},
    {"voice_code": "D009", "voice_type": "BV001_streaming", "name": "BV001"},
    {"voice_code": "D010", "voice_type": "BV002_streaming", "name": "BV002"},
    
    # ICL音色（改用标准集群测试）
    {"voice_code": "D064", "voice_type": "zh_female_zhixingwenwan", "name": "知性温婉"},
    {"voice_code": "D065", "voice_type": "zh_male_lvchaxiaoge", "name": "旅途小哥"},
    {"voice_code": "D066", "voice_type": "zh_female_jiaoruoluoli", "name": "娇弱萝莉"},
    {"voice_code": "D067", "voice_type": "zh_male_lengdanshuli", "name": "冷淡梳理"},
    {"voice_code": "D068", "voice_type": "zh_male_nuanxintitie", "name": "暖心体贴"},
    {"voice_code": "D069", "voice_type": "zh_male_hanhoudunshi", "name": "憨厚敦实"},
]

# 备用音色池（当主要音色不可用时使用）
BACKUP_VOICE_CONFIGS = [
    {"voice_code": "D011", "voice_type": "BV003_streaming", "name": "BV003"},
    {"voice_code": "D012", "voice_type": "BV004_streaming", "name": "BV004"},
    {"voice_code": "D013", "voice_type": "BV005_streaming", "name": "BV005"},
    {"voice_code": "D014", "voice_type": "BV006_streaming", "name": "BV006"},
    {"voice_code": "D015", "voice_type": "BV007_streaming", "name": "BV007"},
    {"voice_code": "D016", "voice_type": "BV008_streaming", "name": "BV008"},
    {"voice_code": "D017", "voice_type": "BV009_streaming", "name": "BV009"},
    {"voice_code": "D018", "voice_type": "BV010_streaming", "name": "BV010"},
    {"voice_code": "D019", "voice_type": "BV011_streaming", "name": "BV011"},
    {"voice_code": "D020", "voice_type": "BV012_streaming", "name": "BV012"},
]

# API配置
API_URL = "https://openspeech.bytedance.com/api/v1/tts"

class DouyinTTSSmart:
    """豆包TTS智能音色核心类"""
    
    def __init__(self, config):
        self.config = config
        self.headers = {"Authorization": f"Bearer;{config['ACCESS_TOKEN']}"}
        self.success_count = 0
        self.error_count = 0
        
    def synthesize_text(self, text, voice_type):
        """使用HTTP API合成单个文本（统一使用标准集群）"""
        
        request_json = {
            "app": {
                "appid": self.config["APPID"],
                "token": self.config["ACCESS_TOKEN"],
                "cluster": "volcano_tts"  # 统一使用标准集群
            },
            "user": {
                "uid": str(uuid.uuid4())
            },
            "audio": {
                "voice_type": voice_type,
                "encoding": self.config["ENCODING"],
                "rate": self.config["RATE"],
                "speed_ratio": self.config["SPEED_RATIO"],
                "volume_ratio": self.config["VOLUME_RATIO"],
                "pitch_ratio": self.config["PITCH_RATIO"],
            },
            "request": {
                "reqid": str(uuid.uuid4()),
                "text": text,
                "text_type": "plain",
                "operation": "query",
                "with_frontend": 1,
                "frontend_type": "unitTson"
            }
        }
        
        try:
            resp = requests.post(API_URL, json=request_json, headers=self.headers, timeout=30)
            
            if resp.status_code == 200:
                resp_data = resp.json()
                if "data" in resp_data:
                    audio_data = base64.b64decode(resp_data["data"])
                    self.success_count += 1
                    return audio_data
                else:
                    print(f"  ✗ API响应无数据: {resp_data}")
                    self.error_count += 1
                    return None
            else:
                try:
                    error_data = resp.json()
                    print(f"  ✗ HTTP错误 {resp.status_code}: {error_data}")
                except:
                    print(f"  ✗ HTTP错误 {resp.status_code}: {resp.text}")
                self.error_count += 1
                return None
                
        except Exception as e:
            print(f"  ✗ 请求异常: {e}")
            self.error_count += 1
            return None

def test_voice_availability(voice_config, config):
    """测试音色是否可用"""
    print(f"测试音色: {voice_config['name']} ({voice_config['voice_type']})")
    
    tts = DouyinTTSSmart(config)
    test_text = "你好"
    
    audio_data = tts.synthesize_text(test_text, voice_config['voice_type'])
    
    if audio_data:
        print(f"  ✓ 音色可用 ({len(audio_data)} 字节)")
        return True
    else:
        print(f"  ✗ 音色不可用")
        return False

def build_available_voice_list(config):
    """构建可用音色列表，不可用的用备用音色替换"""
    print("=== 构建可用音色列表 ===")
    
    available_voices = []
    backup_index = 0
    
    for voice_config in PRIMARY_VOICE_CONFIGS:
        if test_voice_availability(voice_config, config):
            available_voices.append(voice_config)
            print(f"✓ 添加主要音色: {voice_config['voice_code']} - {voice_config['name']}")
        else:
            # 尝试使用备用音色
            while backup_index < len(BACKUP_VOICE_CONFIGS):
                backup_voice = BACKUP_VOICE_CONFIGS[backup_index]
                backup_index += 1
                
                if test_voice_availability(backup_voice, config):
                    available_voices.append(backup_voice)
                    print(f"✓ 添加备用音色: {backup_voice['voice_code']} - {backup_voice['name']} (替换 {voice_config['voice_code']})")
                    break
            else:
                print(f"✗ 无可用备用音色替换 {voice_config['voice_code']}")
        
        time.sleep(0.5)  # 测试间隔
    
    print(f"\n最终可用音色数: {len(available_voices)}")
    return available_voices

def load_text_files_range(config):
    """加载指定范围的文本和拼音文件"""
    print("=== 加载指定范围的文本文件 ===")
    
    start_idx = config["START_INDEX"]
    end_idx = config["END_INDEX"]
    
    print(f"目标范围: 第 {start_idx} 到第 {end_idx-1} 条文本")
    
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            all_texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件总行数: {len(all_texts)}")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    all_pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                all_pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件总行数: {len(all_pinyins)}")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    if start_idx >= len(all_texts) or end_idx > len(all_texts):
        print(f"✗ 范围超出文件长度: 文件有 {len(all_texts)} 行，请求范围 {start_idx}-{end_idx}")
        return None, None
    
    range_texts = all_texts[start_idx:end_idx]
    range_pinyins = all_pinyins[start_idx:end_idx] if len(all_pinyins) >= end_idx else []
    
    print(f"✓ 提取范围文本: {len(range_texts)} 条")
    print(f"✓ 提取范围拼音: {len(range_pinyins)} 条")
    
    return range_texts, range_pinyins

def allocate_texts_to_voices(texts, available_voices, target_per_voice):
    """
    为每个音色分配不重复的文本
    确保文本和音色都不重复
    """
    print("=== 为每个音色分配文本 ===")
    
    total_needed = len(available_voices) * target_per_voice
    total_available = len(texts)
    
    print(f"需要文本总数: {total_needed}")
    print(f"可用文本总数: {total_available}")
    
    if total_needed > total_available:
        print(f"⚠️ 文本不足，将按比例分配")
        actual_per_voice = total_available // len(available_voices)
        print(f"实际每音色分配: {actual_per_voice} 条")
    else:
        actual_per_voice = target_per_voice
    
    # 打乱文本顺序
    shuffled_indices = list(range(len(texts)))
    random.shuffle(shuffled_indices)
    
    voice_text_allocation = {}
    used_indices = set()
    
    for i, voice_config in enumerate(available_voices):
        voice_code = voice_config['voice_code']
        
        # 为当前音色分配文本
        allocated_indices = []
        for idx in shuffled_indices:
            if idx not in used_indices and len(allocated_indices) < actual_per_voice:
                allocated_indices.append(idx)
                used_indices.add(idx)
        
        voice_text_allocation[voice_code] = {
            'voice_config': voice_config,
            'text_indices': allocated_indices,
            'texts': [texts[idx] for idx in allocated_indices]
        }
        
        print(f"音色 {voice_code}: 分配 {len(allocated_indices)} 条文本")
    
    print(f"总计分配文本: {len(used_indices)} 条")
    return voice_text_allocation

def generate_voice_batch_smart(voice_code, allocation, pinyins, config, batch_num):
    """为单个音色生成音频（智能版）"""
    voice_config = allocation['voice_config']
    text_indices = allocation['text_indices']
    texts = allocation['texts']
    
    voice_type = voice_config["voice_type"]
    voice_name = voice_config["name"]
    
    print(f"\n=== 批次 {batch_num}: {voice_name} ({voice_code}) ===")
    print(f"音色类型: {voice_type}")
    print(f"分配文本数: {len(texts)} 条")
    
    if not texts:
        print("✗ 没有分配到文本")
        return False, 0
    
    # 创建TTS对象
    tts = DouyinTTSSmart(config)
    
    success_count = 0
    error_count = 0
    start_time = time.time()
    last_processed_index = 0
    
    # 打开CSV文件进行追加
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    csv_exists = os.path.exists(csv_path)
    
    with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile, delimiter='\t')
        
        if not csv_exists:
            writer.writerow(['音频名', '类型', '文本', '注音1', '音色'])
        
        for i, (text_idx, text) in enumerate(zip(text_indices, texts), 1):
            # 获取对应的拼音
            pinyin = ""
            if text_idx < len(pinyins):
                pinyin = pinyins[text_idx]
            
            # 音频文件名格式：SaD0050000002
            original_idx = config["START_INDEX"] + text_idx
            audio_name = f"Sa{voice_code}{original_idx:07d}"
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")
            
            print(f"[{i}/{len(texts)}] {audio_name}: {text[:30]}{'...' if len(text) > 30 else ''}")
            
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                success_count += 1
                last_processed_index = original_idx
                continue

            # 生成音频
            audio_data = None
            for retry in range(3):
                audio_data = tts.synthesize_text(text, voice_type)
                if audio_data:
                    break
                if retry < 2:
                    print(f"  重试 {retry + 1}/3...")
                    time.sleep(2)

            if audio_data:
                try:
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)

                    writer.writerow([audio_name, 'c', text, pinyin, voice_name])
                    csvfile.flush()

                    print(f"  ✓ 成功 ({len(audio_data)} 字节)")
                    success_count += 1
                    last_processed_index = original_idx

                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1

            time.sleep(config["REQUEST_INTERVAL"])
            
            # 每10个显示进度
            if i % 10 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(texts) - i) * avg_time
                print(f"  进度: {i}/{len(texts)}, 预计剩余: {remaining/60:.1f}分钟")
    
    elapsed_time = time.time() - start_time
    print(f"\n批次 {batch_num} 完成:")
    print(f"  耗时: {elapsed_time/60:.1f}分钟")
    print(f"  成功: {success_count}")
    print(f"  失败: {error_count}")
    print(f"  最后处理索引: {last_processed_index}")
    print(f"  成功率: {(success_count/(success_count+error_count)*100):.1f}%" if success_count+error_count > 0 else "0%")
    
    return success_count > 0, last_processed_index

def main():
    """主函数 - 智能音色版"""
    print("=" * 60)
    print("豆包语音合成大模型 - 智能音色版")
    print("智能音色替换 + 文本不重复分配")
    print("=" * 60)

    config = DOUYIN_CONFIG

    print("配置信息:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  输出目录: {config['OUTPUT_DIR']}")
    print(f"  生成范围: 第 {config['START_INDEX']} 到第 {config['END_INDEX']-1} 条")
    print(f"  每音色文本数: {config['TARGET_TEXTS_PER_VOICE']} 条")

    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return

    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)

    # 1. 构建可用音色列表
    available_voices = build_available_voice_list(config)
    if not available_voices:
        print("✗ 没有可用的音色")
        return

    # 2. 加载文本
    texts, pinyins = load_text_files_range(config)
    if texts is None:
        return

    # 3. 为每个音色分配不重复的文本
    voice_text_allocation = allocate_texts_to_voices(texts, available_voices, config['TARGET_TEXTS_PER_VOICE'])

    # 4. 初始化CSV文件
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    if not os.path.exists(csv_path):
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile, delimiter='\t')
            writer.writerow(['音频名', '类型', '文本', '注音1', '音色'])
        print(f"✓ 创建CSV文件: {csv_path}")

    # 5. 开始分批生成
    print(f"\n=== 开始分批生成 ===")
    
    successful_voices = 0
    failed_voices = 0
    total_generated = 0
    last_global_index = config['START_INDEX']

    for i, (voice_code, allocation) in enumerate(voice_text_allocation.items(), 1):
        success, last_index = generate_voice_batch_smart(voice_code, allocation, pinyins, config, i)
        if success:
            successful_voices += 1
            total_generated += len(allocation['texts'])
            last_global_index = max(last_global_index, last_index)
            print(f"✓ {allocation['voice_config']['name']} 生成成功")
        else:
            failed_voices += 1
            print(f"✗ {allocation['voice_config']['name']} 生成失败")
        
        if i < len(voice_text_allocation):
            print(f"等待 {config['REQUEST_INTERVAL']} 秒后处理下一个音色...")
            time.sleep(config['REQUEST_INTERVAL'])

    # 6. 最终统计
    print(f"\n=== 最终统计 ===")
    print(f"成功音色: {successful_voices}/{len(available_voices)}")
    print(f"失败音色: {failed_voices}/{len(available_voices)}")
    print(f"实际生成音频: {total_generated} 个")
    print(f"最后处理的文本索引: {last_global_index}")
    print(f"输出目录: {config['OUTPUT_DIR']}")
    
    # 检查是否完成所有文本
    total_texts = config['END_INDEX'] - config['START_INDEX']
    if total_generated < total_texts:
        remaining = total_texts - total_generated
        print(f"⚠️ 还有 {remaining} 条文本未处理")
        print(f"下次可从索引 {last_global_index + 1} 开始")
    else:
        print(f"✓ 所有文本处理完成")
    
    print("=" * 60)

if __name__ == "__main__":
    print("\n🚀 开始运行豆包TTS智能音色版...")
    main()
    print("\n🎉 智能音色版运行完成！")
