#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包TTS多音色测试版 - 测试不同音色的可用性
"""

import requests
import base64
import time
import csv
import os
import uuid
import json
from datetime import datetime

# ==================== 测试配置 ====================
TEST_CONFIG = {
    # API配置
    "APPID": "7260176315",
    "ACCESS_TOKEN": "YyrytiGxJvJn3GuN2XwMRfv6ysivcaNf",
    "CLUSTER": "volcano_tts",
    
    # 音频格式配置
    "ENCODING": "wav",
    "SAMPLE_RATE": 16000,
    "SPEED_RATIO": 1.0,
    "VOLUME_RATIO": 1.0,
    "PITCH_RATIO": 1.0,
    
    # 文件配置
    "TEXT_FILE": "../shuffled_from_rank_random.txt",
    "PINYIN_FILE": "../shuffled_from_rank_random_pinyin.txt",
    "OUTPUT_DIR": "audio_output",
    "CSV_FILE": "test_multi_voice_audio_info.csv",
    "REQUEST_INTERVAL": 1.0,  # 测试时稍微慢一点
}

# 测试音色配置 - 每种音色测试1个文件
TEST_VOICES = [
    {"voice_type": "BV001_streaming", "name": "通用女声", "test_index": 8001},
    {"voice_type": "BV002_streaming", "name": "通用男声", "test_index": 8002},
    {"voice_type": "BV701_streaming", "name": "擎苍", "test_index": 8003},
    {"voice_type": "BV119_streaming", "name": "通用赘婿", "test_index": 8004},
    {"voice_type": "BV102_streaming", "name": "儒雅青年", "test_index": 8005},
    {"voice_type": "BV113_streaming", "name": "甜宠少御", "test_index": 8006},
    {"voice_type": "BV115_streaming", "name": "古风少御", "test_index": 8007},
    {"voice_type": "BV007_streaming", "name": "亲切女声", "test_index": 8008},
    {"voice_type": "BV056_streaming", "name": "阳光男声", "test_index": 8009},
    {"voice_type": "BV005_streaming", "name": "活泼女声", "test_index": 8010},
]

# API配置
HOST = "openspeech.bytedance.com"
API_URL = f"https://{HOST}/api/v1/tts"

class DouyinTTSTest:
    """豆包TTS测试类"""
    
    def __init__(self, config):
        self.config = config
        self.header = {"Authorization": f"Bearer;{config['ACCESS_TOKEN']}"}
        
    def synthesize_text(self, text, voice_type):
        """合成单个文本"""
        request_json = {
            "app": {
                "appid": self.config["APPID"],
                "token": "access_token",
                "cluster": self.config["CLUSTER"]
            },
            "user": {
                "uid": "388808087185088"
            },
            "audio": {
                "voice_type": voice_type,
                "encoding": self.config["ENCODING"],
                "sample_rate": self.config["SAMPLE_RATE"],
                "speed_ratio": self.config["SPEED_RATIO"],
                "volume_ratio": self.config["VOLUME_RATIO"],
                "pitch_ratio": self.config["PITCH_RATIO"],
            },
            "request": {
                "reqid": str(uuid.uuid4()),
                "text": text,
                "text_type": "plain",
                "operation": "query",
                "with_frontend": 1,
                "frontend_type": "unitTson"
            }
        }
        
        try:
            resp = requests.post(API_URL, json.dumps(request_json), headers=self.header, timeout=30)
            
            if resp.status_code == 200:
                resp_data = resp.json()
                if "data" in resp_data:
                    return base64.b64decode(resp_data["data"])
                else:
                    print(f"  ✗ API响应无数据: {resp_data}")
                    return None
            else:
                print(f"  ✗ HTTP错误 {resp.status_code}: {resp.text}")
                return None
                
        except requests.exceptions.Timeout:
            print(f"  ✗ 请求超时")
            return None
        except Exception as e:
            print(f"  ✗ 请求异常: {e}")
            return None

def load_test_data(config):
    """加载测试数据"""
    print("=== 加载测试数据 ===")
    
    # 读取文本文件
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    return texts, pinyins

def test_voice_generation(config):
    """测试多音色生成"""
    print("=== 开始多音色测试 ===")
    print(f"测试10种音色，每种生成1个文件")
    print("=" * 50)
    
    # 1. 加载数据
    texts, pinyins = load_test_data(config)
    if texts is None:
        return False
    
    # 2. 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)
    
    # 3. 初始化TTS客户端
    tts = DouyinTTSTest(config)
    
    # 4. 测试每种音色
    success_count = 0
    error_count = 0
    
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    
    print(f"开始测试音色...")
    start_time = time.time()
    
    # 写入CSV记录
    with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        csvfile.write('音频名\t类型\t文本\t注音1\t音色\n')
    
        for i, voice in enumerate(TEST_VOICES, 1):
            voice_type = voice["voice_type"]
            voice_name = voice["name"]
            test_index = voice["test_index"]
            
            text_index = test_index - 1  # 转换为数组索引
            
            if text_index >= len(texts):
                print(f"[{i}/10] {voice_name}: 索引超出范围，跳过")
                continue
            
            text = texts[text_index]
            pinyin = pinyins[text_index] if text_index < len(pinyins) else ""
            audio_name = f"Sc{test_index:07d}"
            
            print(f"[{i}/10] {voice_name} ({voice_type})")
            print(f"  测试文本: {text[:40]}{'...' if len(text) > 40 else ''}")
            
            # 检查文件是否已存在
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}_{voice_name}.wav")
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                continue
            
            # 生成音频
            audio_data = tts.synthesize_text(text, voice_type)
            if audio_data:
                try:
                    # 保存音频文件（文件名包含音色信息）
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)
                    
                    # 写入CSV记录
                    csvfile.write(f"{audio_name}\tc\t{text}\t{pinyin}\t{voice_name}\n")
                    csvfile.flush()
                    
                    print(f"  ✓ 成功 ({len(audio_data)} 字节) -> {audio_name}_{voice_name}.wav")
                    success_count += 1
                    
                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1
            
            # 请求间隔
            time.sleep(config["REQUEST_INTERVAL"])
    
    elapsed_time = time.time() - start_time
    print(f"\n=== 测试完成 ===")
    print(f"耗时: {elapsed_time:.1f}秒")
    print(f"成功: {success_count}/10")
    print(f"失败: {error_count}/10")
    print(f"CSV记录: {csv_path}")
    
    # 显示测试结果
    print(f"\n=== 音色测试结果 ===")
    for voice in TEST_VOICES:
        voice_name = voice["name"]
        test_index = voice["test_index"]
        audio_name = f"Sc{test_index:07d}"
        audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}_{voice_name}.wav")
        
        if os.path.exists(audio_path):
            file_size = os.path.getsize(audio_path)
            print(f"✓ {voice_name}: 成功 ({file_size} 字节)")
        else:
            print(f"✗ {voice_name}: 失败")
    
    return success_count > 0

def main():
    """主函数"""
    print("=" * 60)
    print("豆包TTS多音色测试程序")
    print("=" * 60)
    
    config = TEST_CONFIG
    
    print("测试配置:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  拼音文件: {config['PINYIN_FILE']}")
    print(f"  输出目录: {config['OUTPUT_DIR']}")
    print(f"  测试音色: 10种")
    
    print("\n测试音色列表:")
    for i, voice in enumerate(TEST_VOICES, 1):
        print(f"  {i}. {voice['name']} ({voice['voice_type']})")
    
    print("=" * 60)
    
    # 检查文件
    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return
    
    if not os.path.exists(config['PINYIN_FILE']):
        print(f"✗ 拼音文件不存在: {config['PINYIN_FILE']}")
        return
    
    # 运行测试
    success = test_voice_generation(config)
    
    if success:
        print("\n🎉 多音色测试通过！")
        print("可以运行完整版本进行批量生成第8001-10000条音频")
    else:
        print("\n⚠️ 多音色测试失败，请检查配置和网络")

if __name__ == "__main__":
    main()
