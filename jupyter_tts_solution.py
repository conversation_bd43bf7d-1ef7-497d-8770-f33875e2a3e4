#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Jupyter环境TTS解决方案
包含豆包TTS和多种备选方案
"""

import asyncio
import json
import logging
import uuid
import os
import time
import pandas as pd
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TTSEngineBase:
    """TTS引擎基类"""
    def __init__(self, name):
        self.name = name
        self.success_count = 0
        self.error_count = 0
    
    async def synthesize(self, text, voice, output_path, **kwargs):
        """合成音频的抽象方法"""
        raise NotImplementedError
    
    def get_stats(self):
        """获取统计信息"""
        total = self.success_count + self.error_count
        success_rate = (self.success_count / total * 100) if total > 0 else 0
        return {
            "engine": self.name,
            "success": self.success_count,
            "error": self.error_count,
            "total": total,
            "success_rate": success_rate
        }

class EdgeTTSEngine(TTSEngineBase):
    """微软Edge TTS引擎"""
    
    def __init__(self):
        super().__init__("Edge TTS")
        self.voices = {
            "D001": "zh-CN-XiaoxiaoNeural",  # 晓晓（女）
            "D002": "zh-CN-YunxiNeural",     # 云希（男）
            "D003": "zh-CN-XiaoyiNeural",    # 晓伊（女）
            "D004": "zh-CN-YunyangNeural",   # 云扬（男）
            "D005": "zh-CN-XiaohanNeural",   # 晓涵（女）
            "D006": "zh-CN-XiaomengNeural",  # 晓梦（女）
            "D007": "zh-CN-XiaomoNeural",    # 晓墨（女）
            "D008": "zh-CN-XiaoqiuNeural",   # 晓秋（女）
            "D009": "zh-CN-XiaoruiNeural",   # 晓睿（女）
            "D010": "zh-CN-XiaoshuangNeural", # 晓双（女）
        }
    
    async def synthesize(self, text, voice_code, output_path, speed=1.0):
        """使用Edge TTS合成音频"""
        try:
            import edge_tts
            
            # 获取对应的Edge音色
            edge_voice = self.voices.get(voice_code, "zh-CN-XiaoxiaoNeural")
            
            # 调整语速
            if speed != 1.0:
                rate = f"{(speed - 1) * 100:+.0f}%"
                communicate = edge_tts.Communicate(text, edge_voice, rate=rate)
            else:
                communicate = edge_tts.Communicate(text, edge_voice)
            
            await communicate.save(output_path)
            
            self.success_count += 1
            return True
            
        except ImportError:
            logger.error("Edge TTS未安装，请运行: pip install edge-tts")
            self.error_count += 1
            return False
        except Exception as e:
            logger.error(f"Edge TTS合成失败: {e}")
            self.error_count += 1
            return False

class DouyinTTSEngine(TTSEngineBase):
    """豆包TTS引擎（保留原有逻辑）"""
    
    def __init__(self, appid, access_token):
        super().__init__("豆包TTS")
        self.appid = appid
        self.access_token = access_token
        self.endpoint = "wss://openspeech.bytedance.com/api/v1/tts/ws_binary"
        self.voices = {
            "D001": "BV001_streaming",
            "D002": "BV002_streaming", 
            "D003": "BV007_streaming",
            "D004": "BV056_streaming",
            "D005": "BV005_streaming",
        }
    
    def get_cluster(self, voice_type):
        if voice_type.startswith("S_"):
            return "volcano_icl"
        return "volcano_tts"
    
    async def synthesize(self, text, voice_code, output_path, speed=1.0):
        """使用豆包TTS合成音频"""
        try:
            import websockets
            from protocols import MsgType, full_client_request, receive_message
            
            voice_type = self.voices.get(voice_code, "BV001_streaming")
            headers = {"Authorization": f"Bearer;{self.access_token}"}
            
            websocket = await websockets.connect(
                self.endpoint, 
                additional_headers=headers, 
                max_size=10 * 1024 * 1024
            )
            
            request = {
                "app": {
                    "appid": self.appid,
                    "token": self.access_token,
                    "cluster": self.get_cluster(voice_type),
                },
                "user": {"uid": str(uuid.uuid4())},
                "audio": {
                    "voice_type": voice_type,
                    "encoding": "wav",
                    "speed_ratio": speed,
                },
                "request": {
                    "reqid": str(uuid.uuid4()),
                    "text": text,
                    "operation": "submit",
                },
            }
            
            await full_client_request(websocket, json.dumps(request).encode())
            
            audio_data = bytearray()
            while True:
                msg = await receive_message(websocket)
                
                if msg.type == MsgType.FrontEndResultServer:
                    continue
                elif msg.type == MsgType.AudioOnlyServer:
                    audio_data.extend(msg.payload)
                    if msg.sequence < 0:
                        break
                elif msg.type == MsgType.ErrorServer:
                    error_msg = msg.payload.decode('utf-8', errors='ignore')
                    raise RuntimeError(f"服务器错误: {error_msg}")
                else:
                    raise RuntimeError(f"未知消息类型: {msg.type}")
            
            await websocket.close()
            
            if not audio_data:
                raise RuntimeError("未收到音频数据")
            
            with open(output_path, "wb") as f:
                f.write(audio_data)
            
            self.success_count += 1
            return True
            
        except Exception as e:
            logger.error(f"豆包TTS合成失败: {e}")
            self.error_count += 1
            return False

class MultiTTSGenerator:
    """多TTS引擎生成器"""
    
    def __init__(self, output_dir="audio_multi_tts"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 初始化引擎
        self.engines = {
            "edge": EdgeTTSEngine(),
            "douyin": DouyinTTSEngine("9862368305", "bbxNaAzRzBSkySjouDItXl6tBUUyNd2W")
        }
        
        self.current_engine = "edge"  # 默认使用Edge TTS
        
    def switch_engine(self, engine_name):
        """切换TTS引擎"""
        if engine_name in self.engines:
            self.current_engine = engine_name
            print(f"✓ 切换到 {self.engines[engine_name].name}")
        else:
            print(f"✗ 未知引擎: {engine_name}")
    
    def list_engines(self):
        """列出可用引擎"""
        print("可用TTS引擎:")
        for name, engine in self.engines.items():
            status = "✓" if name == self.current_engine else " "
            print(f"  {status} {name}: {engine.name}")
    
    async def generate_batch(self, texts, voice_configs, speed=1.0, confirm_each=True):
        """批量生成音频"""
        engine = self.engines[self.current_engine]
        
        print(f"使用引擎: {engine.name}")
        print(f"文本数量: {len(texts)}")
        print(f"音色数量: {len(voice_configs)}")
        print(f"语速: {speed}x")
        
        # 创建CSV记录
        csv_path = self.output_dir / f"tts_log_{self.current_engine}.csv"
        records = []
        
        total_files = 0
        
        for voice_config in voice_configs:
            voice_code = voice_config["code"]
            voice_name = voice_config["name"]
            
            print(f"\n=== 音色: {voice_name} ({voice_code}) ===")
            
            if confirm_each:
                try:
                    confirm = input(f"生成 {voice_name}？(y/n): ").strip().lower()
                    if confirm not in ['y', 'yes', '是']:
                        print(f"跳过 {voice_name}")
                        continue
                except:
                    print(f"跳过 {voice_name}")
                    continue
            
            success_count = 0
            
            for i, text in enumerate(texts, 1):
                filename = f"Sc{voice_code}{i:07d}.wav"
                filepath = self.output_dir / filename
                
                print(f"[{i}/{len(texts)}] {text[:20]}{'...' if len(text) > 20 else ''}")
                
                # 检查文件是否已存在
                if filepath.exists():
                    print(f"  跳过已存在文件")
                    success_count += 1
                    continue
                
                # 合成音频
                success = await engine.synthesize(text, voice_code, str(filepath), speed=speed)
                
                if success:
                    print(f"  ✓ 成功")
                    success_count += 1
                    
                    # 记录到CSV
                    records.append({
                        "音频名": filename.replace('.wav', ''),
                        "类型": "c",
                        "文本": text,
                        "注音": "",
                        "音色": voice_name,
                        "引擎": engine.name
                    })
                else:
                    print(f"  ✗ 失败")
                
                # 请求间隔
                await asyncio.sleep(1.0)
                
                # 每10个显示进度
                if i % 10 == 0:
                    stats = engine.get_stats()
                    print(f"  进度: {i}/{len(texts)}, 成功率: {stats['success_rate']:.1f}%")
            
            total_files += success_count
            print(f"{voice_name} 完成: {success_count}/{len(texts)}")
            
            # 询问是否继续
            if confirm_each and voice_config != voice_configs[-1]:
                try:
                    continue_choice = input("继续下一个音色？(y/n): ").strip().lower()
                    if continue_choice not in ['y', 'yes', '是']:
                        break
                except:
                    break
        
        # 保存CSV记录
        if records:
            df = pd.DataFrame(records)
            df.to_csv(csv_path, index=False, encoding='utf-8-sig', sep='\t')
            print(f"\n✓ CSV记录保存: {csv_path}")
        
        # 最终统计
        stats = engine.get_stats()
        print(f"\n=== 生成完成 ===")
        print(f"引擎: {engine.name}")
        print(f"总文件: {total_files}")
        print(f"成功: {stats['success']}")
        print(f"失败: {stats['error']}")
        print(f"成功率: {stats['success_rate']:.1f}%")
        print(f"输出目录: {self.output_dir}")
        
        return stats

# Jupyter环境使用示例
async def jupyter_demo():
    """Jupyter环境演示"""
    print("=" * 60)
    print("Jupyter环境TTS生成演示")
    print("=" * 60)
    
    # 创建生成器
    generator = MultiTTSGenerator()
    
    # 列出可用引擎
    generator.list_engines()
    
    # 测试文本
    test_texts = [
        "你好",
        "测试",
        "中国",
        "北京",
        "上海",
        "广州",
        "深圳",
        "杭州",
        "成都",
        "西安"
    ]
    
    # 音色配置
    voice_configs = [
        {"code": "D001", "name": "音色1"},
        {"code": "D002", "name": "音色2"},
        {"code": "D003", "name": "音色3"},
    ]
    
    print(f"\n首先尝试Edge TTS（推荐）...")
    generator.switch_engine("edge")
    
    try:
        # 检查Edge TTS是否可用
        import edge_tts
        print("✓ Edge TTS可用")
        
        # 生成音频
        stats = await generator.generate_batch(
            texts=test_texts[:5],  # 只用前5个文本测试
            voice_configs=voice_configs[:2],  # 只用前2个音色测试
            speed=1.0,
            confirm_each=False  # Jupyter中不需要交互确认
        )
        
        if stats['success_rate'] >= 80:
            print("🎉 Edge TTS工作正常！")
        else:
            print("⚠️ Edge TTS有问题，尝试豆包TTS...")
            generator.switch_engine("douyin")
            await generator.generate_batch(
                texts=test_texts[:3],
                voice_configs=voice_configs[:1],
                speed=1.0,
                confirm_each=False
            )
            
    except ImportError:
        print("❌ Edge TTS未安装")
        print("请运行: !pip install edge-tts")
        
        print("\n尝试豆包TTS...")
        generator.switch_engine("douyin")
        await generator.generate_batch(
            texts=test_texts[:3],
            voice_configs=voice_configs[:1],
            speed=1.0,
            confirm_each=False
        )

# 在Jupyter中运行的函数
def run_in_jupyter():
    """在Jupyter中运行TTS生成"""
    # 检查是否在Jupyter环境中
    try:
        from IPython import get_ipython
        if get_ipython() is not None:
            print("检测到Jupyter环境")
            # 在Jupyter中运行异步函数
            import nest_asyncio
            nest_asyncio.apply()
            asyncio.run(jupyter_demo())
        else:
            print("非Jupyter环境，直接运行")
            asyncio.run(jupyter_demo())
    except ImportError:
        print("直接运行")
        asyncio.run(jupyter_demo())

if __name__ == "__main__":
    run_in_jupyter()
