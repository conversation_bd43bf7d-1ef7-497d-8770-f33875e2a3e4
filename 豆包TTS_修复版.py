#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包语音合成大模型 - 修复版
基于可用示例修复请求格式和音色配置
"""

import requests
import base64
import time
import csv
import os
import uuid
import json
import glob

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print("✅ 环境设置完成 - 修复版")

# ==================== 配置区域 ====================
DOUYIN_CONFIG = {
    # API配置
    "APPID": "1500263695",  # 使用您原来的APPID
    "ACCESS_TOKEN": "lsZJKq3sy6GngicgAcZN8mWQmqZ1cKGN",  # 使用您原来的TOKEN
    "CLUSTER": "volcano_tts",
    
    # 音频格式配置（按照可用示例格式）
    "ENCODING": "pcm",      # 修复：使用pcm而不是wav
    "RATE": "16000",        # 修复：使用rate而不是sample_rate，且为字符串
    "SPEED_RATIO": 1.0,
    "VOLUME_RATIO": 1.0,
    "PITCH_RATIO": 1.0,
    
    # 文件配置
    "TEXT_FILE": "text_with_English.txt",
    "PINYIN_FILE": "text_with_English_pinyin.txt",
    "OUTPUT_DIR": "audio_output_fixed",
    "CSV_FILE": "doubao_fixed_multi_voice_log.csv",
    "REQUEST_INTERVAL": 0.5,
    
    # 范围生成配置
    "START_INDEX": 251,
    "END_INDEX": 2000,
    "TARGET_TEXTS_PER_VOICE": 10,  # 先测试少量
}

# 使用可用的音色类型（基于您提供的示例格式）
VOICE_CONFIGS = [
    # 基于可用示例的音色格式
    {"voice_code": "M100", "voice_type": "zh_male_M100_conversation_wvae_bigtts", "name": "男声M100"},
    {"voice_code": "F100", "voice_type": "zh_female_F100_conversation_wvae_bigtts", "name": "女声F100"},
    {"voice_code": "M101", "voice_type": "zh_male_M101_conversation_wvae_bigtts", "name": "男声M101"},
    {"voice_code": "F101", "voice_type": "zh_female_F101_conversation_wvae_bigtts", "name": "女声F101"},
    {"voice_code": "M102", "voice_type": "zh_male_M102_conversation_wvae_bigtts", "name": "男声M102"},
    
    # 备用基础音色
    {"voice_code": "BV01", "voice_type": "BV001_streaming", "name": "基础女声1"},
    {"voice_code": "BV02", "voice_type": "BV002_streaming", "name": "基础男声1"},
    {"voice_code": "BV03", "voice_type": "BV003_streaming", "name": "基础女声2"},
    {"voice_code": "BV04", "voice_type": "BV004_streaming", "name": "基础男声2"},
    {"voice_code": "BV05", "voice_type": "BV005_streaming", "name": "基础女声3"},
]

# API配置
HOST = "openspeech.bytedance.com"
API_URL = f"https://{HOST}/api/v1/tts"

print("✅ 配置加载完成")

def get_cluster(voice_type):
    """获取集群名称"""
    if voice_type.startswith("ICL_"):
        return "volcano_icl"
    return "volcano_tts"

class DouyinTTSHTTPFixed:
    """豆包TTS HTTP API修复版核心类"""
    
    def __init__(self, config):
        self.config = config
        self.headers = {"Authorization": f"Bearer;{config['ACCESS_TOKEN']}"}
        self.success_count = 0
        self.error_count = 0
        
    def synthesize_text(self, text, voice_type):
        """使用HTTP API合成单个文本（修复版格式）"""
        # 确定集群
        cluster = get_cluster(voice_type)
        
        # 按照可用示例的格式构建请求
        request_json = {
            "app": {
                "appid": self.config["APPID"],
                "token": self.config["ACCESS_TOKEN"],  # 注意：这里用token而不是access_token
                "cluster": cluster
            },
            "user": {
                "uid": str(uuid.uuid4())  # 生成随机UID
            },
            "audio": {
                "voice_type": voice_type,
                "encoding": self.config["ENCODING"],    # pcm
                "rate": self.config["RATE"],            # "16000" (字符串)
                "speed_ratio": self.config["SPEED_RATIO"],
                "volume_ratio": self.config["VOLUME_RATIO"],
                "pitch_ratio": self.config["PITCH_RATIO"],
            },
            "request": {
                "reqid": str(uuid.uuid4()),
                "text": text,
                "text_type": "plain",
                "operation": "query",
                "with_frontend": 1,
                "frontend_type": "unitTson"
            }
        }
        
        try:
            resp = requests.post(API_URL, json.dumps(request_json), headers=self.headers, timeout=30)
            
            if resp.status_code == 200:
                resp_data = resp.json()
                if "data" in resp_data:
                    audio_data = base64.b64decode(resp_data["data"])
                    self.success_count += 1
                    return audio_data
                else:
                    print(f"  ✗ API响应无数据: {resp_data}")
                    self.error_count += 1
                    return None
            else:
                try:
                    error_data = resp.json()
                    print(f"  ✗ HTTP错误 {resp.status_code}: {error_data}")
                except:
                    print(f"  ✗ HTTP错误 {resp.status_code}: {resp.text}")
                self.error_count += 1
                return None
                
        except requests.exceptions.Timeout:
            print(f"  ✗ 请求超时")
            self.error_count += 1
            return None
        except Exception as e:
            print(f"  ✗ 请求异常: {e}")
            self.error_count += 1
            return None

def test_voice_availability():
    """测试所有配置的音色是否可用"""
    print("=== 测试音色可用性 ===")
    
    config = DOUYIN_CONFIG
    tts = DouyinTTSHTTPFixed(config)
    test_text = "你好"
    
    available_voices = []
    unavailable_voices = []
    
    for voice_config in VOICE_CONFIGS:
        voice_name = voice_config["name"]
        voice_type = voice_config["voice_type"]
        
        print(f"测试: {voice_name} ({voice_type})")
        
        audio_data = tts.synthesize_text(test_text, voice_type)
        
        if audio_data:
            print(f"  ✓ 可用 ({len(audio_data)} 字节)")
            available_voices.append(voice_config)
        else:
            print(f"  ✗ 不可用")
            unavailable_voices.append(voice_config)
        
        time.sleep(0.5)  # 避免请求过快
    
    print(f"\n测试结果:")
    print(f"  可用音色: {len(available_voices)} 个")
    print(f"  不可用音色: {len(unavailable_voices)} 个")
    
    if available_voices:
        print(f"\n✅ 可用音色列表:")
        for voice in available_voices:
            print(f"  - {voice['name']}: {voice['voice_type']}")
    
    if unavailable_voices:
        print(f"\n❌ 不可用音色列表:")
        for voice in unavailable_voices:
            print(f"  - {voice['name']}: {voice['voice_type']}")
    
    return available_voices

def load_text_files_range(config):
    """加载指定范围的文本和拼音文件"""
    print("=== 加载指定范围的文本文件 ===")
    
    start_idx = config["START_INDEX"]
    end_idx = config["END_INDEX"]
    
    print(f"目标范围: 第 {start_idx} 到第 {end_idx-1} 条文本")
    
    # 读取文本文件
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            all_texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件总行数: {len(all_texts)}")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    all_pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                all_pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件总行数: {len(all_pinyins)}")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    # 检查范围有效性
    if start_idx >= len(all_texts) or end_idx > len(all_texts):
        print(f"✗ 范围超出文件长度: 文件有 {len(all_texts)} 行，请求范围 {start_idx}-{end_idx}")
        return None, None
    
    if start_idx >= end_idx:
        print(f"✗ 无效范围: 开始索引 {start_idx} >= 结束索引 {end_idx}")
        return None, None
    
    # 提取指定范围
    range_texts = all_texts[start_idx:end_idx]
    range_pinyins = all_pinyins[start_idx:end_idx] if len(all_pinyins) >= end_idx else []
    
    print(f"✓ 提取范围文本: {len(range_texts)} 条")
    print(f"✓ 提取范围拼音: {len(range_pinyins)} 条")
    
    return range_texts, range_pinyins

def generate_with_available_voices(available_voices, texts, pinyins, config):
    """使用可用音色生成音频"""
    if not available_voices:
        print("✗ 没有可用的音色")
        return
    
    print(f"\n=== 使用 {len(available_voices)} 个可用音色生成音频 ===")
    
    # 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)
    
    # 初始化CSV文件
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile, delimiter='\t')
        writer.writerow(['音频名', '类型', '文本', '注音1', '音色'])
    
    # 为每个可用音色生成音频
    tts = DouyinTTSHTTPFixed(config)
    target_count = config["TARGET_TEXTS_PER_VOICE"]
    
    for i, voice_config in enumerate(available_voices, 1):
        voice_name = voice_config["name"]
        voice_type = voice_config["voice_type"]
        voice_code = voice_config["voice_code"]
        
        print(f"\n--- 音色 {i}: {voice_name} ---")
        
        # 选择文本（取前N个）
        selected_texts = texts[:target_count]
        selected_pinyins = pinyins[:target_count] if pinyins else [""] * target_count
        
        success_count = 0
        
        with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile, delimiter='\t')
            
            for j, (text, pinyin) in enumerate(zip(selected_texts, selected_pinyins), 1):
                audio_name = f"Fx{voice_code}{j:07d}"
                audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")
                
                print(f"[{j}/{len(selected_texts)}] {audio_name}: {text[:20]}...")
                
                # 生成音频
                audio_data = tts.synthesize_text(text, voice_type)
                
                if audio_data:
                    try:
                        # 保存为WAV文件（即使原始是PCM）
                        with open(audio_path, "wb") as f:
                            f.write(audio_data)
                        
                        # 写入CSV记录
                        writer.writerow([audio_name, 'c', text, pinyin, voice_name])
                        
                        print(f"  ✓ 成功 ({len(audio_data)} 字节)")
                        success_count += 1
                        
                    except Exception as e:
                        print(f"  ✗ 保存失败: {e}")
                else:
                    print(f"  ✗ 生成失败")
                
                time.sleep(config["REQUEST_INTERVAL"])
        
        print(f"音色 {voice_name} 完成: {success_count}/{len(selected_texts)} 成功")

def main():
    """主函数 - 修复版"""
    print("=" * 60)
    print("豆包语音合成大模型 - 修复版")
    print("基于可用示例修复请求格式")
    print("=" * 60)
    
    config = DOUYIN_CONFIG
    
    print("配置信息:")
    print(f"  APPID: {config['APPID']}")
    print(f"  编码格式: {config['ENCODING']}")
    print(f"  采样率: {config['RATE']}")
    print(f"  输出目录: {config['OUTPUT_DIR']}")
    
    # 1. 测试音色可用性
    available_voices = test_voice_availability()
    
    if not available_voices:
        print("\n❌ 没有找到可用的音色，请检查配置")
        return
    
    # 2. 加载文本
    texts, pinyins = load_text_files_range(config)
    if texts is None:
        return
    
    # 3. 使用可用音色生成音频
    generate_with_available_voices(available_voices, texts, pinyins, config)
    
    print(f"\n🎉 修复版运行完成！")

if __name__ == "__main__":
    print("\n🚀 开始运行豆包TTS修复版...")
    main()
