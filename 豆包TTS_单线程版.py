#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包TTS单线程调用版本
基于可用示例格式的简化单线程实现
"""

import requests
import base64
import time
import uuid
import json
import os

# ==================== 配置区域 ====================
CONFIG = {
    # API配置
    "APPID": "1500263695",
    "ACCESS_TOKEN": "lsZJKq3sy6GngicgAcZN8mWQmqZ1cKGN",
    "CLUSTER": "volcano_tts",
    
    # 音色配置
    "VOICE_TYPE": "ICL_zh_female_zhixingwenwan_tob",
    
    # 音频格式配置
    "ENCODING": "wav",
    "RATE": "16000",
    "SPEED_RATIO": 1.0,
    "VOLUME_RATIO": 1.0,
    "PITCH_RATIO": 1.0,
    
    # 输出配置
    "OUTPUT_DIR": "single_thread_output",
    "REQUEST_INTERVAL": 0.5,  # 请求间隔（秒）
}

# API端点
API_URL = "https://openspeech.bytedance.com/api/v1/tts"

class DouyinTTSSingle:
    """豆包TTS单线程调用类"""
    
    def __init__(self, config):
        self.config = config
        self.headers = {"Authorization": f"Bearer;{config['ACCESS_TOKEN']}"}
        self.success_count = 0
        self.error_count = 0
    
    def synthesize_text(self, text, output_filename=None):
        """
        合成单个文本为音频
        
        Args:
            text: 要合成的文本
            output_filename: 输出文件名（可选）
            
        Returns:
            tuple: (success, audio_data, message)
        """
        print(f"正在合成: {text[:30]}{'...' if len(text) > 30 else ''}")
        
        # 构建请求（按照可用示例格式）
        request_json = {
            "app": {
                "appid": self.config["APPID"],
                "token": self.config["ACCESS_TOKEN"],
                "cluster": self.config["CLUSTER"]
            },
            "user": {
                "uid": str(uuid.uuid4())
            },
            "audio": {
                "voice_type": self.config["VOICE_TYPE"],
                "encoding": self.config["ENCODING"],
                "rate": self.config["RATE"],
                "speed_ratio": self.config["SPEED_RATIO"],
                "volume_ratio": self.config["VOLUME_RATIO"],
                "pitch_ratio": self.config["PITCH_RATIO"],
            },
            "request": {
                "reqid": str(uuid.uuid4()),
                "text": text,
                "text_type": "plain",
                "operation": "query",
                "with_frontend": 1,
                "frontend_type": "unitTson"
            }
        }
        
        try:
            # 发送请求
            response = requests.post(
                API_URL, 
                data=json.dumps(request_json), 
                headers=self.headers, 
                timeout=30
            )
            
            if response.status_code == 200:
                resp_data = response.json()
                
                if "data" in resp_data:
                    # 解码音频数据
                    audio_data = base64.b64decode(resp_data["data"])
                    self.success_count += 1
                    
                    # 如果指定了输出文件名，则保存
                    if output_filename:
                        self.save_audio(audio_data, output_filename)
                    
                    print(f"  ✓ 合成成功 ({len(audio_data)} 字节)")
                    return True, audio_data, "合成成功"
                else:
                    error_msg = f"API响应无数据: {resp_data}"
                    print(f"  ✗ {error_msg}")
                    self.error_count += 1
                    return False, None, error_msg
            else:
                try:
                    error_data = response.json()
                    error_msg = f"HTTP {response.status_code}: {error_data}"
                except:
                    error_msg = f"HTTP {response.status_code}: {response.text}"
                
                print(f"  ✗ {error_msg}")
                self.error_count += 1
                return False, None, error_msg
                
        except requests.exceptions.Timeout:
            error_msg = "请求超时"
            print(f"  ✗ {error_msg}")
            self.error_count += 1
            return False, None, error_msg
            
        except Exception as e:
            error_msg = f"请求异常: {e}"
            print(f"  ✗ {error_msg}")
            self.error_count += 1
            return False, None, error_msg
    
    def save_audio(self, audio_data, filename):
        """保存音频数据到文件"""
        try:
            # 确保输出目录存在
            os.makedirs(self.config["OUTPUT_DIR"], exist_ok=True)
            
            # 构建完整路径
            if not filename.endswith('.wav'):
                filename += '.wav'
            
            filepath = os.path.join(self.config["OUTPUT_DIR"], filename)
            
            # 保存音频文件
            with open(filepath, 'wb') as f:
                f.write(audio_data)
            
            print(f"  ✓ 音频已保存: {filepath}")
            return True
            
        except Exception as e:
            print(f"  ✗ 保存失败: {e}")
            return False
    
    def batch_synthesize(self, texts, output_prefix="audio"):
        """
        批量合成文本列表
        
        Args:
            texts: 文本列表
            output_prefix: 输出文件前缀
            
        Returns:
            dict: 统计信息
        """
        print(f"开始批量合成 {len(texts)} 个文本...")
        
        results = []
        start_time = time.time()
        
        for i, text in enumerate(texts, 1):
            print(f"\n[{i}/{len(texts)}]", end=" ")
            
            # 生成输出文件名
            output_filename = f"{output_prefix}_{i:04d}.wav"
            
            # 合成音频
            success, audio_data, message = self.synthesize_text(text, output_filename)
            
            results.append({
                "index": i,
                "text": text,
                "success": success,
                "message": message,
                "audio_size": len(audio_data) if audio_data else 0
            })
            
            # 请求间隔
            if i < len(texts):  # 最后一个不需要等待
                time.sleep(self.config["REQUEST_INTERVAL"])
        
        # 统计信息
        elapsed_time = time.time() - start_time
        success_count = sum(1 for r in results if r["success"])
        
        print(f"\n" + "="*50)
        print(f"批量合成完成:")
        print(f"  总数: {len(texts)}")
        print(f"  成功: {success_count}")
        print(f"  失败: {len(texts) - success_count}")
        print(f"  成功率: {success_count/len(texts)*100:.1f}%")
        print(f"  总耗时: {elapsed_time:.1f}秒")
        print(f"  平均耗时: {elapsed_time/len(texts):.1f}秒/个")
        print(f"  输出目录: {self.config['OUTPUT_DIR']}")
        print("="*50)
        
        return {
            "total": len(texts),
            "success": success_count,
            "failed": len(texts) - success_count,
            "success_rate": success_count/len(texts)*100,
            "elapsed_time": elapsed_time,
            "results": results
        }

def test_single_synthesis():
    """测试单个文本合成"""
    print("=== 测试单个文本合成 ===")
    
    tts = DouyinTTSSingle(CONFIG)
    
    # 测试文本
    test_text = "你好，这是一个测试文本。"
    
    success, audio_data, message = tts.synthesize_text(test_text, "test_single.wav")
    
    if success:
        print(f"✓ 单个合成测试成功")
        print(f"  音频大小: {len(audio_data)} 字节")
    else:
        print(f"✗ 单个合成测试失败: {message}")
    
    return success

def test_batch_synthesis():
    """测试批量文本合成"""
    print("\n=== 测试批量文本合成 ===")
    
    tts = DouyinTTSSingle(CONFIG)
    
    # 测试文本列表
    test_texts = [
        "你好世界"
    ]
    
    # 批量合成
    stats = tts.batch_synthesize(test_texts, "batch_test")
    
    return stats["success"] > 0

def load_texts_from_file(filename):
    """从文件加载文本列表"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 从文件加载了 {len(texts)} 行文本")
        return texts
    except Exception as e:
        print(f"✗ 加载文件失败: {e}")
        return []

def main():
    """主函数"""
    print("=" * 60)
    print("豆包TTS单线程调用版本")
    print("=" * 60)
    
    print("配置信息:")
    print(f"  APPID: {CONFIG['APPID']}")
    print(f"  音色: {CONFIG['VOICE_TYPE']}")
    print(f"  编码: {CONFIG['ENCODING']}")
    print(f"  采样率: {CONFIG['RATE']}")
    print(f"  输出目录: {CONFIG['OUTPUT_DIR']}")
    print(f"  请求间隔: {CONFIG['REQUEST_INTERVAL']}秒")
    
    # 1. 测试单个合成
    if not test_single_synthesis():
        print("❌ 单个合成测试失败，请检查配置")
        return
    
    # 2. 测试批量合成
    if not test_batch_synthesis():
        print("❌ 批量合成测试失败")
        return
    
    # 3. 可选：从文件批量合成
    text_file = "text_with_English.txt"  # 您的文本文件
    if os.path.exists(text_file):
        print(f"\n=== 从文件批量合成: {text_file} ===")
        
        texts = load_texts_from_file(text_file)
        if texts:
            # 限制数量避免过多请求
            max_texts = 20
            if len(texts) > max_texts:
                texts = texts[:max_texts]
                print(f"限制处理前 {max_texts} 条文本")
            
            tts = DouyinTTSSingle(CONFIG)
            stats = tts.batch_synthesize(texts, "file_batch")
    
    print(f"\n🎉 单线程版本运行完成！")

if __name__ == "__main__":
    main()
