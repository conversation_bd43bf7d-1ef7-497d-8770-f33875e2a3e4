#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包语音合成大模型WebSocket API调用程序
每种音色生成1小时音频（约24000字）
"""

import asyncio
import json
import logging
import uuid
import os
import time
import glob
from datetime import datetime
import websockets
from protocols import MsgType, full_client_request, receive_message, is_audio_message, is_final_message, extract_audio_data

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ==================== 配置区域 ====================
DOUYIN_CONFIG = {
    # API配置
    "APPID": "9862368305",
    "ACCESS_TOKEN": "bbxNaAzRzBSkySjouDItXl6tBUUyNd2W",
    
    # WebSocket配置
    "ENDPOINT": "wss://openspeech.bytedance.com/api/v1/tts/ws_binary",
    
    # 音频格式配置
    "ENCODING": "wav",
    
    # 文件配置
    "TEXT_FILE": "shuffled_from_rank_modified.txt",
    "PINYIN_FILE": "shuffled_from_rank_modified_pinyin.txt",
    "OUTPUT_DIR": "audio_output_1h",
    "CSV_FILE": "douyin_1h_audio_log.csv",
    "REQUEST_INTERVAL": 1.0,  # WebSocket请求间隔
    
    # 生成配置
    "TARGET_CHARS_PER_VOICE": 24000,  # 每种音色目标字数（约1小时）
}

# 音色配置，200多种音色，明天进行调试 - 10种音色，每种生成约24000字
VOICE_CONFIGS = [
    {"voice_type": "BV001_streaming", "name": "通用女声", "code": "001"},
    {"voice_type": "BV002_streaming", "name": "通用男声", "code": "002"},
    {"voice_type": "BV701_streaming", "name": "擎苍", "code": "701"},
    {"voice_type": "BV119_streaming", "name": "通用赘婿", "code": "119"},
    {"voice_type": "BV102_streaming", "name": "儒雅青年", "code": "102"},
    {"voice_type": "BV113_streaming", "name": "甜宠少御", "code": "113"},
    {"voice_type": "BV115_streaming", "name": "古风少御", "code": "115"},
    {"voice_type": "BV007_streaming", "name": "亲切女声", "code": "007"},
    {"voice_type": "BV056_streaming", "name": "阳光男声", "code": "056"},
    {"voice_type": "BV005_streaming", "name": "活泼女声", "code": "005"},
]

def get_cluster(voice: str) -> str:
    """获取集群名称"""
    if voice.startswith("S_"):
        return "volcano_icl"
    return "volcano_tts"

class DouyinTTSWebSocket:
    """豆包TTS WebSocket核心类"""
    
    def __init__(self, config):
        self.config = config
        self.headers = {
            "Authorization": f"Bearer;{config['ACCESS_TOKEN']}",
        }
    
    async def synthesize_text(self, text, voice_type):
        """使用WebSocket合成单个文本"""
        try:
            # 连接WebSocket
            logger.info(f"连接到 {self.config['ENDPOINT']}")
            websocket = await websockets.connect(
                self.config["ENDPOINT"], 
                additional_headers=self.headers, 
                max_size=10 * 1024 * 1024
            )
            
            try:
                # 确定集群
                cluster = get_cluster(voice_type)
                
                # 准备请求负载
                request = {
                    "app": {
                        "appid": self.config["APPID"],
                        "token": self.config["ACCESS_TOKEN"],
                        "cluster": cluster,
                    },
                    "user": {
                        "uid": str(uuid.uuid4()),
                    },
                    "audio": {
                        "voice_type": voice_type,
                        "encoding": self.config["ENCODING"],
                    },
                    "request": {
                        "reqid": str(uuid.uuid4()),
                        "text": text,
                        "operation": "submit",
                        "with_timestamp": "1",
                        "extra_param": json.dumps({
                            "disable_markdown_filter": False,
                        }),
                    },
                }
                
                # 发送请求
                await full_client_request(websocket, json.dumps(request).encode())
                
                # 接收音频数据
                audio_data = bytearray()
                while True:
                    msg = await receive_message(websocket)

                    if msg.type == MsgType.FrontEndResultServer:
                        continue
                    elif is_audio_message(msg):
                        audio_chunk = extract_audio_data(msg)
                        audio_data.extend(audio_chunk)
                        if is_final_message(msg):  # 最后一条消息
                            break
                    elif msg.type == MsgType.ErrorServer:
                        raise RuntimeError(f"TTS转换失败: {msg.payload.decode()}")
                    else:
                        logger.warning(f"未知消息类型: {msg.type}")

                # 检查是否收到音频数据
                if not audio_data:
                    raise RuntimeError("未收到音频数据")

                return bytes(audio_data)
                
            finally:
                await websocket.close()
                
        except Exception as e:
            logger.error(f"WebSocket合成失败: {e}")
            return None

def load_text_files(config):
    """加载文本和拼音文件"""
    print("=== 加载文本文件 ===")
    
    # 读取文本文件
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    return texts, pinyins

def select_texts_for_target_chars(texts, target_chars):
    """根据目标字数选择文本"""
    selected_texts = []
    selected_indices = []
    current_chars = 0
    
    for i, text in enumerate(texts):
        text_length = len(text)
        if current_chars + text_length <= target_chars:
            selected_texts.append(text)
            selected_indices.append(i)
            current_chars += text_length
        else:
            # 如果加上这个文本会超过目标字数，检查是否接近目标
            if target_chars - current_chars > text_length // 2:
                selected_texts.append(text)
                selected_indices.append(i)
                current_chars += text_length
            break
    
    return selected_texts, selected_indices, current_chars

def get_existing_files(output_dir, voice_code):
    """获取指定音色已存在的文件"""
    pattern = os.path.join(output_dir, f"*{voice_code}*.wav")
    audio_files = glob.glob(pattern)
    return len(audio_files)

async def generate_voice_batch(voice_config, texts, pinyins, config, batch_num):
    """生成单个音色批次的音频"""
    voice_type = voice_config["voice_type"]
    voice_name = voice_config["name"]
    voice_code = voice_config["code"]
    target_chars = config["TARGET_CHARS_PER_VOICE"]
    
    print(f"\n=== 批次 {batch_num}: {voice_name} ({voice_type}) ===")
    print(f"目标字数: {target_chars} 字（约1小时）")
    
    # 检查已存在的文件
    existing_count = get_existing_files(config["OUTPUT_DIR"], voice_code)
    if existing_count > 0:
        print(f"已存在 {existing_count} 个文件，继续生成...")
    
    # 选择文本达到目标字数
    selected_texts, selected_indices, actual_chars = select_texts_for_target_chars(texts, target_chars)
    
    print(f"选择文本: {len(selected_texts)} 条")
    print(f"实际字数: {actual_chars} 字")
    
    if not selected_texts:
        print("✗ 没有可用的文本")
        return False
    
    # 创建TTS对象
    tts = DouyinTTSWebSocket(config)
    
    # 统计信息
    success_count = 0
    error_count = 0
    start_time = time.time()
    
    # 打开CSV文件进行追加
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    csv_exists = os.path.exists(csv_path)
    
    with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
        # 写入标题行（如果文件不存在）
        if not csv_exists:
            csvfile.write('音频名\t类型\t文本\t注音\t音色\n')
        
        for i, (text_idx, text) in enumerate(zip(selected_indices, selected_texts), 1):
            pinyin = pinyins[text_idx] if text_idx < len(pinyins) else ""
            
            # 音频文件名格式：音色代码 + 序号
            audio_name = f"{voice_code}{i:04d}"
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")
            
            print(f"[{i}/{len(selected_texts)}] {audio_name}: {text[:50]}{'...' if len(text) > 50 else ''}")
            
            # 检查文件是否已存在
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                continue
            
            # 生成音频（带重试）
            audio_data = None
            for retry in range(3):
                audio_data = await tts.synthesize_text(text, voice_type)
                if audio_data:
                    break
                if retry < 2:
                    print(f"  重试 {retry + 1}/3...")
                    await asyncio.sleep(2)
            
            if audio_data:
                try:
                    # 保存音频文件
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)
                    
                    # 写入CSV记录
                    csvfile.write(f"{audio_name}\tc\t{text}\t{pinyin}\t{voice_name}\n")
                    csvfile.flush()
                    
                    print(f"  ✓ 成功 ({len(audio_data)} 字节)")
                    success_count += 1
                    
                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1
            
            # 请求间隔
            await asyncio.sleep(config["REQUEST_INTERVAL"])
            
            # 每10个显示进度
            if i % 10 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(selected_texts) - i) * avg_time
                print(f"  进度: {i}/{len(selected_texts)}, 预计剩余: {remaining/60:.1f}分钟")
    
    elapsed_time = time.time() - start_time
    print(f"\n批次 {batch_num} 完成:")
    print(f"  耗时: {elapsed_time/60:.1f}分钟")
    print(f"  成功: {success_count}")
    print(f"  失败: {error_count}")
    print(f"  实际生成字数: {actual_chars}")
    
    return error_count == 0

async def main():
    """主函数"""
    print("=" * 60)
    print("豆包语音合成大模型WebSocket API - 1小时音频生成")
    print("=" * 60)

    config = DOUYIN_CONFIG

    print("配置信息:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  拼音文件: {config['PINYIN_FILE']}")
    print(f"  输出目录: {config['OUTPUT_DIR']}")
    print(f"  每种音色目标字数: {config['TARGET_CHARS_PER_VOICE']} 字")
    print(f"  请求间隔: {config['REQUEST_INTERVAL']}秒")

    print(f"\n音色配置:")
    for i, voice in enumerate(VOICE_CONFIGS, 1):
        print(f"  {i}. {voice['name']} ({voice['voice_type']}) - 代码: {voice['code']}")

    print("=" * 60)

    # 1. 检查必要文件
    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return

    if not os.path.exists(config['PINYIN_FILE']):
        print(f"⚠️ 拼音文件不存在: {config['PINYIN_FILE']}")
        print("将继续处理，但拼音字段将为空")

    # 2. 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)

    # 3. 加载文本和拼音文件
    print(f"\n=== 加载文本文件 ===")
    texts, pinyins = load_text_files(config)
    if texts is None:
        return

    # 计算总字数
    total_chars = sum(len(text) for text in texts)
    print(f"文本总字数: {total_chars} 字")

    # 4. 初始化CSV文件
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    if not os.path.exists(csv_path):
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            csvfile.write('音频名\t类型\t文本\t注音\t音色\n')
        print(f"✓ 创建CSV文件: {csv_path}")

    # 5. 检查现有文件
    print(f"\n=== 检查现有文件 ===")
    total_existing = 0
    for voice in VOICE_CONFIGS:
        existing = get_existing_files(config["OUTPUT_DIR"], voice["code"])
        total_existing += existing
        if existing > 0:
            print(f"  {voice['name']}: {existing} 个文件")

    if total_existing > 0:
        print(f"总计已存在: {total_existing} 个文件")

    # 6. 确认生成
    try:
        confirm = input(f"\n是否开始生成所有音色的1小时音频？(y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("已取消生成")
            return
    except KeyboardInterrupt:
        print("\n程序被中断")
        return

    # 7. 开始分批生成
    print(f"\n=== 开始分批生成 ===")

    for i, voice_config in enumerate(VOICE_CONFIGS, 1):
        success = await generate_voice_batch(voice_config, texts, pinyins, config, i)
        if not success:
            print(f"⚠️ {voice_config['name']}生成失败，可能部分文件未完成")

            # 询问是否继续
            try:
                continue_choice = input("是否继续生成下一个音色？(y/n): ").strip().lower()
                if continue_choice not in ['y', 'yes', '是']:
                    print("生成被中断")
                    break
            except KeyboardInterrupt:
                print("\n生成被中断")
                break

    # 8. 最终统计
    print(f"\n=== 最终统计 ===")

    total_generated = 0
    total_chars_generated = 0

    for voice in VOICE_CONFIGS:
        existing = get_existing_files(config["OUTPUT_DIR"], voice["code"])
        total_generated += existing

        # 估算字数（假设平均每个文件对应的字数）
        estimated_chars = existing * (config["TARGET_CHARS_PER_VOICE"] // 100)  # 粗略估算
        total_chars_generated += estimated_chars

        print(f"{voice['name']}: {existing} 个文件")

    print(f"\n总计生成文件: {total_generated}")
    print(f"估算总字数: {total_chars_generated} 字")
    print(f"估算总时长: {total_chars_generated / 24000:.1f} 小时")

    print(f"\n输出目录: {config['OUTPUT_DIR']}")
    print(f"CSV记录: {csv_path}")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
