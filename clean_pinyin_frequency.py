#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拼音频率清理工具
从拼音文件中删除频率数字，只保留纯拼音
"""

import os
import re
from typing import List, Set

# 配置信息
CONFIG = {
    # 输入文件路径（包含频率数字的拼音文件）
    "INPUT_FILES": [
        "supplement_aliyun_pinyin.txt",
        "supplement_baiduyun_pinyin.txt", 
        "supplement_doubao_pinyin.txt",
        # 可以添加更多文件
    ],
    
    # 输出文件路径（清理后的纯拼音文件）
    "OUTPUT_FILES": [
        "supplement_aliyun_pinyin_clean.txt",
        "supplement_baiduyun_pinyin_clean.txt",
        "supplement_doubao_pinyin_clean.txt",
        # 对应的输出文件
    ],
    
    # 是否显示详细处理过程
    "VERBOSE": True,
}

def extract_pure_pinyin_from_line(line: str) -> str:
    """
    从包含频率数字的行中提取纯拼音
    
    输入示例: "fang2 1637 sha1 3022"
    输出示例: "fang2 sha1"
    
    输入示例: "zong1 fa3 23154"  
    输出示例: "zong1 fa3"
    """
    if not line.strip():
        return ""
    
    # 分割成单词
    parts = line.strip().split()
    
    # 拼音模式：字母+数字（声调）
    pinyin_pattern = r'^[a-zA-Z]+[1-5]?$'
    
    # 提取所有符合拼音格式的部分
    pure_pinyins = []
    for part in parts:
        if re.match(pinyin_pattern, part):
            pure_pinyins.append(part)
    
    return ' '.join(pure_pinyins)

def clean_pinyin_file(input_file: str, output_file: str, verbose: bool = False) -> dict:
    """
    清理单个拼音文件
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
        verbose: 是否显示详细信息
    
    Returns:
        处理统计信息
    """
    if not os.path.exists(input_file):
        print(f"✗ 输入文件不存在: {input_file}")
        return {"success": False, "error": "文件不存在"}
    
    print(f"\n=== 处理文件: {input_file} ===")
    
    try:
        # 读取输入文件
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"✓ 读取文件: {len(lines)} 行")
        
        # 处理每一行
        cleaned_lines = []
        original_pinyin_count = 0
        cleaned_pinyin_count = 0
        empty_lines = 0
        
        for i, line in enumerate(lines):
            # 统计原始拼音数量
            original_parts = line.strip().split()
            original_pinyin_count += len([p for p in original_parts if re.match(r'^[a-zA-Z]+[1-5]?$', p)])
            
            # 清理行
            cleaned_line = extract_pure_pinyin_from_line(line)
            
            if cleaned_line:
                cleaned_lines.append(cleaned_line)
                # 统计清理后拼音数量
                cleaned_pinyin_count += len(cleaned_line.split())
            else:
                empty_lines += 1
            
            # 显示处理示例（前5行）
            if verbose and i < 5:
                print(f"  原始: {line.strip()}")
                print(f"  清理: {cleaned_line}")
                print()
        
        # 写入输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            for line in cleaned_lines:
                f.write(line + '\n')
        
        # 统计信息
        stats = {
            "success": True,
            "input_lines": len(lines),
            "output_lines": len(cleaned_lines),
            "empty_lines": empty_lines,
            "original_pinyin_count": original_pinyin_count,
            "cleaned_pinyin_count": cleaned_pinyin_count,
            "removed_numbers": original_parts_count - cleaned_pinyin_count if 'original_parts_count' in locals() else 0
        }
        
        print(f"✓ 处理完成:")
        print(f"  输入行数: {stats['input_lines']}")
        print(f"  输出行数: {stats['output_lines']}")
        print(f"  空行数: {stats['empty_lines']}")
        print(f"  原始拼音数: {stats['original_pinyin_count']}")
        print(f"  清理后拼音数: {stats['cleaned_pinyin_count']}")
        print(f"  输出文件: {output_file}")
        
        return stats
        
    except Exception as e:
        print(f"✗ 处理文件失败: {e}")
        return {"success": False, "error": str(e)}

def batch_clean_pinyin_files(input_files: List[str], output_files: List[str], verbose: bool = False):
    """
    批量清理拼音文件
    """
    print("=" * 60)
    print("拼音频率清理工具")
    print("删除拼音文件中的频率数字，只保留纯拼音")
    print("=" * 60)
    
    if len(input_files) != len(output_files):
        print("✗ 输入文件和输出文件数量不匹配")
        return
    
    total_stats = {
        "processed_files": 0,
        "successful_files": 0,
        "total_input_lines": 0,
        "total_output_lines": 0,
        "total_original_pinyins": 0,
        "total_cleaned_pinyins": 0
    }
    
    # 处理每个文件
    for input_file, output_file in zip(input_files, output_files):
        stats = clean_pinyin_file(input_file, output_file, verbose)
        
        total_stats["processed_files"] += 1
        
        if stats.get("success", False):
            total_stats["successful_files"] += 1
            total_stats["total_input_lines"] += stats.get("input_lines", 0)
            total_stats["total_output_lines"] += stats.get("output_lines", 0)
            total_stats["total_original_pinyins"] += stats.get("original_pinyin_count", 0)
            total_stats["total_cleaned_pinyins"] += stats.get("cleaned_pinyin_count", 0)
    
    # 显示总体统计
    print(f"\n{'='*60}")
    print("处理总结:")
    print(f"  处理文件数: {total_stats['processed_files']}")
    print(f"  成功文件数: {total_stats['successful_files']}")
    print(f"  总输入行数: {total_stats['total_input_lines']}")
    print(f"  总输出行数: {total_stats['total_output_lines']}")
    print(f"  总原始拼音数: {total_stats['total_original_pinyins']}")
    print(f"  总清理拼音数: {total_stats['total_cleaned_pinyins']}")
    
    if total_stats['total_original_pinyins'] > 0:
        retention_rate = total_stats['total_cleaned_pinyins'] / total_stats['total_original_pinyins'] * 100
        print(f"  拼音保留率: {retention_rate:.1f}%")
    
    print(f"\n🎉 批量清理完成！")
    print("清理后的文件:")
    for i, output_file in enumerate(output_files, 1):
        if os.path.exists(output_file):
            print(f"  {i}. {output_file}")
    print("=" * 60)

def clean_single_file_interactive():
    """
    交互式清理单个文件
    """
    print("\n=== 交互式单文件清理 ===")
    
    # 获取输入文件
    input_file = input("请输入要清理的拼音文件路径: ").strip()
    if not input_file:
        print("✗ 未输入文件路径")
        return
    
    # 生成输出文件名
    base_name = os.path.splitext(input_file)[0]
    output_file = f"{base_name}_clean.txt"
    
    # 询问是否自定义输出文件名
    custom_output = input(f"输出文件名 (默认: {output_file}): ").strip()
    if custom_output:
        output_file = custom_output
    
    # 处理文件
    stats = clean_pinyin_file(input_file, output_file, verbose=True)
    
    if stats.get("success", False):
        print(f"\n✅ 清理成功！输出文件: {output_file}")
    else:
        print(f"\n❌ 清理失败: {stats.get('error', '未知错误')}")

def main():
    """主函数"""
    config = CONFIG
    
    print("选择处理模式:")
    print("1. 批量处理（使用配置文件中的文件列表）")
    print("2. 交互式处理单个文件")
    print("3. 显示示例")
    
    choice = input("\n请选择 (1/2/3): ").strip()
    
    if choice == "1":
        # 批量处理
        batch_clean_pinyin_files(
            config["INPUT_FILES"], 
            config["OUTPUT_FILES"], 
            config["VERBOSE"]
        )
    
    elif choice == "2":
        # 交互式处理
        clean_single_file_interactive()
    
    elif choice == "3":
        # 显示示例
        print("\n=== 处理示例 ===")
        examples = [
            "fang2 1637 sha1 3022",
            "zong1 fa3 23154",
            "bian4 cai2 44612",
            "shang4 gang1 35417",
            "gan4 qun2 7848",
            "fu4 ru2 31507",
            "pei2 xiao4 31658",
            "niu2 pi2 16659",
            "dui4 14 de2 4434",
            "xiao3 xie2 33336",
            "rou4 ti3 7693",
            "xi3 1876 cong2 27"
        ]
        
        print("原始格式 -> 清理后格式:")
        print("-" * 40)
        for example in examples:
            cleaned = extract_pure_pinyin_from_line(example)
            print(f"{example} -> {cleaned}")
        
        print("\n说明:")
        print("- 保留所有拼音（字母+数字声调）")
        print("- 删除纯数字（频率信息）")
        print("- 保持拼音之间的空格分隔")
    
    else:
        print("✗ 无效选择")

if __name__ == "__main__":
    main()
