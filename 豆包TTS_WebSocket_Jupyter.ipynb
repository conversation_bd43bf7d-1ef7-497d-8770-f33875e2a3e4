{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 豆包语音合成大模型WebSocket API - Jupyter版本\n", "\n", "每种音色生成1小时音频（约24000字）\n", "适配Jupyter Notebook环境"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 安装必要的库\n", "!pip install websockets nest-asyncio"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import json\n", "import logging\n", "import uuid\n", "import os\n", "import time\n", "import glob\n", "from datetime import datetime\n", "import asyncio\n", "import nest_asyncio\n", "\n", "# 允许在<PERSON><PERSON><PERSON>中运行asyncio\n", "nest_asyncio.apply()\n", "\n", "# 设置日志\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"✓ 库导入成功\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 配置参数\n", "DOUYIN_CONFIG = {\n", "    # API配置\n", "    \"APPID\": \"9862368305\",\n", "    \"ACCESS_TOKEN\": \"bbxNaAzRzBSkySjouDItXl6tBUUyNd2W\",\n", "    \n", "    # WebSocket配置\n", "    \"ENDPOINT\": \"wss://openspeech.bytedance.com/api/v1/tts/ws_binary\",\n", "    \n", "    # 音频格式配置\n", "    \"ENCODING\": \"wav\",\n", "    \n", "    # 文件配置\n", "    \"TEXT_FILE\": \"shuffled_from_rank_modified.txt\",\n", "    \"PINYIN_FILE\": \"shuffled_from_rank_modified_pinyin.txt\",\n", "    \"OUTPUT_DIR\": \"audio_output_1h\",\n", "    \"CSV_FILE\": \"douyin_1h_audio_log.csv\",\n", "    \"REQUEST_INTERVAL\": 1.0,  # WebSocket请求间隔\n", "    \n", "    # 生成配置\n", "    \"TARGET_CHARS_PER_VOICE\": 24000,  # 每种音色目标字数（约1小时）\n", "}\n", "\n", "# 音色配置 - 10种音色，每种生成约24000字\n", "VOICE_CONFIGS = [\n", "    {\"voice_type\": \"BV001_streaming\", \"name\": \"通用女声\", \"code\": \"001\"},\n", "    {\"voice_type\": \"BV002_streaming\", \"name\": \"通用男声\", \"code\": \"002\"},\n", "    {\"voice_type\": \"BV701_streaming\", \"name\": \"擎苍\", \"code\": \"701\"},\n", "    {\"voice_type\": \"BV119_streaming\", \"name\": \"通用赘婿\", \"code\": \"119\"},\n", "    {\"voice_type\": \"BV102_streaming\", \"name\": \"儒雅青年\", \"code\": \"102\"},\n", "]\n", "\n", "print(\"✓ 配置加载完成\")\n", "print(f\"目标字数: {DOUYIN_CONFIG['TARGET_CHARS_PER_VOICE']} 字/音色\")\n", "print(f\"音色数量: {len(VOICE_CONFIGS)} 种\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_cluster(voice: str) -> str:\n", "    \"\"\"获取集群名称\"\"\"\n", "    if voice.startswith(\"S_\"):\n", "        return \"volcano_icl\"\n", "    return \"volcano_tts\"\n", "\n", "class DouyinTTSWebSocket:\n", "    \"\"\"豆包TTS WebSocket核心类\"\"\"\n", "    \n", "    def __init__(self, config):\n", "        self.config = config\n", "        self.headers = {\n", "            \"Authorization\": f\"Bearer;{config['ACCESS_TOKEN']}\",\n", "        }\n", "    \n", "    async def synthesize_text(self, text, voice_type):\n", "        \"\"\"使用WebSocket合成单个文本\"\"\"\n", "        try:\n", "            import websockets\n", "            \n", "            # 连接WebSocket\n", "            websocket = await websockets.connect(\n", "                self.config[\"ENDPOINT\"], \n", "                additional_headers=self.headers, \n", "                max_size=10 * 1024 * 1024\n", "            )\n", "            \n", "            try:\n", "                # 确定集群\n", "                cluster = get_cluster(voice_type)\n", "                \n", "                # 准备请求负载\n", "                request = {\n", "                    \"app\": {\n", "                        \"appid\": self.config[\"APPID\"],\n", "                        \"token\": self.config[\"ACCESS_TOKEN\"],\n", "                        \"cluster\": cluster,\n", "                    },\n", "                    \"user\": {\n", "                        \"uid\": str(uuid.uuid4()),\n", "                    },\n", "                    \"audio\": {\n", "                        \"voice_type\": voice_type,\n", "                        \"encoding\": self.config[\"ENCODING\"],\n", "                    },\n", "                    \"request\": {\n", "                        \"reqid\": str(uuid.uuid4()),\n", "                        \"text\": text,\n", "                        \"operation\": \"submit\",\n", "                        \"with_timestamp\": \"1\",\n", "                        \"extra_param\": json.dumps({\n", "                            \"disable_markdown_filter\": <PERSON><PERSON><PERSON>,\n", "                        }),\n", "                    },\n", "                }\n", "                \n", "                # 发送请求\n", "                await websocket.send(json.dumps(request))\n", "                \n", "                # 接收音频数据\n", "                audio_data = bytearray()\n", "                response_count = 0\n", "                max_responses = 50  # 最大响应数量限制\n", "                \n", "                while response_count < max_responses:\n", "                    try:\n", "                        response = await asyncio.wait_for(websocket.recv(), timeout=10.0)\n", "                        response_count += 1\n", "                        \n", "                        if isinstance(response, bytes):\n", "                            audio_data.extend(response)\n", "                            # 如果收到足够的音频数据，可以结束\n", "                            if len(audio_data) > 1000:  # 至少1KB数据\n", "                                break\n", "                        elif isinstance(response, str):\n", "                            # 处理JSON响应\n", "                            try:\n", "                                json_resp = json.loads(response)\n", "                                if 'data' in json_resp:\n", "                                    import base64\n", "                                    audio_chunk = base64.b64decode(json_resp['data'])\n", "                                    audio_data.extend(audio_chunk)\n", "                            except json.JSONDecodeError:\n", "                                pass\n", "                        \n", "                    except asyncio.TimeoutError:\n", "                        break\n", "                \n", "                # 检查是否收到音频数据\n", "                if not audio_data:\n", "                    raise RuntimeError(\"未收到音频数据\")\n", "                \n", "                return bytes(audio_data)\n", "                \n", "            finally:\n", "                await websocket.close()\n", "                \n", "        except Exception as e:\n", "            logger.error(f\"WebSocket合成失败: {e}\")\n", "            return None\n", "\n", "print(\"✓ TTS类定义完成\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 测试WebSocket连接\n", "async def test_websocket_connection():\n", "    \"\"\"测试WebSocket连接\"\"\"\n", "    print(\"=== 豆包语音WebSocket连接测试 ===\")\n", "    \n", "    config = DOUYIN_CONFIG\n", "    \n", "    # 创建输出目录\n", "    os.makedirs(config[\"OUTPUT_DIR\"], exist_ok=True)\n", "    \n", "    # 创建TTS对象\n", "    tts = DouyinTTSWebSocket(config)\n", "    \n", "    # 测试文本\n", "    test_text = \"这是一个豆包语音合成大模型的测试文本\"\n", "    voice_type = \"BV001_streaming\"\n", "    \n", "    print(f\"测试文本: {test_text}\")\n", "    print(f\"测试音色: {voice_type}\")\n", "    \n", "    # 合成语音\n", "    audio_data = await tts.synthesize_text(test_text, voice_type)\n", "    \n", "    if audio_data:\n", "        try:\n", "            # 保存音频文件\n", "            audio_path = os.path.join(config[\"OUTPUT_DIR\"], \"test_websocket.wav\")\n", "            with open(audio_path, \"wb\") as f:\n", "                f.write(audio_data)\n", "            print(f\"✓ 测试成功！音频已保存到: {audio_path}\")\n", "            print(f\"✓ 音频大小: {len(audio_data)} 字节\")\n", "            return True\n", "        except Exception as e:\n", "            print(f\"✗ 保存音频失败: {e}\")\n", "            return False\n", "    else:\n", "        print(f\"✗ 测试失败\")\n", "        return False\n", "\n", "# 运行测试\n", "success = await test_websocket_connection()\n", "if success:\n", "    print(\"\\n🎉 WebSocket连接测试成功！\")\n", "else:\n", "    print(\"\\n⚠️ WebSocket连接测试失败\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 如果测试成功，可以继续运行批量生成\n", "def load_text_files(config):\n", "    \"\"\"加载文本和拼音文件\"\"\"\n", "    print(\"=== 加载文本文件 ===\")\n", "    \n", "    # 读取文本文件\n", "    try:\n", "        with open(config[\"TEXT_FILE\"], 'r', encoding='utf-8') as f:\n", "            texts = [line.strip() for line in f if line.strip()]\n", "        print(f\"✓ 文本文件: {len(texts)} 行\")\n", "    except Exception as e:\n", "        print(f\"✗ 读取文本文件失败: {e}\")\n", "        return None, None\n", "    \n", "    # 读取拼音文件\n", "    pinyins = []\n", "    if os.path.exists(config[\"PINYIN_FILE\"]):\n", "        try:\n", "            with open(config[\"PINYIN_FILE\"], 'r', encoding='utf-8') as f:\n", "                pinyins = [line.strip() for line in f if line.strip()]\n", "            print(f\"✓ 拼音文件: {len(pinyins)} 行\")\n", "        except Exception as e:\n", "            print(f\"读取拼音文件失败: {e}\")\n", "    \n", "    return texts, pinyins\n", "\n", "def select_texts_for_target_chars(texts, target_chars):\n", "    \"\"\"根据目标字数选择文本\"\"\"\n", "    selected_texts = []\n", "    selected_indices = []\n", "    current_chars = 0\n", "    \n", "    for i, text in enumerate(texts):\n", "        text_length = len(text)\n", "        if current_chars + text_length <= target_chars:\n", "            selected_texts.append(text)\n", "            selected_indices.append(i)\n", "            current_chars += text_length\n", "        else:\n", "            # 如果加上这个文本会超过目标字数，检查是否接近目标\n", "            if target_chars - current_chars > text_length // 2:\n", "                selected_texts.append(text)\n", "                selected_indices.append(i)\n", "                current_chars += text_length\n", "            break\n", "    \n", "    return selected_texts, selected_indices, current_chars\n", "\n", "# 加载文本文件\n", "texts, pinyins = load_text_files(DOUYIN_CONFIG)\n", "if texts:\n", "    total_chars = sum(len(text) for text in texts)\n", "    print(f\"\\n文本总字数: {total_chars} 字\")\n", "    print(f\"可生成音色数: {total_chars // DOUYIN_CONFIG['TARGET_CHARS_PER_VOICE']} 种\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成单个音色的音频（测试版本，限制文件数量）\n", "async def generate_voice_test(voice_config, texts, pinyins, config, max_files=3):\n", "    \"\"\"生成单个音色的测试音频\"\"\"\n", "    voice_type = voice_config[\"voice_type\"]\n", "    voice_name = voice_config[\"name\"]\n", "    voice_code = voice_config[\"code\"]\n", "    \n", "    print(f\"\\n=== 测试生成: {voice_name} ({voice_type}) ===\")\n", "    print(f\"限制生成: {max_files} 个文件\")\n", "    \n", "    # 选择前几个文本进行测试\n", "    selected_texts = texts[:max_files]\n", "    selected_indices = list(range(max_files))\n", "    actual_chars = sum(len(text) for text in selected_texts)\n", "    \n", "    print(f\"选择文本: {len(selected_texts)} 条\")\n", "    print(f\"实际字数: {actual_chars} 字\")\n", "    \n", "    # 创建TTS对象\n", "    tts = DouyinTTSWebSocket(config)\n", "    \n", "    # 统计信息\n", "    success_count = 0\n", "    error_count = 0\n", "    \n", "    # 打开CSV文件进行追加\n", "    csv_path = os.path.join(config[\"OUTPUT_DIR\"], config[\"CSV_FILE\"])\n", "    csv_exists = os.path.exists(csv_path)\n", "    \n", "    with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:\n", "        # 写入标题行（如果文件不存在）\n", "        if not csv_exists:\n", "            csvfile.write('音频名\\t类型\\t文本\\t注音\\t音色\\n')\n", "        \n", "        for i, (text_idx, text) in enumerate(zip(selected_indices, selected_texts), 1):\n", "            pinyin = pinyins[text_idx] if text_idx < len(pinyins) else \"\"\n", "            \n", "            # 音频文件名格式：音色代码 + 序号\n", "            audio_name = f\"{voice_code}{i:04d}\"\n", "            audio_path = os.path.join(config[\"OUTPUT_DIR\"], f\"{audio_name}.wav\")\n", "            \n", "            print(f\"[{i}/{len(selected_texts)}] {audio_name}: {text[:50]}{'...' if len(text) > 50 else ''}\")\n", "            \n", "            # 检查文件是否已存在\n", "            if os.path.exists(audio_path):\n", "                print(f\"  跳过已存在文件\")\n", "                continue\n", "            \n", "            # 生成音频\n", "            audio_data = await tts.synthesize_text(text, voice_type)\n", "            \n", "            if audio_data:\n", "                try:\n", "                    # 保存音频文件\n", "                    with open(audio_path, \"wb\") as f:\n", "                        f.write(audio_data)\n", "                    \n", "                    # 写入CSV记录\n", "                    csvfile.write(f\"{audio_name}\\tc\\t{text}\\t{pinyin}\\t{voice_name}\\n\")\n", "                    csvfile.flush()\n", "                    \n", "                    print(f\"  ✓ 成功 ({len(audio_data)} 字节)\")\n", "                    success_count += 1\n", "                    \n", "                except Exception as e:\n", "                    print(f\"  ✗ 保存失败: {e}\")\n", "                    error_count += 1\n", "            else:\n", "                print(f\"  ✗ 生成失败\")\n", "                error_count += 1\n", "            \n", "            # 请求间隔\n", "            await asyncio.sleep(config[\"REQUEST_INTERVAL\"])\n", "    \n", "    print(f\"\\n测试完成:\")\n", "    print(f\"  成功: {success_count}\")\n", "    print(f\"  失败: {error_count}\")\n", "    \n", "    return error_count == 0\n", "\n", "# 测试第一个音色\n", "if texts:\n", "    voice_config = VOICE_CONFIGS[0]\n", "    success = await generate_voice_test(voice_config, texts, pinyins, DOUYIN_CONFIG, max_files=3)\n", "    \n", "    if success:\n", "        print(\"\\n🎉 音色测试成功！\")\n", "        print(\"可以继续运行完整的批量生成程序\")\n", "    else:\n", "        print(\"\\n⚠️ 音色测试失败\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}