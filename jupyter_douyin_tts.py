# 豆包语音合成大模型WebSocket API - Jupyter版本
# 解决 asyncio.run() 在Jupyter中的问题

# 第一步：安装必要的库（在Jupyter中运行）
# !pip install websockets nest-asyncio

# 第二步：导入库和配置
import json
import logging
import uuid
import os
import time
import glob
from datetime import datetime
import asyncio
import nest_asyncio

# 允许在Jupyter中运行asyncio
nest_asyncio.apply()

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置参数
DOUYIN_CONFIG = {
    # API配置
    "APPID": "9862368305",
    "ACCESS_TOKEN": "bbxNaAzRzBSkySjouDItXl6tBUUyNd2W",
    
    # WebSocket配置
    "ENDPOINT": "wss://openspeech.bytedance.com/api/v1/tts/ws_binary",
    
    # 音频格式配置
    "ENCODING": "wav",
    
    # 文件配置
    "TEXT_FILE": "shuffled_from_rank_modified.txt",
    "PINYIN_FILE": "shuffled_from_rank_modified_pinyin.txt",
    "OUTPUT_DIR": "audio_output_1h",
    "CSV_FILE": "douyin_1h_audio_log.csv",
    "REQUEST_INTERVAL": 1.0,  # WebSocket请求间隔
    
    # 生成配置
    "TARGET_CHARS_PER_VOICE": 24000,  # 每种音色目标字数（约1小时）
}

# 音色配置 - 10种音色，每种生成约24000字
VOICE_CONFIGS = [
    {"voice_type": "BV001_streaming", "name": "通用女声", "code": "001"},
    {"voice_type": "BV002_streaming", "name": "通用男声", "code": "002"},
    {"voice_type": "BV701_streaming", "name": "擎苍", "code": "701"},
    {"voice_type": "BV119_streaming", "name": "通用赘婿", "code": "119"},
    {"voice_type": "BV102_streaming", "name": "儒雅青年", "code": "102"},
    {"voice_type": "BV113_streaming", "name": "甜宠少御", "code": "113"},
    {"voice_type": "BV115_streaming", "name": "古风少御", "code": "115"},
    {"voice_type": "BV007_streaming", "name": "亲切女声", "code": "007"},
    {"voice_type": "BV056_streaming", "name": "阳光男声", "code": "056"},
    {"voice_type": "BV005_streaming", "name": "活泼女声", "code": "005"},
]

def get_cluster(voice: str) -> str:
    """获取集群名称"""
    if voice.startswith("S_"):
        return "volcano_icl"
    return "volcano_tts"

class DouyinTTSWebSocket:
    """豆包TTS WebSocket核心类"""
    
    def __init__(self, config):
        self.config = config
        self.headers = {
            "Authorization": f"Bearer;{config['ACCESS_TOKEN']}",
        }
    
    async def synthesize_text(self, text, voice_type):
        """使用WebSocket合成单个文本"""
        try:
            import websockets
            
            # 连接WebSocket
            websocket = await websockets.connect(
                self.config["ENDPOINT"], 
                additional_headers=self.headers, 
                max_size=10 * 1024 * 1024
            )
            
            try:
                # 确定集群
                cluster = get_cluster(voice_type)
                
                # 准备请求负载
                request = {
                    "app": {
                        "appid": self.config["APPID"],
                        "token": self.config["ACCESS_TOKEN"],
                        "cluster": cluster,
                    },
                    "user": {
                        "uid": str(uuid.uuid4()),
                    },
                    "audio": {
                        "voice_type": voice_type,
                        "encoding": self.config["ENCODING"],
                    },
                    "request": {
                        "reqid": str(uuid.uuid4()),
                        "text": text,
                        "operation": "submit",
                        "with_timestamp": "1",
                        "extra_param": json.dumps({
                            "disable_markdown_filter": False,
                        }),
                    },
                }
                
                # 发送请求
                await websocket.send(json.dumps(request))
                
                # 接收音频数据
                audio_data = bytearray()
                response_count = 0
                max_responses = 50  # 最大响应数量限制
                
                while response_count < max_responses:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                        response_count += 1
                        
                        if isinstance(response, bytes):
                            audio_data.extend(response)
                            # 如果收到足够的音频数据，可以结束
                            if len(audio_data) > 1000:  # 至少1KB数据
                                break
                        elif isinstance(response, str):
                            # 处理JSON响应
                            try:
                                json_resp = json.loads(response)
                                if 'data' in json_resp:
                                    import base64
                                    audio_chunk = base64.b64decode(json_resp['data'])
                                    audio_data.extend(audio_chunk)
                            except json.JSONDecodeError:
                                pass
                        
                    except asyncio.TimeoutError:
                        break
                
                # 检查是否收到音频数据
                if not audio_data:
                    raise RuntimeError("未收到音频数据")
                
                return bytes(audio_data)
                
            finally:
                await websocket.close()
                
        except Exception as e:
            logger.error(f"WebSocket合成失败: {e}")
            return None

# 第三步：测试函数
async def test_websocket_connection():
    """测试WebSocket连接"""
    print("=== 豆包语音WebSocket连接测试 ===")
    
    config = DOUYIN_CONFIG
    
    # 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)
    
    # 创建TTS对象
    tts = DouyinTTSWebSocket(config)
    
    # 测试文本
    test_text = "这是一个豆包语音合成大模型的测试文本"
    voice_type = "BV001_streaming"
    
    print(f"测试文本: {test_text}")
    print(f"测试音色: {voice_type}")
    
    # 合成语音
    audio_data = await tts.synthesize_text(test_text, voice_type)
    
    if audio_data:
        try:
            # 保存音频文件
            audio_path = os.path.join(config["OUTPUT_DIR"], "test_websocket.wav")
            with open(audio_path, "wb") as f:
                f.write(audio_data)
            print(f"✓ 测试成功！音频已保存到: {audio_path}")
            print(f"✓ 音频大小: {len(audio_data)} 字节")
            return True
        except Exception as e:
            print(f"✗ 保存音频失败: {e}")
            return False
    else:
        print(f"✗ 测试失败")
        return False

def load_text_files(config):
    """加载文本和拼音文件"""
    print("=== 加载文本文件 ===")
    
    # 读取文本文件
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    return texts, pinyins

async def generate_voice_test(voice_config, texts, pinyins, config, max_files=3):
    """生成单个音色的测试音频"""
    voice_type = voice_config["voice_type"]
    voice_name = voice_config["name"]
    voice_code = voice_config["code"]
    
    print(f"\n=== 测试生成: {voice_name} ({voice_type}) ===")
    print(f"限制生成: {max_files} 个文件")
    
    # 选择前几个文本进行测试
    selected_texts = texts[:max_files]
    selected_indices = list(range(max_files))
    actual_chars = sum(len(text) for text in selected_texts)
    
    print(f"选择文本: {len(selected_texts)} 条")
    print(f"实际字数: {actual_chars} 字")
    
    # 创建TTS对象
    tts = DouyinTTSWebSocket(config)
    
    # 统计信息
    success_count = 0
    error_count = 0
    
    # 打开CSV文件进行追加
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    csv_exists = os.path.exists(csv_path)
    
    with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
        # 写入标题行（如果文件不存在）
        if not csv_exists:
            csvfile.write('音频名\t类型\t文本\t注音\t音色\n')
        
        for i, (text_idx, text) in enumerate(zip(selected_indices, selected_texts), 1):
            pinyin = pinyins[text_idx] if text_idx < len(pinyins) else ""
            
            # 音频文件名格式：音色代码 + 序号
            audio_name = f"{voice_code}{i:04d}"
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")
            
            print(f"[{i}/{len(selected_texts)}] {audio_name}: {text[:50]}{'...' if len(text) > 50 else ''}")
            
            # 检查文件是否已存在
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                continue
            
            # 生成音频
            audio_data = await tts.synthesize_text(text, voice_type)
            
            if audio_data:
                try:
                    # 保存音频文件
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)
                    
                    # 写入CSV记录
                    csvfile.write(f"{audio_name}\tc\t{text}\t{pinyin}\t{voice_name}\n")
                    csvfile.flush()
                    
                    print(f"  ✓ 成功 ({len(audio_data)} 字节)")
                    success_count += 1
                    
                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1
            
            # 请求间隔
            await asyncio.sleep(config["REQUEST_INTERVAL"])
    
    print(f"\n测试完成:")
    print(f"  成功: {success_count}")
    print(f"  失败: {error_count}")
    
    return error_count == 0

# 第四步：主函数（在Jupyter中运行）
async def main_jupyter():
    """Jupyter专用主函数"""
    print("=" * 60)
    print("豆包语音合成大模型WebSocket API - Jupyter测试版")
    print("=" * 60)
    
    # 测试WebSocket连接
    success = await test_websocket_connection()
    
    if success:
        print("\n🎉 WebSocket连接测试成功！")
        
        # 加载文本文件
        texts, pinyins = load_text_files(DOUYIN_CONFIG)
        if texts:
            total_chars = sum(len(text) for text in texts)
            print(f"\n文本总字数: {total_chars} 字")
            print(f"可生成音色数: {total_chars // DOUYIN_CONFIG['TARGET_CHARS_PER_VOICE']} 种")
            
            # 测试第一个音色
            voice_config = VOICE_CONFIGS[0]
            success = await generate_voice_test(voice_config, texts, pinyins, DOUYIN_CONFIG, max_files=3)
            
            if success:
                print("\n🎉 音色测试成功！")
                print("可以继续运行完整的批量生成程序")
            else:
                print("\n⚠️ 音色测试失败")
    else:
        print("\n⚠️ WebSocket连接测试失败")

# 在Jupyter中运行这个函数
# await main_jupyter()
