#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拼音覆盖率检查工具
检查音频生成用的拼音集合相对于完整拼音集合的覆盖率
"""

import os
import re
import json
from collections import Counter, defaultdict
from typing import Set, List, Dict, Tuple

# 配置信息
CONFIG = {
    # 完整拼音集合文件
    "COMPLETE_PINYIN_FILE": "pinyin_complete.txt",  
    
    # TTS工具拼音文件配置 - 每个工具有原始+补充两个文件
    "TTS_PINYIN_CONFIGS": {
        "阿里云": {
            "original": "shuffled_text_aliyun_pinyin.txt",
            "supplement": "supplement_aliyun_pinyin_clean.txt"
        },
        "百度云": {
            "original": "shuffled_text_baidu_pinyin.txt",
            "supplement": "supplement_baiduyun_pinyin_clean.txt"
        },
        "豆包": {
            "original": "shuffled_text_doubao_pinyin.txt",
            "supplement": "supplement_doubao_pinyin_clean.txt"
        },
        "初始数据": {
            "original": "shuffled_from_rank_random_pinyin.txt",
            "supplement": ""  # 如果没有补充文件，留空
        }
    },
    
    # 输出报告文件
    "REPORT_FILE": "pinyin_coverage_report_new.txt",
}

def extract_pinyin_from_text(text: str, debug_mode: bool = False) -> Set[str]:
    """
    从文本中提取拼音（支持多种格式）
    支持多种分隔符：空格、制表符、逗号等
    支持多种拼音格式

    Args:
        text: 包含拼音的文本
        debug_mode: 是否显示调试信息

    Returns:
        拼音集合
    """
    # 多种拼音模式
    patterns = [
        (r'[a-zA-Z]+\d+', '标准格式(ni3)'),           # 标准格式：ni3, hao3
        (r'[a-zA-Z]+[1-5]', '数字声调(ni1)'),         # 数字声调：ni1, hao3
        (r'[a-zA-Z]+', '无声调(ni)'),                 # 无声调：ni, hao
    ]

    all_pinyins = set()

    if debug_mode:
        print(f"    原始文本示例: {text[:100]}...")

    # 尝试所有模式
    for pattern, desc in patterns:
        pinyins = re.findall(pattern, text)
        if pinyins and debug_mode:
            print(f"    {desc}: 找到 {len(pinyins)} 个拼音")
            print(f"    示例: {pinyins[:5]}")
        all_pinyins.update(pinyin.lower() for pinyin in pinyins)

    # 过滤掉过短的匹配（可能是误匹配）
    filtered_pinyins = {p for p in all_pinyins if len(p) >= 2}

    if debug_mode:
        print(f"    过滤后拼音数量: {len(filtered_pinyins)}")

    return filtered_pinyins

def load_complete_pinyin_set(file_path: str) -> Set[str]:
    """
    加载完整的拼音集合

    Args:
        file_path: 完整拼音文件路径

    Returns:
        完整拼音集合
    """
    print(f"=== 加载完整拼音集合: {file_path} ===")

    if not os.path.exists(file_path):
        print(f"✗ 文件不存在: {file_path}")
        return set()

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        print(f"✓ 文件大小: {len(content)} 字符")

        # 提取拼音（启用调试模式）
        complete_pinyins = extract_pinyin_from_text(content, debug_mode=True)

        print(f"✓ 完整拼音集合: {len(complete_pinyins)} 个拼音")

        # 显示一些示例
        sample_pinyins = sorted(list(complete_pinyins))[:10]
        print(f"✓ 示例拼音: {', '.join(sample_pinyins)}")

        return complete_pinyins

    except Exception as e:
        print(f"✗ 读取文件失败: {e}")
        return set()

def load_tts_pinyin_files(tts_configs: Dict) -> Dict[str, Set[str]]:
    """
    加载TTS工具的拼音文件（原始+补充的并集）

    Args:
        tts_configs: TTS配置字典，包含每个工具的原始和补充文件路径

    Returns:
        TTS工具名到拼音集合的映射
    """
    print(f"\n=== 加载TTS拼音文件（原始+补充并集） ===")

    tts_pinyins = {}

    for tts_name, config in tts_configs.items():
        print(f"\n处理 {tts_name}:")

        combined_pinyins = set()

        # 加载原始文件
        original_file = config.get("original", "")
        if original_file and os.path.exists(original_file):
            try:
                with open(original_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                original_pinyins = set()
                for line in lines:
                    line_pinyins = extract_pinyin_from_text(line.strip())
                    original_pinyins.update(line_pinyins)

                combined_pinyins.update(original_pinyins)
                print(f"  ✓ 原始文件 ({os.path.basename(original_file)}): {len(original_pinyins)} 个拼音")

            except Exception as e:
                print(f"  ✗ 读取原始文件失败 {original_file}: {e}")
        else:
            print(f"  ⚠️ 原始文件不存在或未配置: {original_file}")

        # 加载补充文件
        supplement_file = config.get("supplement", "")
        if supplement_file and os.path.exists(supplement_file):
            try:
                with open(supplement_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                supplement_pinyins = set()
                for line in lines:
                    line_pinyins = extract_pinyin_from_text(line.strip())
                    supplement_pinyins.update(line_pinyins)

                # 计算新增拼音
                new_pinyins = supplement_pinyins - combined_pinyins
                combined_pinyins.update(supplement_pinyins)

                print(f"  ✓ 补充文件 ({os.path.basename(supplement_file)}): {len(supplement_pinyins)} 个拼音")
                print(f"    新增拼音: {len(new_pinyins)} 个")

            except Exception as e:
                print(f"  ✗ 读取补充文件失败 {supplement_file}: {e}")
        elif supplement_file:
            print(f"  ⚠️ 补充文件不存在: {supplement_file}")
        else:
            print(f"  ℹ️ 未配置补充文件")

        # 保存并集结果
        tts_pinyins[tts_name] = combined_pinyins
        print(f"  📊 {tts_name} 总计: {len(combined_pinyins)} 个拼音")

        # 显示一些示例
        if combined_pinyins:
            sample_pinyins = sorted(list(combined_pinyins))[:5]
            print(f"  示例: {', '.join(sample_pinyins)}")

    return tts_pinyins

def analyze_pinyin_coverage(complete_pinyins: Set[str], audio_pinyins: Dict[str, Set[str]]) -> Dict:
    """
    分析拼音覆盖率
    
    Args:
        complete_pinyins: 完整拼音集合
        audio_pinyins: 音频拼音文件映射
    
    Returns:
        覆盖率分析结果
    """
    print(f"\n=== 分析拼音覆盖率 ===")
    
    if not complete_pinyins:
        print("✗ 完整拼音集合为空，无法分析")
        return {}
    
    analysis_result = {
        'complete_count': len(complete_pinyins),
        'file_analysis': {},
        'union_analysis': {},
        'missing_pinyins': {},
        'unique_pinyins': {},
    }
    
    # 分析每个文件的覆盖率
    for file_name, file_pinyins in audio_pinyins.items():
        if not file_pinyins:
            coverage_rate = 0.0
            covered_pinyins = set()
            missing_pinyins = complete_pinyins.copy()
            unique_pinyins = set()
        else:
            covered_pinyins = file_pinyins.intersection(complete_pinyins)
            missing_pinyins = complete_pinyins - file_pinyins
            unique_pinyins = file_pinyins - complete_pinyins
            coverage_rate = len(covered_pinyins) / len(complete_pinyins) * 100
        
        analysis_result['file_analysis'][file_name] = {
            'total_pinyins': len(file_pinyins),
            'covered_pinyins': len(covered_pinyins),
            'coverage_rate': coverage_rate,
            'missing_count': len(missing_pinyins),
            'unique_count': len(unique_pinyins),
        }
        
        analysis_result['missing_pinyins'][file_name] = missing_pinyins
        analysis_result['unique_pinyins'][file_name] = unique_pinyins
        
        print(f"✓ {file_name}: {coverage_rate:.1f}% 覆盖率 ({len(covered_pinyins)}/{len(complete_pinyins)})")
    
    # 分析所有文件的并集覆盖率
    all_audio_pinyins = set()
    for file_pinyins in audio_pinyins.values():
        all_audio_pinyins.update(file_pinyins)

    # 确保即使没有音频拼音也能正常处理
    union_covered = all_audio_pinyins.intersection(complete_pinyins)
    union_missing = complete_pinyins - all_audio_pinyins
    union_unique = all_audio_pinyins - complete_pinyins
    union_coverage_rate = len(union_covered) / len(complete_pinyins) * 100 if complete_pinyins else 0.0

    analysis_result['union_analysis'] = {
        'total_pinyins': len(all_audio_pinyins),
        'covered_pinyins': len(union_covered),
        'coverage_rate': union_coverage_rate,
        'missing_count': len(union_missing),
        'unique_count': len(union_unique),
    }

    analysis_result['union_missing'] = union_missing
    analysis_result['union_unique'] = union_unique

    print(f"✓ 所有文件并集: {union_coverage_rate:.1f}% 覆盖率 ({len(union_covered)}/{len(complete_pinyins)})")

    # 如果覆盖率很低，显示格式分析
    if union_coverage_rate < 10.0:
        print(f"\n⚠️ 覆盖率很低，可能存在格式不匹配问题")
        print(f"完整集合示例拼音: {sorted(list(complete_pinyins))[:10]}")
        print(f"音频文件示例拼音: {sorted(list(all_audio_pinyins))[:10]}")

    return analysis_result

def generate_detailed_report(analysis_result: Dict, report_file: str):
    """
    生成详细的覆盖率报告
    
    Args:
        analysis_result: 分析结果
        report_file: 报告文件路径
    """
    print(f"\n=== 生成详细报告: {report_file} ===")
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("拼音覆盖率分析报告\n")
            f.write("=" * 60 + "\n\n")
            
            # 基本信息
            f.write(f"完整拼音集合数量: {analysis_result['complete_count']} 个\n\n")
            
            # 各TTS工具覆盖率
            f.write("各TTS工具覆盖率分析:\n")
            f.write("-" * 40 + "\n")

            for tts_name, tts_analysis in analysis_result['file_analysis'].items():
                f.write(f"\nTTS工具: {tts_name}\n")
                f.write(f"  工具拼音数量: {tts_analysis['total_pinyins']}\n")
                f.write(f"  覆盖拼音数量: {tts_analysis['covered_pinyins']}\n")
                f.write(f"  覆盖率: {tts_analysis['coverage_rate']:.2f}%\n")
                f.write(f"  缺失拼音数量: {tts_analysis['missing_count']}\n")
                f.write(f"  独有拼音数量: {tts_analysis['unique_count']}\n")
            
            # 并集覆盖率
            if 'union_analysis' in analysis_result:
                union_analysis = analysis_result['union_analysis']
                f.write(f"\n所有TTS工具并集覆盖率:\n")
                f.write("-" * 40 + "\n")
                f.write(f"  并集拼音数量: {union_analysis['total_pinyins']}\n")
                f.write(f"  覆盖拼音数量: {union_analysis['covered_pinyins']}\n")
                f.write(f"  覆盖率: {union_analysis['coverage_rate']:.2f}%\n")
                f.write(f"  缺失拼音数量: {union_analysis['missing_count']}\n")
                f.write(f"  独有拼音数量: {union_analysis['unique_count']}\n")
            
            # 详细的缺失拼音列表
            f.write(f"\n详细缺失拼音列表:\n")
            f.write("-" * 40 + "\n")
            
            for tts_name, missing_pinyins in analysis_result['missing_pinyins'].items():
                if missing_pinyins:
                    f.write(f"\n{tts_name} 缺失的拼音 ({len(missing_pinyins)} 个):\n")
                    missing_list = sorted(list(missing_pinyins))
                    # 每行显示10个拼音
                    for i in range(0, len(missing_list), 10):
                        line_pinyins = missing_list[i:i+10]
                        f.write(f"  {', '.join(line_pinyins)}\n")
            
            # 并集缺失拼音
            if 'union_missing' in analysis_result and analysis_result['union_missing']:
                f.write(f"\n所有TTS工具并集缺失的拼音 ({len(analysis_result['union_missing'])} 个):\n")
                missing_list = sorted(list(analysis_result['union_missing']))
                for i in range(0, len(missing_list), 10):
                    line_pinyins = missing_list[i:i+10]
                    f.write(f"  {', '.join(line_pinyins)}\n")
            
            # 独有拼音列表
            f.write(f"\n独有拼音列表（不在完整集合中）:\n")
            f.write("-" * 40 + "\n")
            
            for tts_name, unique_pinyins in analysis_result['unique_pinyins'].items():
                if unique_pinyins:
                    f.write(f"\n{tts_name} 独有的拼音 ({len(unique_pinyins)} 个):\n")
                    unique_list = sorted(list(unique_pinyins))
                    for i in range(0, len(unique_list), 10):
                        line_pinyins = unique_list[i:i+10]
                        f.write(f"  {', '.join(line_pinyins)}\n")
        
        print(f"✓ 详细报告已生成: {report_file}")
        
    except Exception as e:
        print(f"✗ 生成报告失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("拼音覆盖率检查工具")
    print("检查音频生成用的拼音集合相对于完整拼音集合的覆盖率")
    print("=" * 60)
    
    config = CONFIG
    
    print("配置信息:")
    print(f"  完整拼音文件: {config['COMPLETE_PINYIN_FILE']}")
    print(f"  TTS工具数量: {len(config['TTS_PINYIN_CONFIGS'])}")
    for tts_name, tts_config in config['TTS_PINYIN_CONFIGS'].items():
        print(f"    {tts_name}:")
        print(f"      原始文件: {tts_config['original']}")
        if tts_config['supplement']:
            print(f"      补充文件: {tts_config['supplement']}")
        else:
            print(f"      补充文件: 无")
    print(f"  报告文件: {config['REPORT_FILE']}")
    print("=" * 60)
    
    # 1. 加载完整拼音集合
    complete_pinyins = load_complete_pinyin_set(config['COMPLETE_PINYIN_FILE'])
    if not complete_pinyins:
        print("✗ 无法加载完整拼音集合，程序退出")
        return
    
    # 2. 加载TTS拼音文件（原始+补充并集）
    tts_pinyins = load_tts_pinyin_files(config['TTS_PINYIN_CONFIGS'])

    # 3. 分析覆盖率
    analysis_result = analyze_pinyin_coverage(complete_pinyins, tts_pinyins)
    
    if not analysis_result:
        print("✗ 覆盖率分析失败，程序退出")
        return
    
    # 4. 生成详细报告
    generate_detailed_report(analysis_result, config['REPORT_FILE'])
    
    # 5. 显示总结
    print(f"\n=== 覆盖率分析总结 ===")
    print(f"完整拼音集合: {analysis_result['complete_count']} 个拼音")

    for tts_name, tts_analysis in analysis_result['file_analysis'].items():
        print(f"{tts_name}: {tts_analysis['coverage_rate']:.1f}% 覆盖率 ({tts_analysis['covered_pinyins']}/{analysis_result['complete_count']})")

    if 'union_analysis' in analysis_result:
        union_analysis = analysis_result['union_analysis']
        print(f"所有TTS工具并集: {union_analysis['coverage_rate']:.1f}% 覆盖率 ({union_analysis['covered_pinyins']}/{analysis_result['complete_count']})")
    
    print(f"\n🎉 拼音覆盖率检查完成！")
    print(f"详细报告: {config['REPORT_FILE']}")
    print("=" * 60)

if __name__ == "__main__":
    main()
