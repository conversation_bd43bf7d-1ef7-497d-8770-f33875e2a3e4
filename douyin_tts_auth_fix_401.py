# ============================================================================
# 豆包TTS HTTP API认证修复版本
# 修复401错误："load grant: requested grant not found in SaaS storage"
# 复制此代码到您的TTS.ipynb中替换现有代码
# ============================================================================

"""
豆包语音合成大模型 - HTTP API认证修复版
修复401认证错误，使用正确的API配置
"""

import requests
import base64
import time
import csv
import os
import uuid
import json
import glob

# 设置日志
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print("✅ 环境设置完成 - 认证修复版")

# ==================== 配置区域 ====================
DOUYIN_CONFIG = {
    # API配置 - 使用之前工作的配置
    "APPID": "1783501808",  # 恢复到之前工作的APPID
    "ACCESS_TOKEN": "zkVeXDcXCG_Jh0LnTVratFjOswIGt4AO",  # 恢复到之前工作的TOKEN
    "CLUSTER": "volcano_tts",
    
    # 音频格式配置
    "ENCODING": "wav",
    "SAMPLE_RATE": 16000,
    "SPEED_RATIO": 1.0,
    "VOLUME_RATIO": 1.0,
    "PITCH_RATIO": 1.0,
    
    # 文件配置
    "TEXT_FILE": "shuffled_text_dedup.txt",
    "PINYIN_FILE": "shuffled_text_dedup_pinyin.txt",
    "OUTPUT_DIR": "audio_output_auth_fixed",
    "CSV_FILE": "douyin_auth_fixed_log.csv",
    "REQUEST_INTERVAL": 1.0,  # 增加请求间隔
    
    # 生成配置
    "TARGET_CHARS_PER_VOICE": 1000,  # 先测试少量文本
}

# 音色配置 - 只测试几种基础音色
VOICE_CONFIGS = [
    {"voice_code": "D001", "voice_type": "BV001_streaming", "name": "通用女声"},
    {"voice_code": "D002", "voice_type": "BV002_streaming", "name": "通用男声"},
    {"voice_code": "D003", "voice_type": "BV007_streaming", "name": "亲切女声"},
]

# API配置
HOST = "openspeech.bytedance.com"
API_URL = f"https://{HOST}/api/v1/tts"

print("✅ 配置加载完成")

def get_cluster(voice_type):
    """获取集群名称"""
    if voice_type.startswith("ICL_") or voice_type.startswith("S_"):
        return "volcano_icl"
    return "volcano_tts"

class DouyinTTSAuthFixed:
    """豆包TTS认证修复版"""
    
    def __init__(self, config):
        self.config = config
        self.success_count = 0
        self.error_count = 0
        
    def get_auth_headers_v1(self):
        """认证方式1: Bearer;token"""
        return {
            "Authorization": f"Bearer;{self.config['ACCESS_TOKEN']}",
            "Content-Type": "application/json"
        }
    
    def get_auth_headers_v2(self):
        """认证方式2: Bearer token"""
        return {
            "Authorization": f"Bearer {self.config['ACCESS_TOKEN']}",
            "Content-Type": "application/json"
        }
    
    def synthesize_text_with_auth_test(self, text, voice_type):
        """测试不同认证方式合成文本"""
        cluster = get_cluster(voice_type)
        
        # 基础请求数据
        request_json = {
            "app": {
                "appid": self.config["APPID"],
                "token": self.config["ACCESS_TOKEN"],
                "cluster": cluster
            },
            "user": {
                "uid": str(uuid.uuid4())
            },
            "audio": {
                "voice_type": voice_type,
                "encoding": self.config["ENCODING"],
                "sample_rate": self.config["SAMPLE_RATE"],
                "speed_ratio": self.config["SPEED_RATIO"],
                "volume_ratio": self.config["VOLUME_RATIO"],
                "pitch_ratio": self.config["PITCH_RATIO"],
            },
            "request": {
                "reqid": str(uuid.uuid4()),
                "text": text,
                "text_type": "plain",
                "operation": "query",
                "with_frontend": 1,
                "frontend_type": "unitTson"
            }
        }
        
        # 尝试不同的认证方式
        auth_methods = [
            ("Bearer;token", self.get_auth_headers_v1()),
            ("Bearer token", self.get_auth_headers_v2()),
        ]
        
        for auth_name, headers in auth_methods:
            print(f"    🔑 尝试认证方式: {auth_name}")
            
            try:
                resp = requests.post(API_URL, json.dumps(request_json), headers=headers, timeout=30)
                
                print(f"    📥 响应状态: {resp.status_code}")
                
                if resp.status_code == 200:
                    resp_data = resp.json()
                    if "data" in resp_data:
                        audio_data = base64.b64decode(resp_data["data"])
                        print(f"    ✅ 认证成功！音频大小: {len(audio_data)} 字节")
                        self.success_count += 1
                        return audio_data, auth_name, headers
                    else:
                        print(f"    ⚠️ 响应无音频数据: {resp_data}")
                elif resp.status_code == 401:
                    resp_data = resp.json() if resp.headers.get('content-type', '').startswith('application/json') else {}
                    error_code = resp_data.get('code', 'N/A')
                    error_msg = resp_data.get('message', resp.text)
                    print(f"    ❌ 认证失败 [{error_code}]: {error_msg}")
                else:
                    print(f"    ❌ HTTP错误 {resp.status_code}: {resp.text}")
                    
            except requests.exceptions.Timeout:
                print(f"    ⏰ 请求超时")
            except Exception as e:
                print(f"    ❌ 请求异常: {e}")
            
            time.sleep(1)  # 认证测试间隔
        
        print(f"    ❌ 所有认证方式都失败")
        self.error_count += 1
        return None, None, None

def load_text_files(config):
    """加载文本文件"""
    print("=== 加载文本文件 ===")
    
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
        return texts
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return []

def test_api_authentication():
    """测试API认证"""
    print("=" * 60)
    print("豆包TTS API认证测试")
    print("=" * 60)
    
    config = DOUYIN_CONFIG
    
    print("当前配置:")
    print(f"  APPID: {config['APPID']}")
    print(f"  TOKEN: {config['ACCESS_TOKEN'][:20]}...")
    print(f"  API端点: {API_URL}")
    
    # 加载测试文本
    texts = load_text_files(config)
    if not texts:
        print("❌ 无法加载测试文本")
        return False
    
    test_text = texts[0]
    print(f"  测试文本: '{test_text}'")
    
    # 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)
    
    # 测试每种音色的认证
    tts = DouyinTTSAuthFixed(config)
    
    for i, voice_config in enumerate(VOICE_CONFIGS, 1):
        voice_type = voice_config["voice_type"]
        voice_name = voice_config["name"]
        voice_code = voice_config["voice_code"]
        
        print(f"\n🎵 测试音色 {i}/{len(VOICE_CONFIGS)}: {voice_name}")
        print(f"  音色类型: {voice_type}")
        print(f"  集群: {get_cluster(voice_type)}")
        
        # 测试认证和合成
        audio_data, working_auth, working_headers = tts.synthesize_text_with_auth_test(test_text, voice_type)
        
        if audio_data:
            # 保存测试文件
            test_file = os.path.join(config["OUTPUT_DIR"], f"auth_test_{voice_code}.wav")
            with open(test_file, "wb") as f:
                f.write(audio_data)
            
            print(f"  🎉 {voice_name} 认证成功！")
            print(f"  ✅ 工作的认证方式: {working_auth}")
            print(f"  ✅ 测试文件: {test_file}")
            
            return True, working_auth, working_headers
        else:
            print(f"  ❌ {voice_name} 认证失败")
        
        time.sleep(2)  # 音色测试间隔
    
    print(f"\n❌ 所有音色认证都失败")
    return False, None, None

def main():
    """主函数 - 认证修复版"""
    print("🔐 豆包TTS API认证修复程序")
    print("此程序将测试API认证并诊断401错误")
    
    # 测试API认证
    success, working_auth, working_headers = test_api_authentication()
    
    if success:
        print(f"\n🎯 认证修复成功！")
        print(f"✅ 工作的认证方式: {working_auth}")
        print(f"✅ 可以继续使用此配置进行批量生成")
        
        print(f"\n📋 建议的配置:")
        print(f"APPID: {DOUYIN_CONFIG['APPID']}")
        print(f"ACCESS_TOKEN: {DOUYIN_CONFIG['ACCESS_TOKEN']}")
        print(f"认证头: {working_headers}")
        
    else:
        print(f"\n❌ 认证修复失败")
        print(f"\n🔧 可能的解决方案:")
        print(f"1. 检查豆包控制台中的API密钥状态")
        print(f"2. 确认APPID和ACCESS_TOKEN是否正确")
        print(f"3. 检查账户是否有TTS服务权限")
        print(f"4. 确认账户余额是否充足")
        print(f"5. 联系豆包技术支持获取帮助")
        
        print(f"\n🔍 错误分析:")
        print(f"'load grant: requested grant not found in SaaS storage' 通常表示:")
        print(f"- API密钥无效或已过期")
        print(f"- APPID与ACCESS_TOKEN不匹配")
        print(f"- 账户没有相应的服务权限")

print("✅ 认证修复程序准备完成")

# 运行认证测试
print("\n🚀 开始运行豆包TTS认证修复程序...")
main()

print("\n📋 认证测试完成！")
print("请根据上面的结果调整API配置。")
