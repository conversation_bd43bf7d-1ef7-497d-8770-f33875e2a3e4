#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包TTS官方代码测试版
基于官方示例代码进行测试
"""

import asyncio
import json
import logging
import uuid
import os
import time
import websockets
from protocols import MsgType, full_client_request, receive_message

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置信息
CONFIG = {
    "APPID": "9862368305",
    "ACCESS_TOKEN": "bbxNaAzRzBSkySjouDItXl6tBUUyNd2W",
    "ENDPOINT": "wss://openspeech.bytedance.com/api/v1/tts/ws_binary",
    "OUTPUT_DIR": "audio_output_official_test",
}

def get_cluster(voice: str) -> str:
    """获取集群名称"""
    if voice.startswith("S_"):
        return "volcano_icl"
    return "volcano_tts"

async def synthesize_text_official(appid, access_token, voice_type, text, encoding="wav", endpoint=None):
    """
    使用官方代码逻辑进行TTS合成
    """
    if endpoint is None:
        endpoint = CONFIG["ENDPOINT"]
    
    # 确定集群
    cluster = get_cluster(voice_type)

    # 连接到服务器
    headers = {
        "Authorization": f"Bearer;{access_token}",
    }

    logger.info(f"连接到 {endpoint}")
    websocket = await websockets.connect(
        endpoint, additional_headers=headers, max_size=10 * 1024 * 1024
    )
    
    try:
        logger.info(f"已连接到WebSocket服务器, Logid: {websocket.response.headers.get('x-tt-logid', 'N/A')}")

        # 准备请求负载
        request = {
            "app": {
                "appid": appid,
                "token": access_token,
                "cluster": cluster,
            },
            "user": {
                "uid": str(uuid.uuid4()),
            },
            "audio": {
                "voice_type": voice_type,
                "encoding": encoding,
            },
            "request": {
                "reqid": str(uuid.uuid4()),
                "text": text,
                "operation": "submit",
                "with_timestamp": "1",
                "extra_param": json.dumps(
                    {
                        "disable_markdown_filter": False,
                    }
                ),
            },
        }

        # 发送请求
        logger.info(f"发送TTS请求: {text}")
        await full_client_request(websocket, json.dumps(request).encode())

        # 接收音频数据
        audio_data = bytearray()
        while True:
            msg = await receive_message(websocket)

            if msg.type == MsgType.FrontEndResultServer:
                continue
            elif msg.type == MsgType.AudioOnlyServer:
                audio_data.extend(msg.payload)
                if msg.sequence < 0:  # 最后一条消息
                    break
            elif msg.type == MsgType.ErrorServer:
                # 解析错误消息
                try:
                    error_data = json.loads(msg.payload.decode('utf-8'))
                    error_msg = error_data.get('message', '未知错误')
                    error_code = error_data.get('code', 'N/A')
                    raise RuntimeError(f"服务器错误 [{error_code}]: {error_msg}")
                except json.JSONDecodeError:
                    raise RuntimeError(f"服务器错误: {msg.payload.decode('utf-8', errors='ignore')}")
            else:
                raise RuntimeError(f"未知消息类型: {msg.type}, 内容: {msg}")

        # 检查是否收到音频数据
        if not audio_data:
            raise RuntimeError("未收到音频数据")

        logger.info(f"音频接收完成: {len(audio_data)} 字节")
        return bytes(audio_data)

    finally:
        await websocket.close()
        logger.info("连接已关闭")

async def test_official_tts():
    """测试官方TTS代码"""
    print("=" * 60)
    print("豆包TTS官方代码测试")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs(CONFIG["OUTPUT_DIR"], exist_ok=True)
    
    # 测试配置
    test_cases = [
        {"text": "你好", "voice": "zh_female_wanqudashu_moon_bigtts", "name": "婉曲大叔"},
        {"text": "测试", "voice": "zh_female_daimengchuanmei_moon_bigtts", "name": "黛梦传媒"},
        {"text": "中国", "voice": "zh_male_guozhoudege_moon_bigtts", "name": "国州德哥"},
        {"text": "北京", "voice": "zh_male_beijingxiaoye_moon_bigtts", "name": "北京小爷"},
        {"text": "上海", "voice": "zh_male_shaonianzixin_moon_bigtts", "name": "少年子心"},
    ]
    
    print(f"测试用例: {len(test_cases)} 个")
    for i, case in enumerate(test_cases, 1):
        print(f"  {i}. 文本: '{case['text']}', 音色: {case['name']}")
    
    # 确认开始测试
    try:
        confirm = input(f"\n是否开始官方代码测试？(y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("测试取消")
            return
    except KeyboardInterrupt:
        print("\n测试被中断")
        return
    
    print(f"\n=== 开始测试 ===")
    start_time = time.time()
    
    success_count = 0
    error_count = 0
    
    for i, case in enumerate(test_cases, 1):
        text = case["text"]
        voice_type = case["voice"]
        voice_name = case["name"]
        
        print(f"\n[{i}/{len(test_cases)}] 测试: '{text}' - {voice_name}")
        
        try:
            # 合成音频
            audio_data = await synthesize_text_official(
                appid=CONFIG["APPID"],
                access_token=CONFIG["ACCESS_TOKEN"],
                voice_type=voice_type,
                text=text,
                encoding="wav",
                endpoint=CONFIG["ENDPOINT"]
            )
            
            # 保存音频文件
            filename = f"test_{i:02d}_{text}_{voice_name}.wav"
            filepath = os.path.join(CONFIG["OUTPUT_DIR"], filename)
            
            with open(filepath, "wb") as f:
                f.write(audio_data)
            
            print(f"  ✅ 成功: {filename} ({len(audio_data)} 字节)")
            success_count += 1
            
        except Exception as e:
            print(f"  ❌ 失败: {e}")
            error_count += 1
        
        # 请求间隔
        if i < len(test_cases):
            print(f"  等待 2 秒...")
            await asyncio.sleep(2)
    
    # 最终统计
    elapsed_time = time.time() - start_time
    total = success_count + error_count
    success_rate = (success_count / total * 100) if total > 0 else 0
    
    print(f"\n=== 测试完成 ===")
    print(f"总耗时: {elapsed_time:.1f} 秒")
    print(f"成功: {success_count} 个")
    print(f"失败: {error_count} 个")
    print(f"成功率: {success_rate:.1f}%")
    print(f"输出目录: {CONFIG['OUTPUT_DIR']}")
    
    if success_rate >= 80:
        print("🎉 官方代码测试成功！可以应用到多音色生成程序中")
        
        # 生成应用建议
        print(f"\n📋 应用建议:")
        print(f"1. 将官方代码逻辑集成到多音色生成程序中")
        print(f"2. 使用 msg.type == MsgType.AudioOnlyServer 处理音频消息")
        print(f"3. 使用 msg.sequence < 0 判断最后一条消息")
        print(f"4. 保持 2-3 秒的请求间隔")
        
    else:
        print("⚠️ 仍有问题，需要检查API配置或网络环境")

async def test_single_synthesis():
    """测试单个文本合成"""
    print("=" * 60)
    print("单个文本合成测试")
    print("=" * 60)
    
    # 获取用户输入
    try:
        text = input("请输入要合成的文本: ").strip()
        if not text:
            text = "你好，这是一个测试"
        
        print(f"合成文本: '{text}'")
        
        # 合成音频
        audio_data = await synthesize_text_official(
            appid=CONFIG["APPID"],
            access_token=CONFIG["ACCESS_TOKEN"],
            voice_type="zh_female_wanqudashu_moon_bigtts",
            text=text,
            encoding="wav"
        )
        
        # 保存文件
        filename = f"single_test_{int(time.time())}.wav"
        filepath = os.path.join(CONFIG["OUTPUT_DIR"], filename)
        
        os.makedirs(CONFIG["OUTPUT_DIR"], exist_ok=True)
        with open(filepath, "wb") as f:
            f.write(audio_data)
        
        print(f"✅ 合成成功!")
        print(f"文件保存: {filepath}")
        print(f"音频大小: {len(audio_data)} 字节")
        
    except Exception as e:
        print(f"❌ 合成失败: {e}")

def main():
    """主函数"""
    print("豆包TTS官方代码测试工具")
    print("1. 批量测试 (test)")
    print("2. 单个测试 (single)")
    
    try:
        choice = input("请选择测试模式 (1/2): ").strip()
        
        if choice == "1" or choice.lower() == "test":
            asyncio.run(test_official_tts())
        elif choice == "2" or choice.lower() == "single":
            asyncio.run(test_single_synthesis())
        else:
            print("无效选择，默认运行批量测试")
            asyncio.run(test_official_tts())
            
    except KeyboardInterrupt:
        print("\n程序被中断")

if __name__ == "__main__":
    main()
