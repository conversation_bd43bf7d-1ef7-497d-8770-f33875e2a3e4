# 音频数据初筛工具使用说明

## 功能概述
这个工具用于检测TTS生成的音频文件中的质量问题，包括：
- 静音音频检测
- 超短音频检测  
- 前后静音时间检查
- 音频质量评估

## 使用方法

### 基本用法
`ash
python audio_screening_tool.py
`

### 高级用法
`ash
python audio_screening_tool.py --directory ./audio_files --max-files 100 --output-dir ./reports
`

### 参数说明
- --directory, -d: 音频文件目录 (默认: 当前目录)
- --max-files, -m: 最大处理文件数量 (可选)
- --silence-threshold, -s: 静音检测阈值 (默认: 0.01)
- --min-duration: 最小音频时长(秒) (默认: 0.5)
- --target-silence: 目标静音时长(秒) (默认: 0.2)
- --silence-tolerance: 静音时长容差(秒) (默认: 0.05)
- --output-dir, -o: 报告输出目录 (默认: 当前目录)

## 输出文件

### 1. 完整报告 (audio_screening_full_YYYYMMDD_HHMMSS.csv)
包含所有音频文件的详细分析结果

### 2. 问题文件报告 (audio_screening_problems_YYYYMMDD_HHMMSS.csv)  
只包含有问题的音频文件

### 3. 摘要报告 (audio_screening_summary_YYYYMMDD_HHMMSS.json)
包含统计摘要信息

## 检测的问题类型

1. **完全静音**: 音频文件几乎没有声音
2. **时长过短**: 音频时长小于最小要求
3. **开头静音异常**: 开头静音时间不在目标范围内
4. **结尾静音异常**: 结尾静音时间不在目标范围内
5. **音频音量过低**: 音频振幅过小
6. **音频可能削峰**: 音频振幅过大

## 当前检测结果

根据对50个样本文件的分析：
- 所有文件都存在开头静音时间过短的问题（平均0.025秒，期望0.20.05秒）
- 14个文件存在结尾静音时间异常的问题
- 1个文件时长过短
- 没有完全静音的文件

## 建议

1. **调整TTS生成参数**，增加音频开头和结尾的静音时间到200ms左右
2. **检查TTS配置**，确保生成的音频时长符合要求
3. **定期运行此工具**进行质量监控

## 技术细节

- 支持WAV格式音频文件
- 使用numpy进行音频数据处理
- 静音检测基于归一化振幅阈值
- 支持单声道和立体声音频
