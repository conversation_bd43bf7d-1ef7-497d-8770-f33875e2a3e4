#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络诊断工具 - 检查讯飞TTS连接问题
"""

import socket
import requests
import subprocess
import sys
import time

def test_dns_resolution():
    """测试DNS解析"""
    print("=== DNS解析测试 ===")
    
    hosts = [
        "tts-api.xfyun.cn",
        "ws-api.xfyun.cn", 
        "www.xfyun.cn"
    ]
    
    for host in hosts:
        try:
            ip = socket.gethostbyname(host)
            print(f"✓ {host} -> {ip}")
        except Exception as e:
            print(f"✗ {host} -> 解析失败: {e}")

def test_ping():
    """测试ping连通性"""
    print("\n=== Ping连通性测试 ===")
    
    hosts = [
        "tts-api.xfyun.cn",
        "www.xfyun.cn"
    ]
    
    for host in hosts:
        try:
            # Windows ping命令
            result = subprocess.run(
                ["ping", "-n", "3", host], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            if result.returncode == 0:
                print(f"✓ {host} - Ping成功")
            else:
                print(f"✗ {host} - Ping失败")
        except Exception as e:
            print(f"✗ {host} - Ping异常: {e}")

def test_http_connection():
    """测试HTTP连接"""
    print("\n=== HTTP连接测试 ===")
    
    urls = [
        "https://www.xfyun.cn",
        "https://tts-api.xfyun.cn"
    ]
    
    for url in urls:
        try:
            response = requests.get(url, timeout=10)
            print(f"✓ {url} - HTTP状态: {response.status_code}")
        except requests.exceptions.Timeout:
            print(f"✗ {url} - HTTP超时")
        except requests.exceptions.ConnectionError:
            print(f"✗ {url} - HTTP连接失败")
        except Exception as e:
            print(f"✗ {url} - HTTP异常: {e}")

def test_websocket_port():
    """测试WebSocket端口连通性"""
    print("\n=== WebSocket端口测试 ===")
    
    hosts = [
        ("tts-api.xfyun.cn", 443),
        ("ws-api.xfyun.cn", 443)
    ]
    
    for host, port in hosts:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"✓ {host}:{port} - 端口开放")
            else:
                print(f"✗ {host}:{port} - 端口不可达")
        except Exception as e:
            print(f"✗ {host}:{port} - 测试异常: {e}")

def test_proxy_settings():
    """检查代理设置"""
    print("\n=== 代理设置检查 ===")
    
    import os
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    
    has_proxy = False
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"发现代理设置: {var} = {value}")
            has_proxy = True
    
    if not has_proxy:
        print("✓ 未发现系统代理设置")
    else:
        print("⚠️ 发现代理设置，可能影响WebSocket连接")

def test_firewall_suggestions():
    """防火墙建议"""
    print("\n=== 防火墙和安全软件建议 ===")
    print("如果上述测试失败，请检查:")
    print("1. Windows防火墙是否阻止了Python程序")
    print("2. 企业防火墙是否阻止了WebSocket连接")
    print("3. 杀毒软件是否阻止了网络连接")
    print("4. 是否在企业网络环境中（可能有网络限制）")
    print("5. 尝试使用手机热点测试网络环境")

def test_websocket_libraries():
    """检查WebSocket库"""
    print("\n=== WebSocket库检查 ===")
    
    try:
        import websocket
        print(f"✓ websocket-client版本: {websocket.__version__}")
    except ImportError:
        print("✗ websocket-client未安装")
        print("请运行: pip install websocket-client")
    
    try:
        import ssl
        print(f"✓ SSL支持: {ssl.OPENSSL_VERSION}")
    except ImportError:
        print("✗ SSL支持缺失")

def main():
    """主函数"""
    print("=" * 60)
    print("讯飞TTS网络诊断工具")
    print("=" * 60)
    
    print("开始网络诊断，请稍候...")
    
    # 运行所有测试
    test_websocket_libraries()
    test_dns_resolution()
    test_ping()
    test_http_connection()
    test_websocket_port()
    test_proxy_settings()
    test_firewall_suggestions()
    
    print("\n" + "=" * 60)
    print("诊断完成")
    print("=" * 60)
    
    print("\n💡 解决建议:")
    print("1. 如果DNS解析失败，检查网络连接")
    print("2. 如果Ping失败，可能是网络不通或被阻止")
    print("3. 如果HTTP连接失败，检查防火墙设置")
    print("4. 如果WebSocket端口不通，可能是企业网络限制")
    print("5. 尝试关闭防火墙和杀毒软件测试")
    print("6. 尝试使用不同的网络环境（如手机热点）")

if __name__ == "__main__":
    main()
