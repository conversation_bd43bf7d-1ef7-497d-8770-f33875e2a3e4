# 讯飞TTS多音色生成工具 - 修改说明

## 🔄 修改内容

根据您的要求，我已经修改了讯飞TTS工具，现在的配置如下：

### 📊 新的配置

#### 1. 文本范围
- **使用文本**: 文本文件的第1条到第1000条
- **文件命名**: Sc0010001.wav 到 Sc0011000.wav

#### 2. 音色配置（5种音色，每种200条）
```
1. 讯飞小燕 (x4_xiaoyan): Sc0010001-Sc0010200 (200条)
2. 讯飞小露 (x4_yezi): Sc0010201-Sc0010400 (200条)  
3. 讯飞许久 (aisjiuxu): Sc0010401-Sc0010600 (200条)
4. 讯飞小婧 (aisjinger): Sc0010601-Sc0010800 (200条)
5. 讯飞许小宝 (aisbabyxu): Sc0010801-Sc0011000 (200条)
```

### 🔧 技术实现

#### 文本索引映射逻辑
```python
# 配置参数
"START_INDEX": 10001,      # CSV中记录的文件名起始
"END_INDEX": 11000,        # CSV中记录的文件名结束  
"TEXT_START_INDEX": 1,     # 文本文件中的起始行（第1条）
"TEXT_END_INDEX": 1000,    # 文本文件中的结束行（第1000条）

# 映射计算
text_offset = index - config["START_INDEX"]  # 相对偏移
text_index = config["TEXT_START_INDEX"] - 1 + text_offset  # 实际文本索引
```

#### 映射关系验证
- ✅ Sc0010001.wav → 文本第1行
- ✅ Sc0010200.wav → 文本第200行  
- ✅ Sc0010201.wav → 文本第201行
- ✅ Sc0010400.wav → 文本第400行
- ✅ Sc0010401.wav → 文本第401行
- ✅ Sc0010600.wav → 文本第600行
- ✅ Sc0010601.wav → 文本第601行
- ✅ Sc0010800.wav → 文本第800行
- ✅ Sc0010801.wav → 文本第801行
- ✅ Sc0011000.wav → 文本第1000行

### 📁 修改的文件

1. **`xunfei_tts_multi_voice_1000.py`** - 主程序（已修改）
   - 更新了音色配置为5种音色
   - 修改了文本索引映射逻辑
   - 更新了配置参数显示

2. **`xunfei_tts_test_modified.py`** - 测试验证程序（新增）
   - 验证文本索引映射逻辑
   - 测试音色配置正确性

### 🎯 输出结果

#### CSV记录格式
```csv
"音频名","类型","文本","注音1","音色"
"Sc0010001","c","被使","bei4 shi3","讯飞小燕"
"Sc0010002","c","起做","qi3 zuo4","讯飞小燕"
...
"Sc0010201","c","以两也","yi3 liang3 ye3","讯飞小露"
...
"Sc0010401","c","去里一是上","qu4 li3 yi1 shi4 shang4","讯飞许久"
...
"Sc0010601","c","对下中国也这","dui4 xia4 zhong1'guo2 ye3 zhe4","讯飞小婧"
...
"Sc0010801","c","我把他的什么最","wo3 ba3 ta1 de shen2'me zui4","讯飞许小宝"
...
"Sc0011000","c","重要向已家会三。","zhong4'yao4 xiang4 yi3 jia1 hui4 san1。","讯飞许小宝"
```

#### 音频文件
- **总数**: 1000个WAV文件
- **格式**: 16kHz, 16bit, 单声道
- **命名**: Sc0010001.wav - Sc0011000.wav
- **分布**: 每种音色200个文件

### ✅ 验证结果

通过 `xunfei_tts_test_modified.py` 测试验证：
- ✅ 文本文件加载正常（10000行）
- ✅ 拼音文件加载正常（10000行）
- ✅ 音色配置正确（5种音色，每种200条）
- ✅ 文本索引映射正确（第1-1000条）
- ✅ 文件命名范围正确（Sc0010001-Sc0011000）

### 🚀 使用方法

1. **验证配置**:
   ```bash
   python xunfei_tts_test_modified.py
   ```

2. **开始生成**:
   ```bash
   python xunfei_tts_multi_voice_1000.py
   ```

### 📈 预期输出

运行完成后将生成：
- **1000个音频文件**: Sc0010001.wav - Sc0011000.wav
- **1个CSV记录文件**: xunfei_audio_info_1000.csv
- **音色分布**:
  - 讯飞小燕: 200个文件（Sc0010001-Sc0010200）
  - 讯飞小露: 200个文件（Sc0010201-Sc0010400）
  - 讯飞许久: 200个文件（Sc0010401-Sc0010600）
  - 讯飞小婧: 200个文件（Sc0010601-Sc0010800）
  - 讯飞许小宝: 200个文件（Sc0010801-Sc0011000）

### ⚠️ 注意事项

1. **网络连接**: 确保能正常访问讯飞WebSocket API
2. **API配置**: 确认APPID、APIKey、APISecret正确
3. **文本文件**: 确保文本文件至少有1000行内容
4. **存储空间**: 预留足够空间存储1000个音频文件

### 🎉 总结

修改已完成，现在的讯飞TTS工具完全符合您的要求：
- ✅ 使用文本文件的第1-1000条
- ✅ 生成文件名为Sc0010001.wav到Sc0011000.wav
- ✅ 5种音色每种生成200条
- ✅ 完整的CSV记录文件

一旦网络连接问题解决，即可开始生成符合要求的1000条讯飞多音色音频文件！
