#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拼音匹配诊断工具
诊断拼音匹配失败的原因：编码问题、不可见字符、格式差异等
"""

import os
import re
import unicodedata
from collections import defaultdict
from typing import Set, List, Dict

# 配置信息
CONFIG = {
    "COMPLETE_PINYIN_FILE": "pinyin_complete.txt",
    "SAMPLE_TTS_FILE": "shuffled_text_aliyun_pinyin.txt",  # 用于对比的TTS文件
    "DIAGNOSTIC_REPORT": "pinyin_diagnostic_report.txt",
}

def analyze_character_details(text: str) -> Dict:
    """
    分析字符串的详细信息：编码、Unicode、不可见字符等
    """
    details = {
        'text': text,
        'length': len(text),
        'bytes_utf8': text.encode('utf-8'),
        'bytes_length': len(text.encode('utf-8')),
        'unicode_points': [ord(c) for c in text],
        'unicode_names': [],
        'categories': [],
        'invisible_chars': [],
        'normalized_nfc': unicodedata.normalize('NFC', text),
        'normalized_nfd': unicodedata.normalize('NFD', text),
    }
    
    for i, char in enumerate(text):
        try:
            name = unicodedata.name(char, f'UNKNOWN_{ord(char)}')
            category = unicodedata.category(char)
            details['unicode_names'].append(name)
            details['categories'].append(category)
            
            # 检查不可见字符
            if category in ['Cc', 'Cf', 'Cs', 'Co', 'Cn'] or ord(char) < 32:
                details['invisible_chars'].append((i, char, ord(char), name))
        except:
            details['unicode_names'].append(f'ERROR_{ord(char)}')
            details['categories'].append('UNKNOWN')
    
    return details

def extract_pinyin_with_positions(text: str) -> List[Dict]:
    """
    提取拼音并记录位置信息
    """
    patterns = [
        (r'[a-zA-Z]+\d+', '标准格式(ni3)'),
        (r'[a-zA-Z]+[1-5]', '数字声调(ni1)'),
        (r'[a-zü]+', '无声调(ni)'),
    ]
    
    all_matches = []
    
    for pattern, desc in patterns:
        for match in re.finditer(pattern, text):
            pinyin = match.group().lower()
            if len(pinyin) >= 2:  # 过滤短匹配
                all_matches.append({
                    'pinyin': pinyin,
                    'start': match.start(),
                    'end': match.end(),
                    'pattern': desc,
                    'context_before': text[max(0, match.start()-5):match.start()],
                    'context_after': text[match.end():match.end()+5],
                    'details': analyze_character_details(pinyin)
                })
    
    return all_matches

def load_and_analyze_complete_pinyin(file_path: str) -> Dict:
    """
    加载并分析完整拼音文件
    """
    print(f"=== 分析完整拼音文件: {file_path} ===")
    
    if not os.path.exists(file_path):
        print(f"✗ 文件不存在: {file_path}")
        return {}
    
    try:
        # 尝试不同编码读取
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'latin-1']
        content = None
        used_encoding = None
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                used_encoding = encoding
                print(f"✓ 成功使用编码: {encoding}")
                break
            except UnicodeDecodeError:
                continue
        
        if content is None:
            print("✗ 无法用常见编码读取文件")
            return {}
        
        # 文件基本信息
        file_info = {
            'encoding': used_encoding,
            'file_size': len(content),
            'line_count': len(content.split('\n')),
            'char_count': len(content),
            'byte_count': len(content.encode('utf-8')),
        }
        
        print(f"文件信息:")
        print(f"  编码: {file_info['encoding']}")
        print(f"  文件大小: {file_info['char_count']} 字符")
        print(f"  行数: {file_info['line_count']}")
        print(f"  字节数: {file_info['byte_count']}")
        
        # 提取拼音
        pinyin_matches = extract_pinyin_with_positions(content)
        unique_pinyins = {}
        
        for match in pinyin_matches:
            pinyin = match['pinyin']
            if pinyin not in unique_pinyins:
                unique_pinyins[pinyin] = []
            unique_pinyins[pinyin].append(match)
        
        print(f"✓ 提取拼音: {len(unique_pinyins)} 个唯一拼音")
        
        # 分析前10个拼音的详细信息
        print(f"\n前10个拼音详细分析:")
        for i, (pinyin, matches) in enumerate(list(unique_pinyins.items())[:10]):
            first_match = matches[0]
            details = first_match['details']
            print(f"  {i+1}. '{pinyin}' (出现{len(matches)}次)")
            print(f"     Unicode: {details['unicode_points']}")
            print(f"     字节: {details['bytes_utf8']}")
            if details['invisible_chars']:
                print(f"     不可见字符: {details['invisible_chars']}")
        
        return {
            'file_info': file_info,
            'content': content,
            'pinyin_matches': pinyin_matches,
            'unique_pinyins': unique_pinyins,
            'pinyin_set': set(unique_pinyins.keys())
        }
        
    except Exception as e:
        print(f"✗ 分析文件失败: {e}")
        return {}

def compare_pinyin_matching(complete_analysis: Dict, sample_file: str) -> Dict:
    """
    对比拼音匹配情况
    """
    print(f"\n=== 对比拼音匹配: {sample_file} ===")
    
    if not complete_analysis or not os.path.exists(sample_file):
        return {}
    
    # 加载样本文件
    try:
        with open(sample_file, 'r', encoding='utf-8') as f:
            sample_content = f.read()
        
        sample_matches = extract_pinyin_with_positions(sample_content)
        sample_pinyins = set(match['pinyin'] for match in sample_matches)
        
        print(f"✓ 样本文件拼音: {len(sample_pinyins)} 个")
        
    except Exception as e:
        print(f"✗ 读取样本文件失败: {e}")
        return {}
    
    complete_pinyins = complete_analysis['pinyin_set']
    
    # 匹配分析
    matched = sample_pinyins.intersection(complete_pinyins)
    not_matched = sample_pinyins - complete_pinyins
    
    print(f"匹配结果:")
    print(f"  匹配成功: {len(matched)} 个")
    print(f"  匹配失败: {len(not_matched)} 个")
    
    # 分析匹配失败的原因
    mismatch_analysis = []
    
    for pinyin in list(not_matched)[:10]:  # 分析前10个失败的
        print(f"\n分析失败拼音: '{pinyin}'")
        
        # 在完整文件中查找相似的拼音
        similar_candidates = []
        for complete_pinyin in complete_pinyins:
            # 检查是否只是编码差异
            if pinyin.lower() == complete_pinyin.lower():
                similar_candidates.append(('大小写差异', complete_pinyin))
            elif pinyin.replace(' ', '') == complete_pinyin.replace(' ', ''):
                similar_candidates.append(('空格差异', complete_pinyin))
            elif len(pinyin) == len(complete_pinyin):
                diff_count = sum(c1 != c2 for c1, c2 in zip(pinyin, complete_pinyin))
                if diff_count <= 2:
                    similar_candidates.append(('字符差异', complete_pinyin))
        
        if similar_candidates:
            print(f"  找到相似候选: {similar_candidates[:3]}")
        else:
            print(f"  未找到相似候选")
        
        # 详细字符分析
        details = analyze_character_details(pinyin)
        print(f"  Unicode: {details['unicode_points']}")
        print(f"  字节: {details['bytes_utf8']}")
        if details['invisible_chars']:
            print(f"  不可见字符: {details['invisible_chars']}")
        
        mismatch_analysis.append({
            'pinyin': pinyin,
            'details': details,
            'similar_candidates': similar_candidates
        })
    
    return {
        'matched': matched,
        'not_matched': not_matched,
        'mismatch_analysis': mismatch_analysis
    }

def manual_search_test(complete_analysis: Dict) -> Dict:
    """
    手动搜索测试
    """
    print(f"\n=== 手动搜索测试 ===")
    
    if not complete_analysis:
        return {}
    
    content = complete_analysis['content']
    
    # 测试一些常见拼音
    test_pinyins = ['ni3', 'hao3', 'shi4', 'jie4', 'wo3', 'de5', 'zhe4', 'yi1']
    
    search_results = {}
    
    for test_pinyin in test_pinyins:
        print(f"\n搜索测试: '{test_pinyin}'")
        
        # 直接字符串搜索
        if test_pinyin in content:
            print(f"  ✓ 直接搜索: 找到")
        else:
            print(f"  ✗ 直接搜索: 未找到")
        
        # 正则搜索
        pattern = re.escape(test_pinyin)
        matches = re.findall(pattern, content)
        if matches:
            print(f"  ✓ 正则搜索: 找到 {len(matches)} 次")
        else:
            print(f"  ✗ 正则搜索: 未找到")
        
        # 在提取的拼音中搜索
        if test_pinyin in complete_analysis['pinyin_set']:
            print(f"  ✓ 提取集合: 存在")
        else:
            print(f"  ✗ 提取集合: 不存在")
        
        search_results[test_pinyin] = {
            'direct_search': test_pinyin in content,
            'regex_search': len(matches) > 0,
            'in_extracted_set': test_pinyin in complete_analysis['pinyin_set']
        }
    
    return search_results

def generate_diagnostic_report(complete_analysis: Dict, comparison: Dict, 
                             search_test: Dict, report_file: str):
    """
    生成诊断报告
    """
    print(f"\n=== 生成诊断报告: {report_file} ===")
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("拼音匹配诊断报告\n")
            f.write("=" * 60 + "\n\n")
            
            # 文件信息
            if complete_analysis:
                file_info = complete_analysis['file_info']
                f.write("完整拼音文件信息:\n")
                f.write("-" * 30 + "\n")
                f.write(f"编码: {file_info['encoding']}\n")
                f.write(f"字符数: {file_info['char_count']}\n")
                f.write(f"行数: {file_info['line_count']}\n")
                f.write(f"字节数: {file_info['byte_count']}\n\n")
            
            # 匹配分析
            if comparison:
                f.write("匹配分析结果:\n")
                f.write("-" * 30 + "\n")
                f.write(f"匹配成功: {len(comparison['matched'])} 个\n")
                f.write(f"匹配失败: {len(comparison['not_matched'])} 个\n\n")
                
                if comparison['mismatch_analysis']:
                    f.write("匹配失败详细分析:\n")
                    f.write("-" * 30 + "\n")
                    for analysis in comparison['mismatch_analysis']:
                        f.write(f"\n拼音: '{analysis['pinyin']}'\n")
                        f.write(f"Unicode: {analysis['details']['unicode_points']}\n")
                        f.write(f"字节: {analysis['details']['bytes_utf8']}\n")
                        if analysis['details']['invisible_chars']:
                            f.write(f"不可见字符: {analysis['details']['invisible_chars']}\n")
                        if analysis['similar_candidates']:
                            f.write(f"相似候选: {analysis['similar_candidates']}\n")
            
            # 搜索测试
            if search_test:
                f.write("\n手动搜索测试结果:\n")
                f.write("-" * 30 + "\n")
                for pinyin, results in search_test.items():
                    f.write(f"\n'{pinyin}':\n")
                    f.write(f"  直接搜索: {'✓' if results['direct_search'] else '✗'}\n")
                    f.write(f"  正则搜索: {'✓' if results['regex_search'] else '✗'}\n")
                    f.write(f"  提取集合: {'✓' if results['in_extracted_set'] else '✗'}\n")
            
            # 解决建议
            f.write("\n解决建议:\n")
            f.write("-" * 30 + "\n")
            f.write("1. 检查文件编码，确保使用UTF-8\n")
            f.write("2. 清理不可见字符和特殊空格\n")
            f.write("3. 统一拼音格式（大小写、声调标记）\n")
            f.write("4. 使用Unicode标准化处理\n")
            f.write("5. 检查正则表达式模式是否正确\n")
        
        print(f"✓ 诊断报告已生成: {report_file}")
        
    except Exception as e:
        print(f"✗ 生成报告失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("拼音匹配诊断工具")
    print("诊断拼音匹配失败的原因")
    print("=" * 60)
    
    config = CONFIG
    
    # 1. 分析完整拼音文件
    complete_analysis = load_and_analyze_complete_pinyin(config['COMPLETE_PINYIN_FILE'])
    
    # 2. 对比匹配情况
    comparison = compare_pinyin_matching(complete_analysis, config['SAMPLE_TTS_FILE'])
    
    # 3. 手动搜索测试
    search_test = manual_search_test(complete_analysis)
    
    # 4. 生成诊断报告
    generate_diagnostic_report(complete_analysis, comparison, search_test, config['DIAGNOSTIC_REPORT'])
    
    print(f"\n🎉 诊断完成！")
    print(f"详细报告: {config['DIAGNOSTIC_REPORT']}")
    print("=" * 60)

if __name__ == "__main__":
    main()
