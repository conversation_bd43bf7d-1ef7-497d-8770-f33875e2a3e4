#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包API诊断工具
检查API配置、权限和请求格式
"""

import asyncio
import json
import logging
import uuid
import websockets
from protocols import MsgType, full_client_request, receive_message

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# API配置
API_CONFIG = {
    "APPID": "9862368305",
    "ACCESS_TOKEN": "bbxNaAzRzBSkySjouDItXl6tBUUyNd2W",
    "ENDPOINT": "wss://openspeech.bytedance.com/api/v1/tts/ws_binary",
}

def get_cluster(voice: str) -> str:
    """获取集群名称"""
    if voice.startswith("S_"):
        return "volcano_icl"
    return "volcano_tts"

async def diagnose_api_connection():
    """诊断API连接"""
    print("=" * 60)
    print("豆包API连接诊断")
    print("=" * 60)
    
    print(f"APPID: {API_CONFIG['APPID']}")
    print(f"ACCESS_TOKEN: {API_CONFIG['ACCESS_TOKEN'][:20]}...")
    print(f"ENDPOINT: {API_CONFIG['ENDPOINT']}")
    
    # 测试连接
    headers = {
        "Authorization": f"Bearer;{API_CONFIG['ACCESS_TOKEN']}",
    }
    
    try:
        print(f"\n🔗 尝试连接到WebSocket服务器...")
        websocket = await websockets.connect(
            API_CONFIG["ENDPOINT"], 
            additional_headers=headers, 
            max_size=10 * 1024 * 1024
        )
        
        print(f"✅ WebSocket连接成功!")
        
        # 获取响应头信息
        try:
            response_headers = dict(websocket.response.headers)
            print(f"📋 响应头信息:")
            for key, value in response_headers.items():
                print(f"  {key}: {value}")
        except Exception as e:
            print(f"📋 无法获取响应头: {e}")
            print(f"📋 Logid: {websocket.response.headers.get('x-tt-logid', 'N/A')}")
        
        await websocket.close()
        return True
        
    except Exception as e:
        print(f"❌ WebSocket连接失败: {e}")
        return False

async def test_simple_request():
    """测试简单的TTS请求"""
    print(f"\n=" * 60)
    print("简单TTS请求测试")
    print("=" * 60)
    
    # 使用最基本的配置
    voice_type = "BV001_streaming"  # 使用基础音色
    text = "你好"
    cluster = get_cluster(voice_type)
    
    print(f"测试文本: {text}")
    print(f"音色类型: {voice_type}")
    print(f"集群: {cluster}")
    
    headers = {
        "Authorization": f"Bearer;{API_CONFIG['ACCESS_TOKEN']}",
    }
    
    try:
        websocket = await websockets.connect(
            API_CONFIG["ENDPOINT"], 
            additional_headers=headers, 
            max_size=10 * 1024 * 1024
        )
        
        # 构建最简单的请求
        request = {
            "app": {
                "appid": API_CONFIG["APPID"],
                "token": API_CONFIG["ACCESS_TOKEN"],
                "cluster": cluster,
            },
            "user": {
                "uid": str(uuid.uuid4()),
            },
            "audio": {
                "voice_type": voice_type,
                "encoding": "wav",
            },
            "request": {
                "reqid": str(uuid.uuid4()),
                "text": text,
                "operation": "submit",
            },
        }
        
        print(f"\n📤 发送请求:")
        print(json.dumps(request, indent=2, ensure_ascii=False))
        
        # 发送请求
        await full_client_request(websocket, json.dumps(request).encode('utf-8'))
        
        print(f"\n📥 等待响应...")
        
        # 接收响应
        message_count = 0
        while message_count < 10:  # 最多接收10条消息
            try:
                msg = await asyncio.wait_for(receive_message(websocket), timeout=10.0)
                message_count += 1
                
                print(f"📨 收到消息 {message_count}:")
                print(f"  类型: {msg.type} ({MsgType(msg.type).name if msg.type in MsgType else '未知'})")
                print(f"  序号: {msg.sequence}")
                print(f"  负载大小: {msg.payload_size}")
                
                if msg.type == MsgType.FrontEndResultServer:
                    print(f"  内容: 前端结果")
                    continue
                elif msg.type == MsgType.AudioOnlyServer:
                    print(f"  内容: 音频数据 ({len(msg.payload)} 字节)")
                    if msg.sequence < 0:
                        print(f"  ✅ 收到最后一条音频消息")
                        break
                elif msg.type == MsgType.ErrorServer:
                    print(f"  内容: 错误消息")
                    try:
                        error_data = json.loads(msg.payload.decode('utf-8'))
                        print(f"  错误详情: {json.dumps(error_data, indent=4, ensure_ascii=False)}")
                    except:
                        print(f"  错误内容: {msg.payload.decode('utf-8', errors='ignore')}")
                    break
                else:
                    print(f"  内容: 未知类型")
                    print(f"  原始数据: {msg.payload[:100]}...")
                    
            except asyncio.TimeoutError:
                print(f"⏰ 接收消息超时")
                break
            except Exception as e:
                print(f"❌ 接收消息失败: {e}")
                break
        
        await websocket.close()
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")

async def test_different_voices():
    """测试不同的音色"""
    print(f"\n=" * 60)
    print("不同音色测试")
    print("=" * 60)
    
    # 测试不同的音色
    test_voices = [
        "BV001_streaming",  # 基础音色
        "BV002_streaming",  # 基础音色
        "zh_female_wanqudashu_moon_bigtts",  # 您使用的音色
    ]
    
    for voice in test_voices:
        print(f"\n🎵 测试音色: {voice}")
        cluster = get_cluster(voice)
        print(f"集群: {cluster}")
        
        headers = {
            "Authorization": f"Bearer;{API_CONFIG['ACCESS_TOKEN']}",
        }
        
        try:
            websocket = await websockets.connect(
                API_CONFIG["ENDPOINT"], 
                additional_headers=headers, 
                max_size=10 * 1024 * 1024
            )
            
            request = {
                "app": {
                    "appid": API_CONFIG["APPID"],
                    "token": API_CONFIG["ACCESS_TOKEN"],
                    "cluster": cluster,
                },
                "user": {
                    "uid": str(uuid.uuid4()),
                },
                "audio": {
                    "voice_type": voice,
                    "encoding": "wav",
                },
                "request": {
                    "reqid": str(uuid.uuid4()),
                    "text": "测试",
                    "operation": "submit",
                },
            }
            
            await full_client_request(websocket, json.dumps(request).encode('utf-8'))
            
            # 只接收第一条消息来检查是否有错误
            try:
                msg = await asyncio.wait_for(receive_message(websocket), timeout=5.0)
                
                if msg.type == MsgType.ErrorServer:
                    try:
                        error_data = json.loads(msg.payload.decode('utf-8'))
                        print(f"  ❌ 错误: {error_data}")
                    except:
                        print(f"  ❌ 错误: {msg.payload.decode('utf-8', errors='ignore')}")
                else:
                    print(f"  ✅ 成功: 收到类型 {msg.type} 的消息")
                    
            except asyncio.TimeoutError:
                print(f"  ⏰ 超时")
            
            await websocket.close()
            
        except Exception as e:
            print(f"  ❌ 连接失败: {e}")
        
        await asyncio.sleep(1)  # 间隔1秒

async def main():
    """主函数"""
    print("豆包API诊断工具")
    print("这个工具将帮助诊断API配置和连接问题")
    
    # 1. 测试基本连接
    connection_ok = await diagnose_api_connection()
    
    if not connection_ok:
        print("\n❌ 基本连接失败，请检查:")
        print("1. 网络连接是否正常")
        print("2. API密钥是否正确")
        print("3. 是否有防火墙阻止")
        return
    
    # 2. 测试简单请求
    await test_simple_request()
    
    # 3. 测试不同音色
    await test_different_voices()
    
    print(f"\n=" * 60)
    print("诊断完成")
    print("=" * 60)
    
    print("📋 建议检查项目:")
    print("1. 确认API密钥是否有TTS服务权限")
    print("2. 确认APPID是否正确")
    print("3. 确认音色类型是否支持")
    print("4. 联系豆包技术支持获取帮助")

if __name__ == "__main__":
    asyncio.run(main())
