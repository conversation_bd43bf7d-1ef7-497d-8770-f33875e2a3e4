import os
import zipfile

# 配置路径
input_folder = "audio_output"  # 输入文件夹路径
output_zip = "audio_c_10000.zip"  # 输出ZIP文件名

# 确保输入文件夹存在
if not os.path.exists(input_folder):
    print(f"错误：文件夹 '{input_folder}' 不存在")
    exit(1)

# 创建ZIP文件并添加文件
added_count = 0
with zipfile.ZipFile(output_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
    # 遍历1到10000的文件序号
    for i in range(1, 10001):
        # 生成7位数字格式的文件名 (Sc0000001.wav ~ Sc0010000.wav)
        filename = f"Sc{i:07d}.wav"
        filepath = os.path.join(input_folder, filename)
        
        # 仅添加存在的文件
        if os.path.isfile(filepath):
            zipf.write(filepath, arcname=filename)
            added_count += 1
            
            # 每1000个文件显示一次进度
            if added_count % 1000 == 0:
                print(f"已添加 {added_count} 个文件...")

print(f"\n操作完成！成功添加 {added_count} 个文件到 {output_zip}")
print(f"ZIP文件大小: {os.path.getsize(output_zip)/(1024*1024):.2f} MB")