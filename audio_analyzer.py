#!/usr/bin/env python3
"""
音频文件分析工具
用于分析音频文件的基本属性和特征
"""

import os
import wave
import numpy as np
from pathlib import Path
import pandas as pd
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

def analyze_audio_file_basic(file_path: str) -> Dict:
    """
    使用基础wave库分析音频文件
    """
    try:
        with wave.open(file_path, 'rb') as wav_file:
            frames = wav_file.getnframes()
            sample_rate = wav_file.getframerate()
            channels = wav_file.getnchannels()
            sample_width = wav_file.getsampwidth()
            duration = frames / sample_rate

            # 读取音频数据
            audio_data = wav_file.readframes(frames)

            # 转换为numpy数组
            if sample_width == 1:
                dtype = np.uint8
            elif sample_width == 2:
                dtype = np.int16
            elif sample_width == 4:
                dtype = np.int32
            else:
                dtype = np.float32

            audio_array = np.frombuffer(audio_data, dtype=dtype)

            # 计算基本统计信息
            max_amplitude = np.max(np.abs(audio_array)) if len(audio_array) > 0 else 0
            rms = np.sqrt(np.mean(audio_array.astype(np.float64)**2)) if len(audio_array) > 0 else 0

            # 静音检测（相对于最大可能值）
            max_possible = 2**(sample_width * 8 - 1) - 1
            normalized_max = max_amplitude / max_possible if max_possible > 0 else 0
            is_silent = normalized_max < 0.01

            result = {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'file_size_mb': os.path.getsize(file_path) / (1024 * 1024),
                'duration_seconds': duration,
                'sample_rate': sample_rate,
                'channels': channels,
                'sample_width': sample_width,
                'frames': frames,
                'max_amplitude': max_amplitude,
                'normalized_max_amplitude': normalized_max,
                'rms_energy': rms,
                'is_silent': is_silent,
                'status': 'success'
            }

            return result

    except Exception as e:
        return {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'error': str(e),
            'status': 'error'
        }

def analyze_sample_files(directory: str, sample_count: int = 10) -> pd.DataFrame:
    """
    分析目录中的样本音频文件

    Args:
        directory: 音频文件目录
        sample_count: 要分析的样本数量

    Returns:
        包含分析结果的DataFrame
    """
    # 获取所有wav文件
    wav_files = list(Path(directory).glob("*.wav"))

    if not wav_files:
        print(f"在目录 {directory} 中未找到wav文件")
        return pd.DataFrame()

    # 选择样本文件
    sample_files = wav_files[:sample_count]

    print(f"分析 {len(sample_files)} 个样本文件...")

    results = []
    for i, file_path in enumerate(sample_files, 1):
        print(f"分析文件 {i}/{len(sample_files)}: {file_path.name}")
        result = analyze_audio_file_basic(str(file_path))
        results.append(result)

    return pd.DataFrame(results)

def print_analysis_summary(df: pd.DataFrame):
    """
    打印分析结果摘要
    """
    if df.empty:
        print("没有分析结果")
        return

    print("\n" + "="*60)
    print("音频文件分析摘要")
    print("="*60)

    # 成功分析的文件
    success_df = df[df['status'] == 'success']
    error_df = df[df['status'] == 'error']

    print(f"总文件数: {len(df)}")
    print(f"成功分析: {len(success_df)}")
    print(f"分析失败: {len(error_df)}")

    if len(success_df) > 0:
        print(f"\n基本统计信息:")
        print(f"平均时长: {success_df['duration_seconds'].mean():.2f} 秒")
        print(f"时长范围: {success_df['duration_seconds'].min():.2f} - {success_df['duration_seconds'].max():.2f} 秒")
        print(f"采样率: {success_df['sample_rate'].unique()}")
        print(f"声道数: {success_df['channels'].unique()}")
        print(f"样本宽度: {success_df['sample_width'].unique()}")
        print(f"平均文件大小: {success_df['file_size_mb'].mean():.3f} MB")

        # 静音检测
        silent_count = success_df['is_silent'].sum()
        print(f"\n静音文件检测:")
        print(f"疑似静音文件: {silent_count} 个")
        if silent_count > 0:
            silent_files = success_df[success_df['is_silent']]['file_name'].tolist()
            print(f"静音文件列表: {silent_files}")

        # 音频能量统计
        print(f"\n音频能量统计:")
        print(f"平均归一化最大振幅: {success_df['normalized_max_amplitude'].mean():.4f}")
        print(f"归一化最大振幅范围: {success_df['normalized_max_amplitude'].min():.4f} - {success_df['normalized_max_amplitude'].max():.4f}")

    if len(error_df) > 0:
        print(f"\n错误文件:")
        for _, row in error_df.iterrows():
            print(f"  {row['file_name']}: {row['error']}")

if __name__ == "__main__":
    # 分析当前目录的样本文件
    current_dir = "."

    print("开始分析音频文件...")
    df = analyze_sample_files(current_dir, sample_count=20)

    if not df.empty:
        # 保存详细结果
        df.to_csv("audio_analysis_sample.csv", index=False, encoding='utf-8-sig')
        print(f"\n详细分析结果已保存到: audio_analysis_sample.csv")

        # 打印摘要
        print_analysis_summary(df)
    else:
        print("未找到可分析的音频文件")
