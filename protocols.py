#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包语音WebSocket协议支持模块
简化版实现，用于支持语音合成大模型API
"""

import struct
import json
from enum import IntEnum

class MsgType(IntEnum):
    """消息类型枚举"""
    FrontEndResultServer = 0x1
    AudioOnlyServer = 0x2
    ErrorServer = 0x3

class Message:
    """WebSocket消息类"""
    
    def __init__(self, msg_type, sequence, payload):
        self.type = msg_type
        self.sequence = sequence
        self.payload = payload
    
    def __str__(self):
        return f"Message(type={self.type}, sequence={self.sequence}, payload_size={len(self.payload)})"

async def full_client_request(websocket, data):
    """发送完整的客户端请求"""
    # 构建消息头
    # 格式: [4字节消息类型][4字节序列号][4字节负载长度][负载数据]
    msg_type = 0x1  # 客户端请求类型
    sequence = 0
    payload_length = len(data)
    
    # 打包消息头
    header = struct.pack('<III', msg_type, sequence, payload_length)
    
    # 发送消息头和负载
    await websocket.send(header + data)

async def receive_message(websocket):
    """接收WebSocket消息"""
    try:
        # 接收原始数据
        raw_data = await websocket.recv()
        
        # 如果是二进制数据，尝试解析协议
        if isinstance(raw_data, bytes):
            if len(raw_data) < 12:  # 至少需要12字节的消息头
                # 可能是简单的音频数据
                return Message(MsgType.AudioOnlyServer, -1, raw_data)
            
            try:
                # 尝试解析消息头
                msg_type, sequence, payload_length = struct.unpack('<III', raw_data[:12])
                payload = raw_data[12:12+payload_length] if payload_length > 0 else b''
                
                return Message(msg_type, sequence, payload)
            except struct.error:
                # 解析失败，当作音频数据处理
                return Message(MsgType.AudioOnlyServer, -1, raw_data)
        
        # 如果是文本数据，尝试解析JSON
        elif isinstance(raw_data, str):
            try:
                json_data = json.loads(raw_data)
                # 根据JSON内容判断消息类型
                if 'audio' in json_data:
                    return Message(MsgType.AudioOnlyServer, -1, json_data.get('audio', b''))
                else:
                    return Message(MsgType.FrontEndResultServer, 0, raw_data.encode())
            except json.JSONDecodeError:
                return Message(MsgType.FrontEndResultServer, 0, raw_data.encode())
        
        # 默认情况
        return Message(MsgType.AudioOnlyServer, -1, raw_data if isinstance(raw_data, bytes) else raw_data.encode())
        
    except Exception as e:
        # 发生错误时返回错误消息
        error_msg = f"接收消息失败: {e}"
        return Message(MsgType.ErrorServer, -1, error_msg.encode())

def parse_audio_response(data):
    """解析音频响应数据"""
    try:
        if isinstance(data, bytes):
            return data
        elif isinstance(data, str):
            # 尝试解析JSON格式的响应
            json_data = json.loads(data)
            if 'data' in json_data:
                import base64
                return base64.b64decode(json_data['data'])
            return data.encode()
        return data
    except Exception:
        return data if isinstance(data, bytes) else data.encode()

class WebSocketProtocol:
    """WebSocket协议处理类"""
    
    def __init__(self):
        self.sequence = 0
    
    def create_request(self, request_data):
        """创建请求消息"""
        if isinstance(request_data, str):
            request_data = request_data.encode()
        
        self.sequence += 1
        return {
            'type': 0x1,
            'sequence': self.sequence,
            'data': request_data
        }
    
    async def send_request(self, websocket, request_data):
        """发送请求"""
        await full_client_request(websocket, request_data)
    
    async def receive_response(self, websocket):
        """接收响应"""
        return await receive_message(websocket)

# 兼容性函数
def get_message_type(msg_type_int):
    """获取消息类型"""
    try:
        return MsgType(msg_type_int)
    except ValueError:
        return MsgType.ErrorServer

def is_audio_message(message):
    """判断是否为音频消息"""
    return message.type == MsgType.AudioOnlyServer

def is_final_message(message):
    """判断是否为最终消息"""
    return message.sequence < 0

def extract_audio_data(message):
    """提取音频数据"""
    if is_audio_message(message):
        return parse_audio_response(message.payload)
    return b''
