"""
简化TTS音频生成程序
每种音色生成24000字音频（约1小时）
支持Edge TTS（推荐）和豆包TTS
"""

import asyncio
import json
import logging
import uuid
import os
import time
import pandas as pd
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 允许在Jupyter中运行异步代码
try:
    import nest_asyncio
    nest_asyncio.apply()
except ImportError:
    pass

# ==================== 配置区域 ====================
CONFIG = {
    # 文件配置
    "TEXT_FILE": "shuffled_from_rank_random.txt",  # 文本文件路径
    "OUTPUT_DIR": "audio_output_24k",              # 输出目录
    "CSV_FILE": "tts_24k_log.csv",                 # CSV记录文件
    
    # 生成配置
    "TARGET_CHARS_PER_VOICE": 24000,  # 每种音色目标字数
    "REQUEST_INTERVAL": 1.0,           # 请求间隔（秒）
    
    # TTS引擎选择
    "USE_ENGINE": "edge",  # "edge" 或 "douyin"
    
    # 豆包TTS配置（如果使用）
    "DOUYIN_APPID": "9862368305",
    "DOUYIN_ACCESS_TOKEN": "bbxNaAzRzBSkySjouDItXl6tBUUyNd2W",
}

# 音色配置 - 简化版
VOICE_CONFIGS = [
    {"code": "D001", "name": "音色1", "edge_voice": "zh-CN-XiaoxiaoNeural", "douyin_voice": "BV001_streaming"},
    {"code": "D002", "name": "音色2", "edge_voice": "zh-CN-YunxiNeural", "douyin_voice": "BV002_streaming"},
    {"code": "D003", "name": "音色3", "edge_voice": "zh-CN-XiaoyiNeural", "douyin_voice": "BV007_streaming"},
    {"code": "D004", "name": "音色4", "edge_voice": "zh-CN-YunyangNeural", "douyin_voice": "BV056_streaming"},
    {"code": "D005", "name": "音色5", "edge_voice": "zh-CN-XiaohanNeural", "douyin_voice": "BV005_streaming"},
]

class SimpleTTSGenerator:
    """简化的TTS生成器"""
    
    def __init__(self, config):
        self.config = config
        self.output_dir = Path(config["OUTPUT_DIR"])
        self.output_dir.mkdir(exist_ok=True)
        
        self.success_count = 0
        self.error_count = 0
    
    def load_texts(self):
        """加载文本文件"""
        try:
            with open(self.config["TEXT_FILE"], 'r', encoding='utf-8') as f:
                texts = [line.strip() for line in f if line.strip()]
            print(f"✅ 加载文本: {len(texts)} 条")
            return texts
        except Exception as e:
            print(f"❌ 加载文本失败: {e}")
            return []
    
    def select_texts_for_target_chars(self, texts, target_chars):
        """选择文本达到目标字数"""
        selected_texts = []
        current_chars = 0
        
        for text in texts:
            text_length = len(text)
            if current_chars + text_length <= target_chars:
                selected_texts.append(text)
                current_chars += text_length
            else:
                # 如果接近目标字数，也添加
                if target_chars - current_chars > text_length // 2:
                    selected_texts.append(text)
                    current_chars += text_length
                break
        
        return selected_texts, current_chars
    
    async def synthesize_with_edge_tts(self, text, voice):
        """使用Edge TTS合成"""
        try:
            import edge_tts
            
            communicate = edge_tts.Communicate(text, voice)
            audio_data = b""
            
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_data += chunk["data"]
            
            return audio_data if audio_data else None
            
        except ImportError:
            print("❌ Edge TTS未安装，请运行: pip install edge-tts")
            return None
        except Exception as e:
            logger.error(f"Edge TTS合成失败: {e}")
            return None
    
    async def synthesize_with_douyin_tts(self, text, voice):
        """使用豆包TTS合成"""
        try:
            import websockets
            from protocols import MsgType, full_client_request, receive_message
            
            headers = {"Authorization": f"Bearer;{self.config['DOUYIN_ACCESS_TOKEN']}"}
            endpoint = "wss://openspeech.bytedance.com/api/v1/tts/ws_binary"
            
            websocket = await websockets.connect(endpoint, additional_headers=headers, max_size=10 * 1024 * 1024)
            
            cluster = "volcano_icl" if voice.startswith("S_") else "volcano_tts"
            
            request = {
                "app": {
                    "appid": self.config["DOUYIN_APPID"],
                    "token": self.config["DOUYIN_ACCESS_TOKEN"],
                    "cluster": cluster,
                },
                "user": {"uid": str(uuid.uuid4())},
                "audio": {"voice_type": voice, "encoding": "wav"},
                "request": {
                    "reqid": str(uuid.uuid4()),
                    "text": text,
                    "operation": "submit",
                },
            }
            
            await full_client_request(websocket, json.dumps(request).encode())
            
            audio_data = bytearray()
            while True:
                msg = await receive_message(websocket)
                
                if msg.type == MsgType.FrontEndResultServer:
                    continue
                elif msg.type == MsgType.AudioOnlyServer:
                    audio_data.extend(msg.payload)
                    if msg.sequence < 0:
                        break
                elif msg.type == MsgType.ErrorServer:
                    error_msg = msg.payload.decode('utf-8', errors='ignore')
                    raise RuntimeError(f"服务器错误: {error_msg}")
                else:
                    raise RuntimeError(f"未知消息类型: {msg.type}")
            
            await websocket.close()
            return bytes(audio_data) if audio_data else None
            
        except Exception as e:
            logger.error(f"豆包TTS合成失败: {e}")
            return None
    
    async def synthesize_text(self, text, voice_config):
        """合成文本（自动选择引擎）"""
        engine = self.config["USE_ENGINE"]
        
        if engine == "edge":
            return await self.synthesize_with_edge_tts(text, voice_config["edge_voice"])
        elif engine == "douyin":
            return await self.synthesize_with_douyin_tts(text, voice_config["douyin_voice"])
        else:
            print(f"❌ 未知引擎: {engine}")
            return None
    
    async def generate_voice_audio(self, voice_config, texts):
        """为单个音色生成音频"""
        voice_code = voice_config["code"]
        voice_name = voice_config["name"]
        engine = self.config["USE_ENGINE"]
        
        print(f"\n=== 生成音色: {voice_name} ({voice_code}) ===")
        print(f"使用引擎: {engine.upper()}")
        print(f"文本数量: {len(texts)}")
        
        # 确认生成
        try:
            confirm = input(f"是否生成 {voice_name}？(y/n): ").strip().lower()
            if confirm not in ['y', 'yes', '是']:
                print(f"跳过 {voice_name}")
                return 0, 0
        except:
            # Jupyter环境中可能无法输入，默认生成
            print(f"自动生成 {voice_name}")
        
        success_count = 0
        error_count = 0
        records = []
        start_time = time.time()
        
        for i, text in enumerate(texts, 1):
            # 文件名格式：Sc + 音色代码 + 序号
            filename = f"Sc{voice_code}{i:07d}.wav"
            filepath = self.output_dir / filename
            
            print(f"[{i}/{len(texts)}] {text[:30]}{'...' if len(text) > 30 else ''}")
            
            # 检查文件是否已存在
            if filepath.exists():
                print(f"  跳过已存在文件")
                success_count += 1
                continue
            
            # 合成音频
            audio_data = await self.synthesize_text(text, voice_config)
            
            if audio_data:
                try:
                    # 保存音频文件
                    with open(filepath, "wb") as f:
                        f.write(audio_data)
                    
                    print(f"  ✅ 成功: {len(audio_data)} 字节")
                    success_count += 1
                    self.success_count += 1
                    
                    # 记录到CSV
                    records.append({
                        "音频名": filename.replace('.wav', ''),
                        "类型": "c",
                        "文本": text,
                        "注音": "",
                        "音色": voice_name,
                        "引擎": engine
                    })
                    
                except Exception as e:
                    print(f"  ❌ 保存失败: {e}")
                    error_count += 1
                    self.error_count += 1
            else:
                print(f"  ❌ 合成失败")
                error_count += 1
                self.error_count += 1
            
            # 请求间隔
            await asyncio.sleep(self.config["REQUEST_INTERVAL"])
            
            # 每10个显示进度
            if i % 10 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(texts) - i) * avg_time
                print(f"  进度: {i}/{len(texts)}, 预计剩余: {remaining/60:.1f}分钟")
        
        # 保存CSV记录
        if records:
            csv_path = self.output_dir / f"tts_log_{voice_code}.csv"
            df = pd.DataFrame(records)
            df.to_csv(csv_path, index=False, encoding='utf-8-sig', sep='\t')
            print(f"✅ CSV保存: {csv_path}")
        
        elapsed_time = time.time() - start_time
        print(f"\n{voice_name} 完成:")
        print(f"  耗时: {elapsed_time/60:.1f}分钟")
        print(f"  成功: {success_count}")
        print(f"  失败: {error_count}")
        print(f"  成功率: {(success_count/(success_count+error_count)*100):.1f}%" if success_count+error_count > 0 else "0%")
        
        return success_count, error_count

async def main():
    """主函数"""
    print("=" * 60)
    print("简化TTS音频生成程序")
    print("=" * 60)
    
    config = CONFIG
    
    print("配置信息:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  输出目录: {config['OUTPUT_DIR']}")
    print(f"  目标字数: {config['TARGET_CHARS_PER_VOICE']} 字/音色")
    print(f"  使用引擎: {config['USE_ENGINE'].upper()}")
    print(f"  请求间隔: {config['REQUEST_INTERVAL']} 秒")
    
    print(f"\n音色配置:")
    for i, voice in enumerate(VOICE_CONFIGS, 1):
        print(f"  {i}. {voice['name']} ({voice['code']})")
    
    print("=" * 60)
    
    # 创建生成器
    generator = SimpleTTSGenerator(config)
    
    # 1. 加载文本
    print("\n=== 加载文本 ===")
    all_texts = generator.load_texts()
    if not all_texts:
        print("❌ 没有可用文本")
        return
    
    total_chars = sum(len(text) for text in all_texts)
    print(f"文本总字数: {total_chars} 字")
    
    # 2. 检查文件是否存在
    if not os.path.exists(config['TEXT_FILE']):
        print(f"❌ 文本文件不存在: {config['TEXT_FILE']}")
        return
    
    # 3. 开始生成
    print(f"\n=== 开始生成 ===")
    
    total_success = 0
    total_error = 0
    
    for i, voice_config in enumerate(VOICE_CONFIGS, 1):
        print(f"\n进度: {i}/{len(VOICE_CONFIGS)}")
        
        # 为每个音色选择文本
        selected_texts, actual_chars = generator.select_texts_for_target_chars(
            all_texts, config["TARGET_CHARS_PER_VOICE"]
        )
        
        print(f"选择文本: {len(selected_texts)} 条, {actual_chars} 字")
        
        # 生成音频
        success, error = await generator.generate_voice_audio(voice_config, selected_texts)
        total_success += success
        total_error += error
        
        # 询问是否继续
        if i < len(VOICE_CONFIGS):
            try:
                continue_choice = input("继续下一个音色？(y/n): ").strip().lower()
                if continue_choice not in ['y', 'yes', '是']:
                    break
            except:
                # Jupyter环境中自动继续
                print("自动继续下一个音色")
    
    # 4. 最终统计
    print(f"\n=== 生成完成 ===")
    print(f"总成功: {total_success}")
    print(f"总失败: {total_error}")
    print(f"成功率: {(total_success/(total_success+total_error)*100):.1f}%" if total_success+total_error > 0 else "0%")
    print(f"输出目录: {config['OUTPUT_DIR']}")

# 在Jupyter中运行
if __name__ == "__main__":
    # 检查是否在Jupyter环境
    try:
        from IPython import get_ipython
        if get_ipython() is not None:
            # Jupyter环境
            await main()
        else:
            # 普通Python环境
            asyncio.run(main())
    except:
        # 直接运行
        asyncio.run(main())
