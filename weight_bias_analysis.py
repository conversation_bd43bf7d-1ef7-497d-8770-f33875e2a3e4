#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
权重偏差对拼音覆盖率影响分析工具
分析基于词频权重的文本生成是否导致拼音覆盖率偏低
"""

import os
import re
import json
import random
from collections import Counter, defaultdict
from typing import Set, List, Dict, Tuple

# 配置信息
CONFIG = {
    # 文件路径
    "DICTIONARY_FILE": "现代汉语词典.txt",  # 词典文件
    "COMPLETE_PINYIN_FILE": "pinyin_complete.txt",  # 完整拼音集合
    "GENERATED_TEXT_FILE": "shuffled_text_aliyun.txt",  # 生成的文本文件
    "GENERATED_PINYIN_FILE": "shuffled_text_aliyun_pinyin.txt",  # 生成的拼音文件
    "REPORT_FILE": "weight_bias_analysis_report.txt",  # 分析报告
}

def extract_pinyin_from_text(text: str) -> Set[str]:
    """从文本中提取拼音"""
    patterns = [
        r'[a-zA-Z]+\d+',
        r'[a-zA-Z]+[1-5]',
        r'[a-zA-Z]+',
    ]
    
    all_pinyins = set()
    for pattern in patterns:
        pinyins = re.findall(pattern, text)
        all_pinyins.update(pinyin.lower() for pinyin in pinyins)
    
    return {p for p in all_pinyins if len(p) >= 2}

def analyze_word_frequency_bias(dict_file: str) -> Dict:
    """分析词典中的词频分布和拼音分布"""
    print(f"=== 分析词典词频偏差: {dict_file} ===")
    
    if not os.path.exists(dict_file):
        print(f"✗ 词典文件不存在: {dict_file}")
        return {}
    
    try:
        with open(dict_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        word_freq = Counter()
        word_pinyins = {}
        pinyin_word_count = defaultdict(int)
        
        # 解析词典，假设格式为：词语 拼音 其他信息
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            parts = line.split()
            if len(parts) >= 2:
                word = parts[0]
                pinyin_part = parts[1]
                
                # 提取拼音
                word_pinyins_set = extract_pinyin_from_text(pinyin_part)
                
                if word_pinyins_set:
                    # 假设词频与词的常用程度相关（这里简化处理）
                    # 实际应用中可能需要真实的词频数据
                    word_length = len(word)
                    estimated_freq = max(1, 10 - word_length)  # 简化的频率估算
                    
                    word_freq[word] = estimated_freq
                    word_pinyins[word] = word_pinyins_set
                    
                    # 统计每个拼音对应的词数
                    for pinyin in word_pinyins_set:
                        pinyin_word_count[pinyin] += 1
        
        print(f"✓ 解析词典: {len(word_freq)} 个词条")
        print(f"✓ 拼音种类: {len(pinyin_word_count)} 个")
        
        # 分析高频词的拼音分布
        high_freq_words = word_freq.most_common(1000)  # 前1000高频词
        high_freq_pinyins = set()
        for word, freq in high_freq_words:
            if word in word_pinyins:
                high_freq_pinyins.update(word_pinyins[word])
        
        # 分析低频拼音
        pinyin_freq_dist = Counter(pinyin_word_count)
        low_freq_pinyins = {p for p, count in pinyin_freq_dist.items() if count <= 5}
        
        return {
            'word_freq': word_freq,
            'word_pinyins': word_pinyins,
            'pinyin_word_count': dict(pinyin_word_count),
            'high_freq_pinyins': high_freq_pinyins,
            'low_freq_pinyins': low_freq_pinyins,
            'total_pinyins': set(pinyin_word_count.keys())
        }
        
    except Exception as e:
        print(f"✗ 分析词典失败: {e}")
        return {}

def simulate_weighted_text_generation(dict_analysis: Dict, target_chars: int = 50000) -> Dict:
    """模拟基于权重的文本生成"""
    print(f"\n=== 模拟权重文本生成 ===")
    
    if not dict_analysis:
        return {}
    
    word_freq = dict_analysis['word_freq']
    word_pinyins = dict_analysis['word_pinyins']
    
    # 创建权重列表
    words = list(word_freq.keys())
    weights = [word_freq[word] for word in words]
    
    # 模拟生成文本
    generated_words = []
    current_chars = 0
    
    while current_chars < target_chars:
        # 按权重随机选择词语
        selected_word = random.choices(words, weights=weights, k=1)[0]
        generated_words.append(selected_word)
        current_chars += len(selected_word)
    
    # 统计生成的拼音
    generated_pinyins = set()
    word_usage_count = Counter(generated_words)
    
    for word in generated_words:
        if word in word_pinyins:
            generated_pinyins.update(word_pinyins[word])
    
    print(f"✓ 生成文本: {len(generated_words)} 个词，{current_chars} 个字符")
    print(f"✓ 涉及拼音: {len(generated_pinyins)} 个")
    
    return {
        'generated_words': generated_words,
        'generated_pinyins': generated_pinyins,
        'word_usage_count': word_usage_count,
        'total_chars': current_chars
    }

def simulate_uniform_text_generation(dict_analysis: Dict, target_chars: int = 50000) -> Dict:
    """模拟均匀分布的文本生成"""
    print(f"\n=== 模拟均匀文本生成 ===")
    
    if not dict_analysis:
        return {}
    
    word_pinyins = dict_analysis['word_pinyins']
    words = list(word_pinyins.keys())
    
    # 均匀随机选择
    generated_words = []
    current_chars = 0
    
    while current_chars < target_chars:
        selected_word = random.choice(words)
        generated_words.append(selected_word)
        current_chars += len(selected_word)
    
    # 统计生成的拼音
    generated_pinyins = set()
    word_usage_count = Counter(generated_words)
    
    for word in generated_words:
        if word in word_pinyins:
            generated_pinyins.update(word_pinyins[word])
    
    print(f"✓ 生成文本: {len(generated_words)} 个词，{current_chars} 个字符")
    print(f"✓ 涉及拼音: {len(generated_pinyins)} 个")
    
    return {
        'generated_words': generated_words,
        'generated_pinyins': generated_pinyins,
        'word_usage_count': word_usage_count,
        'total_chars': current_chars
    }

def analyze_actual_generated_text(text_file: str, pinyin_file: str) -> Dict:
    """分析实际生成的文本"""
    print(f"\n=== 分析实际生成文本 ===")
    
    actual_pinyins = set()
    actual_texts = []
    
    # 读取实际生成的文本
    if os.path.exists(text_file):
        try:
            with open(text_file, 'r', encoding='utf-8') as f:
                actual_texts = [line.strip() for line in f if line.strip()]
            print(f"✓ 实际文本: {len(actual_texts)} 行")
        except Exception as e:
            print(f"✗ 读取文本文件失败: {e}")
    
    # 读取实际生成的拼音
    if os.path.exists(pinyin_file):
        try:
            with open(pinyin_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line_pinyins = extract_pinyin_from_text(line.strip())
                    actual_pinyins.update(line_pinyins)
            print(f"✓ 实际拼音: {len(actual_pinyins)} 个")
        except Exception as e:
            print(f"✗ 读取拼音文件失败: {e}")
    
    return {
        'actual_texts': actual_texts,
        'actual_pinyins': actual_pinyins,
        'total_chars': sum(len(text) for text in actual_texts)
    }

def compare_generation_methods(complete_pinyins: Set[str], dict_analysis: Dict, 
                             weighted_result: Dict, uniform_result: Dict, 
                             actual_result: Dict) -> Dict:
    """比较不同生成方法的拼音覆盖率"""
    print(f"\n=== 比较生成方法 ===")
    
    if not complete_pinyins:
        return {}
    
    results = {}
    
    # 分析权重生成
    if weighted_result:
        weighted_coverage = len(weighted_result['generated_pinyins'].intersection(complete_pinyins))
        weighted_rate = weighted_coverage / len(complete_pinyins) * 100
        results['weighted'] = {
            'coverage_count': weighted_coverage,
            'coverage_rate': weighted_rate,
            'total_pinyins': len(weighted_result['generated_pinyins']),
            'missing_pinyins': complete_pinyins - weighted_result['generated_pinyins']
        }
        print(f"✓ 权重生成覆盖率: {weighted_rate:.1f}%")
    
    # 分析均匀生成
    if uniform_result:
        uniform_coverage = len(uniform_result['generated_pinyins'].intersection(complete_pinyins))
        uniform_rate = uniform_coverage / len(complete_pinyins) * 100
        results['uniform'] = {
            'coverage_count': uniform_coverage,
            'coverage_rate': uniform_rate,
            'total_pinyins': len(uniform_result['generated_pinyins']),
            'missing_pinyins': complete_pinyins - uniform_result['generated_pinyins']
        }
        print(f"✓ 均匀生成覆盖率: {uniform_rate:.1f}%")
    
    # 分析实际生成
    if actual_result:
        actual_coverage = len(actual_result['actual_pinyins'].intersection(complete_pinyins))
        actual_rate = actual_coverage / len(complete_pinyins) * 100
        results['actual'] = {
            'coverage_count': actual_coverage,
            'coverage_rate': actual_rate,
            'total_pinyins': len(actual_result['actual_pinyins']),
            'missing_pinyins': complete_pinyins - actual_result['actual_pinyins']
        }
        print(f"✓ 实际生成覆盖率: {actual_rate:.1f}%")
    
    return results

def generate_bias_analysis_report(complete_pinyins: Set[str], dict_analysis: Dict,
                                comparison_results: Dict, report_file: str):
    """生成权重偏差分析报告"""
    print(f"\n=== 生成分析报告: {report_file} ===")
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("权重偏差对拼音覆盖率影响分析报告\n")
            f.write("=" * 60 + "\n\n")
            
            # 基本信息
            f.write("基本信息:\n")
            f.write("-" * 30 + "\n")
            f.write(f"完整拼音集合: {len(complete_pinyins)} 个\n")
            if dict_analysis:
                f.write(f"词典词条数: {len(dict_analysis.get('word_freq', {}))}\n")
                f.write(f"词典拼音数: {len(dict_analysis.get('total_pinyins', set()))}\n")
                f.write(f"高频词拼音数: {len(dict_analysis.get('high_freq_pinyins', set()))}\n")
                f.write(f"低频拼音数: {len(dict_analysis.get('low_freq_pinyins', set()))}\n")
            f.write("\n")
            
            # 覆盖率比较
            f.write("覆盖率比较:\n")
            f.write("-" * 30 + "\n")
            
            methods = ['weighted', 'uniform', 'actual']
            method_names = ['权重生成', '均匀生成', '实际生成']
            
            for method, name in zip(methods, method_names):
                if method in comparison_results:
                    result = comparison_results[method]
                    f.write(f"{name}:\n")
                    f.write(f"  覆盖拼音数: {result['coverage_count']}\n")
                    f.write(f"  覆盖率: {result['coverage_rate']:.2f}%\n")
                    f.write(f"  生成拼音总数: {result['total_pinyins']}\n")
                    f.write(f"  缺失拼音数: {len(result['missing_pinyins'])}\n")
                    f.write("\n")
            
            # 权重偏差分析
            if dict_analysis:
                f.write("权重偏差分析:\n")
                f.write("-" * 30 + "\n")
                
                high_freq_pinyins = dict_analysis.get('high_freq_pinyins', set())
                low_freq_pinyins = dict_analysis.get('low_freq_pinyins', set())
                
                f.write(f"高频词拼音覆盖情况:\n")
                if 'actual' in comparison_results:
                    actual_pinyins = comparison_results['actual']['total_pinyins']
                    high_freq_in_actual = len(high_freq_pinyins.intersection(
                        comparison_results['actual'].get('missing_pinyins', set())
                    ))
                    f.write(f"  高频拼音在实际生成中的缺失: {high_freq_in_actual} 个\n")
                
                f.write(f"\n低频拼音影响:\n")
                f.write(f"  低频拼音总数: {len(low_freq_pinyins)}\n")
                if 'actual' in comparison_results:
                    low_freq_missing = len(low_freq_pinyins.intersection(
                        comparison_results['actual'].get('missing_pinyins', set())
                    ))
                    f.write(f"  低频拼音缺失数: {low_freq_missing}\n")
                    f.write(f"  低频拼音缺失率: {low_freq_missing/len(low_freq_pinyins)*100:.1f}%\n")
            
            # 建议
            f.write("\n改进建议:\n")
            f.write("-" * 30 + "\n")
            f.write("1. 降低高频词权重，增加低频词的选择概率\n")
            f.write("2. 在文本生成时考虑拼音覆盖率作为约束条件\n")
            f.write("3. 使用分层采样，确保各频率段的词都有代表\n")
            f.write("4. 定期检查生成文本的拼音分布，调整权重策略\n")
        
        print(f"✓ 分析报告已生成: {report_file}")
        
    except Exception as e:
        print(f"✗ 生成报告失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("权重偏差对拼音覆盖率影响分析工具")
    print("=" * 60)
    
    config = CONFIG
    
    # 1. 加载完整拼音集合
    complete_pinyins = set()
    if os.path.exists(config['COMPLETE_PINYIN_FILE']):
        try:
            with open(config['COMPLETE_PINYIN_FILE'], 'r', encoding='utf-8') as f:
                content = f.read()
            complete_pinyins = extract_pinyin_from_text(content)
            print(f"✓ 完整拼音集合: {len(complete_pinyins)} 个")
        except Exception as e:
            print(f"✗ 读取完整拼音集合失败: {e}")
    
    # 2. 分析词典词频偏差
    dict_analysis = analyze_word_frequency_bias(config['DICTIONARY_FILE'])
    
    # 3. 模拟不同生成方法
    weighted_result = simulate_weighted_text_generation(dict_analysis)
    uniform_result = simulate_uniform_text_generation(dict_analysis)
    
    # 4. 分析实际生成的文本
    actual_result = analyze_actual_generated_text(
        config['GENERATED_TEXT_FILE'], 
        config['GENERATED_PINYIN_FILE']
    )
    
    # 5. 比较不同方法
    comparison_results = compare_generation_methods(
        complete_pinyins, dict_analysis, weighted_result, uniform_result, actual_result
    )
    
    # 6. 生成分析报告
    generate_bias_analysis_report(
        complete_pinyins, dict_analysis, comparison_results, config['REPORT_FILE']
    )
    
    # 7. 显示总结
    print(f"\n=== 分析总结 ===")
    if comparison_results:
        for method, name in [('weighted', '权重生成'), ('uniform', '均匀生成'), ('actual', '实际生成')]:
            if method in comparison_results:
                rate = comparison_results[method]['coverage_rate']
                print(f"{name}: {rate:.1f}% 覆盖率")
    
    print(f"\n🎉 权重偏差分析完成！")
    print(f"详细报告: {config['REPORT_FILE']}")
    print("=" * 60)

if __name__ == "__main__":
    main()
