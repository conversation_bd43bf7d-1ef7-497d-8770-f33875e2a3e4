#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
讯飞TTS多音色生成器（修复版） - 基于官方demo修改
使用5种不同音色，每种音色生成200条
"""

import websocket
import datetime
import hashlib
import base64
import hmac
import json
from urllib.parse import urlencode
import time
import ssl
from wsgiref.handlers import format_date_time
from datetime import datetime
from time import mktime
import _thread as thread
import os
import wave
import csv

# ==================== 配置区域 ====================
XUNFEI_CONFIG = {
    # API配置 
    "APPID": "fee2aa97",
    "APISecret": "YWYzODMwN2ExNjhlMDBmN2ExMzE5MWQz",
    "APIKey": "a4727f9636df58ee310a31d2b857361a",
     
    # 语音参数配置
    "SPEED": "50",
    "VOLUME": "80",
    "PITCH": "50",
    
    # 文件配置
    "TEXT_FILE": "../shuffled_from_rank_random.txt",
    "PINYIN_FILE": "../shuffled_from_rank_random_pinyin.txt",
    "OUTPUT_DIR": "audio_output",
    "CSV_FILE": "xunfei_audio_info_1000.csv",
    "REQUEST_INTERVAL": 0.5,
    
    # 生成范围配置
    "START_INDEX": 10001,
    "END_INDEX": 11000,
    "TEXT_START_INDEX": 1,
    "TEXT_END_INDEX": 1000,
    "TOTAL_COUNT": 1000
}

# 音色配置 - 5种音色，每种生成200条
VOICE_CONFIGS = [
    {"voice_type": "x4_xiaoyan", "name": "讯飞小燕", "start": 10001, "end": 10200},
    {"voice_type": "x4_yezi", "name": "讯飞小露", "start": 10201, "end": 10400},
    {"voice_type": "aisjiuxu", "name": "讯飞许久", "start": 10401, "end": 10600},
    {"voice_type": "aisjinger", "name": "讯飞小婧", "start": 10601, "end": 10800},
    {"voice_type": "aisbabyxu", "name": "讯飞许小宝", "start": 10801, "end": 11000},
]

class XunfeiTTSFixed:
    """基于官方demo的讯飞TTS类"""
    
    def __init__(self, config):
        self.config = config
        self.audio_data = b""
        self.status = 0
        self.error_msg = ""
        
    def create_ws_param(self, text, voice_type):
        """创建WebSocket参数对象"""
        return Ws_Param(
            APPID=self.config["APPID"],
            APIKey=self.config["APIKey"], 
            APISecret=self.config["APISecret"],
            Text=text,
            voice_type=voice_type,
            speed=self.config["SPEED"],
            volume=self.config["VOLUME"],
            pitch=self.config["PITCH"]
        )
    
    def synthesize_text(self, text, voice_type, timeout=30):
        """合成单个文本"""
        self.audio_data = b""
        self.status = 0
        self.error_msg = ""
        
        # 创建参数对象
        ws_param = self.create_ws_param(text, voice_type)
        
        # 创建WebSocket连接
        websocket.enableTrace(False)
        ws_url = ws_param.create_url()
        
        ws = websocket.WebSocketApp(
            ws_url,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close
        )
        ws.on_open = lambda ws: self.on_open(ws, ws_param)
        
        # 运行WebSocket
        ws.run_forever(sslopt={"cert_reqs": ssl.CERT_NONE})
        
        # 等待完成
        start_time = time.time()
        while self.status not in [2, -1] and (time.time() - start_time) < timeout:
            time.sleep(0.1)
        
        return self.audio_data if self.status == 2 else None
    
    def on_message(self, ws, message):
        """WebSocket消息回调"""
        try:
            message = json.loads(message)
            code = message["code"]
            
            if code != 0:
                self.error_msg = f"API错误: {message['message']} (code: {code})"
                print(f"  ✗ {self.error_msg}")
                self.status = -1
            else:
                data = message.get("data")
                if data:
                    audio = data["audio"]
                    audio_data = base64.b64decode(audio)
                    self.audio_data += audio_data
                    
                    status = data["status"]
                    if status == 2:  # 传输完成
                        self.status = 2
                        ws.close()
        except Exception as e:
            self.error_msg = f"消息处理错误: {e}"
            print(f"  ✗ {self.error_msg}")
            self.status = -1
    
    def on_error(self, ws, error):
        """WebSocket错误回调"""
        self.error_msg = f"WebSocket错误: {error}"
        print(f"  ✗ {self.error_msg}")
        self.status = -1
    
    def on_close(self, ws, close_status_code=None, close_msg=None):
        """WebSocket关闭回调"""
        pass
    
    def on_open(self, ws, ws_param):
        """WebSocket连接成功回调"""
        def run(*args):
            d = {
                "common": ws_param.CommonArgs,
                "business": ws_param.BusinessArgs,
                "data": ws_param.Data,
            }
            d = json.dumps(d)
            ws.send(d)
        
        thread.start_new_thread(run, ())
        self.status = 1

class Ws_Param(object):
    """WebSocket参数类（基于官方demo）"""
    
    def __init__(self, APPID, APIKey, APISecret, Text, voice_type="x4_yezi", speed="50", volume="80", pitch="50"):
        self.APPID = APPID
        self.APIKey = APIKey
        self.APISecret = APISecret
        self.Text = Text

        # 公共参数(common)
        self.CommonArgs = {"app_id": self.APPID}
        
        # 业务参数(business)
        self.BusinessArgs = {
            "aue": "raw", 
            "auf": "audio/L16;rate=16000", 
            "vcn": voice_type,
            "speed": speed,
            "volume": volume,
            "pitch": pitch,
            "tte": "utf8"
        }
        
        # 数据参数
        self.Data = {
            "status": 2, 
            "text": str(base64.b64encode(self.Text.encode('utf-8')), "UTF8")
        }

    def create_url(self):
        """生成WebSocket URL（基于官方demo）"""
        url = 'wss://tts-api.xfyun.cn/v2/tts'
        
        # 生成RFC1123格式的时间戳
        now = datetime.now()
        date = format_date_time(mktime(now.timetuple()))

        # 拼接字符串
        signature_origin = "host: " + "tts-api.xfyun.cn" + "\n"
        signature_origin += "date: " + date + "\n"
        signature_origin += "GET " + "/v2/tts " + "HTTP/1.1"
        
        # 进行hmac-sha256进行加密
        signature_sha = hmac.new(
            self.APISecret.encode('utf-8'), 
            signature_origin.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')

        authorization_origin = "api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"" % (
            self.APIKey, "hmac-sha256", "host date request-line", signature_sha)
        authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode(encoding='utf-8')
        
        # 将请求的鉴权参数组合为字典
        v = {
            "authorization": authorization,
            "date": date,
            "host": "tts-api.xfyun.cn"
        }
        
        # 拼接鉴权参数，生成url
        url = url + '?' + urlencode(v)
        return url

def save_audio_as_wav(audio_data, output_path, sample_rate=16000):
    """保存PCM音频数据为WAV文件"""
    with wave.open(output_path, 'wb') as wav_file:
        wav_file.setnchannels(1)  # 单声道
        wav_file.setsampwidth(2)  # 16位
        wav_file.setframerate(sample_rate)  # 采样率
        wav_file.writeframes(audio_data)

def test_single_voice():
    """测试单个音色"""
    print("=" * 60)
    print("讯飞TTS修复版测试")
    print("=" * 60)
    
    config = XUNFEI_CONFIG
    
    # 检查文件
    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return False
    
    # 读取测试文本
    try:
        with open(config['TEXT_FILE'], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 加载文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return False
    
    # 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)
    
    # 创建TTS对象
    tts = XunfeiTTSFixed(config)
    
    # 测试文本
    test_text = texts[0] if texts else "这是一个测试文本"
    voice_type = "x4_xiaoyan"
    
    print(f"\n开始测试:")
    print(f"  文本: {test_text}")
    print(f"  音色: {voice_type}")
    
    # 合成语音
    audio_data = tts.synthesize_text(test_text, voice_type)
    
    if audio_data:
        try:
            # 保存音频文件
            audio_path = os.path.join(config["OUTPUT_DIR"], "test_fixed.wav")
            save_audio_as_wav(audio_data, audio_path)
            print(f"✓ 测试成功！音频已保存到: {audio_path}")
            print(f"✓ 音频大小: {len(audio_data)} 字节")
            return True
        except Exception as e:
            print(f"✗ 保存音频失败: {e}")
            return False
    else:
        print(f"✗ 测试失败: {tts.error_msg}")
        return False

def main():
    """主函数"""
    success = test_single_voice()
    
    if success:
        print(f"\n🎉 讯飞TTS修复版测试成功！")
        print(f"可以继续开发完整的批量生成功能")
    else:
        print(f"\n⚠️ 讯飞TTS修复版测试失败")
        print(f"请检查网络连接和API配置")

if __name__ == "__main__":
    main()
