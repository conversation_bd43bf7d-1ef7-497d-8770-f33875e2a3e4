#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的文本生成器
解决语义不通和重复文本问题
"""

import random
import pypinyin
from pypinyin import Style
import re
from collections import defaultdict

# 按类别组织的缩写及其语义相关的模板
abbreviation_templates = {
    # 技术类
    "AI": [
        "AI助手很智能", "使用AI提高效率", "学习AI基础知识", 
        "优化AI模型", "测试AI性能", "AI技术发展迅速",
        "AI算法很复杂", "训练AI需要数据"
    ],
    "PDF": [
        "下载PDF文件", "保存为PDF格式", "共享PDF文档", 
        "查看PDF内容", "转换PDF格式", "PDF文件很大",
        "打印PDF页面", "编辑PDF内容"
    ],
    "API": [
        "调用API接口", "API返回数据", "测试API功能",
        "API文档详细", "更新API版本", "API响应很快",
        "集成第三方API", "API密钥管理"
    ],
    "GPS": [
        "GPS定位准确", "开启GPS导航", "GPS信号很弱",
        "GPS追踪位置", "校准GPS设备", "GPS卫星连接",
        "GPS路线规划", "GPS坐标显示"
    ],
    "USB": [
        "插入USB设备", "USB传输数据", "USB接口损坏",
        "格式化USB盘", "USB充电很慢", "USB驱动安装",
        "USB设备识别", "USB接口类型"
    ],
    "WiFi": [
        "连接WiFi网络", "WiFi密码错误", "WiFi信号很强",
        "重启WiFi路由器", "WiFi速度很快", "WiFi频段选择",
        "WiFi热点分享", "WiFi安全设置"
    ],
    "CPU": [
        "CPU使用率高", "CPU温度过热", "CPU性能测试",
        "CPU核心数量", "CPU频率调节", "CPU架构设计",
        "CPU缓存大小", "CPU功耗控制"
    ],
    "RAM": [
        "RAM内存不足", "增加RAM容量", "RAM频率很高",
        "RAM兼容性好", "清理RAM空间", "RAM条数量",
        "RAM品牌选择", "RAM超频设置"
    ],
    "SSD": [
        "SSD读写很快", "SSD容量充足", "SSD寿命很长",
        "安装SSD硬盘", "SSD价格下降", "SSD接口类型",
        "SSD固件更新", "SSD健康检测"
    ],
    "VPN": [
        "连接VPN服务", "VPN速度很慢", "VPN保护隐私",
        "VPN服务器选择", "VPN协议类型", "VPN连接稳定",
        "VPN流量加密", "VPN账号管理"
    ]
}

# 语义相关的通用模板
general_templates = [
    "请检查{abbr}状态", "更新{abbr}设置", "重启{abbr}服务",
    "配置{abbr}参数", "监控{abbr}性能", "备份{abbr}数据",
    "恢复{abbr}功能", "优化{abbr}配置", "测试{abbr}连接",
    "安装{abbr}驱动", "卸载{abbr}程序", "升级{abbr}版本",
    "修复{abbr}错误", "启用{abbr}功能", "禁用{abbr}选项",
    "扫描{abbr}设备", "清理{abbr}缓存", "同步{abbr}数据"
]

# 语义相关的混合模板
mixed_templates = [
    "同时启用{abbr1}和{abbr2}功能", "比较{abbr1}与{abbr2}性能",
    "从{abbr1}切换到{abbr2}", "{abbr1}兼容{abbr2}协议",
    "将{abbr1}数据导入{abbr2}", "{abbr1}和{abbr2}同步更新",
    "选择{abbr1}还是{abbr2}更好", "{abbr1}配合{abbr2}使用",
    "备份{abbr1}到{abbr2}设备", "{abbr1}通过{abbr2}连接"
]

# 语义相关的前缀和后缀
semantic_prefixes = {
    "请求": ["请", "麻烦", "帮忙"],
    "建议": ["建议", "推荐", "最好"],
    "需要": ["需要", "必须", "应该"],
    "可能": ["可能", "也许", "或许"],
    "确认": ["确认", "检查", "验证"]
}

semantic_suffixes = {
    "疑问": ["吗？", "呢？", "吧？"],
    "肯定": ["。", "！", "的。"],
    "语气": ["哦。", "啊！", "呢。"],
    "强调": ["！！", "。。", "~~~"]
}

# 数字拼音映射
DIGIT_PINYIN = {
    '0': 'ling2', '1': 'yi1', '2': 'er4', '3': 'san1',
    '4': 'si4', '5': 'wu3', '6': 'liu4', '7': 'qi1',
    '8': 'ba1', '9': 'jiu3'
}

def chinese_to_pinyin(text):
    """中文转拼音函数（优化版）"""
    # 处理特殊缩写：将连续大写字母拆分为单个字母
    text = re.sub(r'([A-Z]{2,})', lambda m: ' '.join(m.group(0)), text)
    
    pinyin_list = []
    for char in text:
        if '\u4e00' <= char <= '\u9fff':  # 中文字符
            p = pypinyin.pinyin(char, style=Style.TONE3, heteronym=False)
            if p:
                pinyin_list.append(p[0][0])
        elif char.isupper():  # 大写字母
            pinyin_list.append(char)
        elif char.isdigit():  # 数字
            pinyin_list.append(DIGIT_PINYIN.get(char, char))
        else:  # 其他字符
            pinyin_list.append(char)
    
    return " ".join(pinyin_list)

def generate_semantic_text(template, abbr, abbr2=None):
    """生成语义相关的文本"""
    if abbr2:
        text = template.replace("{abbr1}", abbr).replace("{abbr2}", abbr2)
    else:
        text = template.replace("{abbr}", abbr)
    
    return text

def add_semantic_enhancements(text, enhancement_prob=0.3):
    """添加语义相关的前缀和后缀"""
    enhanced_text = text
    
    # 随机添加语义相关的前缀
    if random.random() < enhancement_prob:
        prefix_category = random.choice(list(semantic_prefixes.keys()))
        prefix = random.choice(semantic_prefixes[prefix_category])
        enhanced_text = prefix + enhanced_text
    
    # 随机添加语义相关的后缀
    if random.random() < enhancement_prob:
        suffix_category = random.choice(list(semantic_suffixes.keys()))
        suffix = random.choice(semantic_suffixes[suffix_category])
        enhanced_text += suffix
    
    return enhanced_text

def generate_unique_dataset(num_samples):
    """生成唯一的数据集，避免重复"""
    results = []
    seen_texts = set()  # 用于去重
    all_abbrs = list(abbreviation_templates.keys())
    
    # 统计生成数量
    generation_stats = defaultdict(int)
    
    # 1. 使用特定模板生成（每个模板只生成一次）
    print("生成特定模板文本...")
    for abbr, templates in abbreviation_templates.items():
        for template in templates:
            text = generate_semantic_text(template, abbr)
            if text not in seen_texts:
                enhanced_text = add_semantic_enhancements(text, 0.2)  # 降低增强概率
                pinyin_text = chinese_to_pinyin(enhanced_text)
                results.append((enhanced_text, pinyin_text))
                seen_texts.add(text)
                generation_stats["specific"] += 1
    
    # 2. 使用通用模板生成
    print("生成通用模板文本...")
    attempts = 0
    max_attempts = num_samples * 3  # 避免无限循环
    
    while len(results) < num_samples * 0.7 and attempts < max_attempts:
        template = random.choice(general_templates)
        abbr = random.choice(all_abbrs)
        text = generate_semantic_text(template, abbr)
        
        if text not in seen_texts:
            enhanced_text = add_semantic_enhancements(text, 0.25)
            pinyin_text = chinese_to_pinyin(enhanced_text)
            results.append((enhanced_text, pinyin_text))
            seen_texts.add(text)
            generation_stats["general"] += 1
        
        attempts += 1
    
    # 3. 使用混合模板生成
    print("生成混合模板文本...")
    attempts = 0
    while len(results) < num_samples and attempts < max_attempts:
        template = random.choice(mixed_templates)
        abbr1, abbr2 = random.sample(all_abbrs, 2)
        text = generate_semantic_text(template, abbr1, abbr2)
        
        if text not in seen_texts:
            enhanced_text = add_semantic_enhancements(text, 0.2)
            pinyin_text = chinese_to_pinyin(enhanced_text)
            results.append((enhanced_text, pinyin_text))
            seen_texts.add(text)
            generation_stats["mixed"] += 1
        
        attempts += 1
    
    # 打印生成统计
    print(f"生成统计:")
    print(f"  特定模板: {generation_stats['specific']} 条")
    print(f"  通用模板: {generation_stats['general']} 条")
    print(f"  混合模板: {generation_stats['mixed']} 条")
    print(f"  总计: {len(results)} 条")
    print(f"  去重率: {len(seen_texts)/len(results)*100:.1f}%")
    
    return results[:num_samples]

def validate_text_quality(data):
    """验证文本质量"""
    print("\n验证文本质量...")
    
    # 检查重复
    texts = [item[0] for item in data]
    unique_texts = set(texts)
    duplicate_rate = (len(texts) - len(unique_texts)) / len(texts) * 100
    
    # 检查长度分布
    lengths = [len(text) for text in texts]
    avg_length = sum(lengths) / len(lengths)
    min_length = min(lengths)
    max_length = max(lengths)
    
    # 检查语义完整性（简单检查）
    incomplete_count = 0
    for text in texts:
        # 检查是否有明显的语义问题
        if text.count('？') > 1 or text.count('！') > 2:
            incomplete_count += 1
    
    print(f"质量报告:")
    print(f"  重复率: {duplicate_rate:.2f}%")
    print(f"  平均长度: {avg_length:.1f} 字符")
    print(f"  长度范围: {min_length}-{max_length} 字符")
    print(f"  可能有问题的文本: {incomplete_count} 条")
    
    # 显示一些示例
    print(f"\n文本示例:")
    for i, (text, pinyin) in enumerate(data[:5]):
        print(f"  {i+1}. {text}")
        print(f"     {pinyin}")

def save_data(data, chinese_file, pinyin_file):
    """保存数据到文件"""
    chinese_lines = []
    pinyin_lines = []
    
    for text, pinyin_text in data:
        chinese_lines.append(text)
        pinyin_lines.append(pinyin_text)
    
    with open(chinese_file, "w", encoding="utf-8") as f:
        f.write("\n".join(chinese_lines))
    
    with open(pinyin_file, "w", encoding="utf-8") as f:
        f.write("\n".join(pinyin_lines))
    
    print(f"\n文件保存:")
    print(f"  中文文本: {chinese_file}")
    print(f"  拼音文本: {pinyin_file}")

def main():
    """主程序"""
    print("=" * 60)
    print("改进的文本生成器")
    print("解决语义不通和重复文本问题")
    print("=" * 60)
    
    # 生成1750条数据
    target_samples = 1750
    print(f"目标生成: {target_samples} 条文本")
    
    # 生成数据集
    dataset = generate_unique_dataset(target_samples)
    
    # 验证质量
    validate_text_quality(dataset)
    
    # 保存到文件
    save_data(dataset, "sentences_improved.txt", "pinyin_improved.txt")
    
    print(f"\n🎉 生成完成！")
    print(f"实际生成: {len(dataset)} 条数据")
    print("=" * 60)

if __name__ == "__main__":
    main()
