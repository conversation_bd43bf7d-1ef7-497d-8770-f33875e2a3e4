#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的文本生成器
解决语义不通和重复文本问题
"""

import random
import pypinyin
from pypinyin import Style
import re
from collections import defaultdict

# 使用您提供的丰富模板，并进行优化
abbreviation_templates = {
    # 技术类
    "AI": ["{abbr}助手很智能", "使用{abbr}提高效率", "学习{abbr}基础知识", "优化{abbr}模型", "测试{abbr}性能","{abbr}是一种智能技术","{abbr}的中文名为人工智能","{abbr}技术通过模拟人类智能来实现机器学习和推理","{abbr}技术广泛应用于计算机软件领域","{abbr}技术是计算机软件开发中的一种关键技术"],
    "PDF": ["下载{abbr}文件", "保存为{abbr}格式", "共享{abbr}文档", "查看{abbr}内容", "转换{abbr}格式","{abbr}是一种文件格式","{abbr}的中文名为便携式文档格式","{abbr}技术通过使用统一的格式来存储文档","{abbr}技术广泛应用于办公软件领域","{abbr}技术是办公软件中的一种关键技术"],
    "USB": ["连接{abbr}设备", "插入{abbr}驱动器", "测试{abbr}端口", "需要{abbr}扩展坞", "使用{abbr}传输数据"],
    "GPS": ["开启{abbr}导航", "更新{abbr}地图", "设置{abbr}目的地", "信号丢失{abbr}定位", "车载{abbr}系统"],
    "TTS": ["打开{abbr}功能", "使用{abbr}朗读", "测试{abbr}系统", "中文{abbr}效果不错", "语音{abbr}转换"],
    "ASR": ["开启{abbr}功能", "使用{abbr}识别", "测试{abbr}系统", "中文{abbr}效果不错", "语音{abbr}转换"],
    "VR": ["体验{abbr}游戏", "戴上{abbr}头盔", "测试{abbr}设备", "虚拟{abbr}世界", "增强{abbr}现实"],
    "AR": ["体验{abbr}游戏", "戴上{abbr}头盔", "测试{abbr}设备", "虚拟{abbr}世界", "增强{abbr}现实"],
    "MR": ["体验{abbr}游戏", "戴上{abbr}头盔", "测试{abbr}设备", "虚拟{abbr}世界", "增强{abbr}现实"],
    "ML": ["学习{abbr}算法", "使用{abbr}模型", "测试{abbr}效果", "中文{abbr}效果不错", "机器{abbr}学习","{abbr}是一种机器学习技术","{abbr}的中文名为机器学习","{abbr}技术通过让机器从数据中自动学习规律和模式来实现智能化","{abbr}技术广泛应用于计算机软件领域","{abbr}技术是计算机软件开发中的一种关键技术"],
    "LAN": ["连接{abbr}网络", "设置{abbr}密码", "共享{abbr}文件", "测试{abbr}速度", "使用{abbr}传输数据"],
    "WAN": ["连接{abbr}网络", "设置{abbr}密码", "共享{abbr}文件", "测试{abbr}速度", "使用{abbr}传输数据"],
    "IP": ["设置{abbr}地址", "检查{abbr}冲突", "配置{abbr}静态", "临时{abbr}更改", "理解{abbr}概念","{abbr}是一种网络协议","{abbr}的中文名为互联网协议","{abbr}技术通过在客户端和服务器之间传输数据来实现网络通信","{abbr}技术广泛应用于网页开发领域","{abbr}技术是网页开发中的一种关键技术"],
    "URL": ["访问{abbr}网站", "记住{abbr}地址", "分享{abbr}链接", "检查{abbr}拼写", "理解{abbr}概念","{abbr}是一种网络地址","{abbr}的中文名为统一资源定位符","{abbr}技术通过使用统一的地址格式来标识网络资源","{abbr}技术广泛应用于网页开发领域","{abbr}技术是网页开发中的一种关键技术"],
    "HTTP": ["访问{abbr}网站", "记住{abbr}地址", "分享{abbr}链接", "检查{abbr}拼写", "理解{abbr}概念","{abbr}是一种网络协议","{abbr}的中文名为超文本传输协议","{abbr}技术通过在客户端和服务器之间传输数据来实现网络通信","{abbr}技术广泛应用于网页开发领域","{abbr}技术是网页开发中的一种关键技术"],
    "HTTPS": ["访问{abbr}网站", "记住{abbr}地址", "分享{abbr}链接", "检查{abbr}拼写", "理解{abbr}概念","{abbr}是一种网络协议","{abbr}的中文名为超文本传输协议安全版","{abbr}技术通过在客户端和服务器之间传输加密数据来实现网络通信","{abbr}技术广泛应用于网页开发领域","{abbr}技术是网页开发中的一种关键技术"],
    "TCP": ["理解{abbr}协议", "检查{abbr}连接", "优化{abbr}性能", "测试{abbr}端口", "使用{abbr}传输数据","{abbr}是一种网络协议","{abbr}的中文名为传输控制协议","{abbr}技术通过在客户端和服务器之间传输数据来实现网络通信","{abbr}技术广泛应用于网页开发领域","{abbr}技术是网页开发中的一种关键技术"],
    "UDP": ["理解{abbr}协议", "检查{abbr}连接", "优化{abbr}性能", "测试{abbr}端口", "使用{abbr}传输数据","{abbr}是一种网络协议","{abbr}的中文名为用户数据报协议","{abbr}技术通过在客户端和服务器之间传输数据来实现网络通信","{abbr}技术广泛应用于网页开发领域","{abbr}技术是网页开发中的一种关键技术"],
    "HTML": ["编写{abbr}代码", "学习{abbr}基础", "优化{abbr}结构", "理解{abbr}语义", "使用{abbr}布局","{abbr}与网页设计相关","{abbr}的中文名为超文本标记语言","{abbr}技术通过使用标记语言来描述网页的结构","{abbr}技术广泛应用于网页开发领域","{abbr}技术是网页开发中的一种关键技术"],
    "CSS": ["编写{abbr}代码", "学习{abbr}基础", "优化{abbr}样式", "理解{abbr}布局", "使用{abbr}动画","{abbr}与网页设计相关","{abbr}的中文名为层叠样式表","{abbr}技术通过使用标记语言来描述网页的样式","{abbr}技术广泛应用于网页开发领域","{abbr}技术是网页开发中的一种关键技术"],
    "JS": ["编写{abbr}代码", "学习{abbr}基础", "优化{abbr}性能", "理解{abbr}逻辑", "使用{abbr}交互","{abbr}是一种客户端脚本语言","{abbr}技术通过在客户端执行脚本来实现网页的交互功能","{abbr}技术广泛应用于网页开发领域","{abbr}技术是网页开发中的一种关键技术"],
    "API": ["调用{abbr}接口", "理解{abbr}文档", "优化{abbr}性能", "处理{abbr}错误", "调用安全的{abbr}","{abbr}是一种编程接口","{abbr}的中文名为应用程序接口","{abbr}技术通过提供一组函数和协议来实现不同软件之间的交互","{abbr}技术广泛应用于计算机软件领域","{abbr}技术是计算机软件开发中的一种关键技术"],
    "SDK": ["集成{abbr}开发包", "理解{abbr}文档", "优化{abbr}性能", "处理{abbr}错误", "使用{abbr}安全","{abbr}是一种开发工具包","{abbr}的中文名为软件开发工具包","{abbr}技术通过提供一组工具和库来简化软件开发过程","{abbr}技术广泛应用于计算机软件领域","{abbr}技术是计算机软件开发中的一种关键技术"],
    "UI": ["设计{abbr}界面", "优化{abbr}体验", "理解{abbr}原则", "使用{abbr}组件", "测试{abbr}交互"],
    "UX": ["优化{abbr}体验", "理解{abbr}原则", "使用{abbr}组件", "测试{abbr}交互", "设计{abbr}流程"],
    "XML": ["编写{abbr}代码", "学习{abbr}基础", "优化{abbr}结构", "理解{abbr}语义", "使用{abbr}布局"],
    "JWT": ["生成{abbr}令牌", "验证{abbr}签名", "刷新{abbr}令牌", "处理{abbr}错误", "使用{abbr}安全"],
    "SSL": ["使用{abbr}加密", "理解{abbr}原理", "处理{abbr}错误", "验证{abbr}证书", "使用{abbr}安全","{abbr}是一种网络安全协议","{abbr}的中文名为安全套接字层","{abbr}技术通过在客户端和服务器之间建立加密连接来实现网络安全","{abbr}技术广泛应用于网页开发领域","{abbr}技术是网页开发中的一种关键技术"],
    "TLS": ["使用{abbr}加密", "理解{abbr}原理", "处理{abbr}错误", "验证{abbr}证书", "使用{abbr}安全","{abbr}是一种网络安全协议","{abbr}的中文名为传输层安全协议","{abbr}技术通过在客户端和服务器之间建立加密连接来实现网络安全","{abbr}技术广泛应用于网页开发领域","{abbr}技术是网页开发中的一种关键技术"],
    "CPU": ["升级{abbr}频率", "清理{abbr}缓存", "解决{abbr}散热问题", "更换{abbr}风扇", "优化{abbr}性能","{abbr}是一种中央处理器","{abbr}的中文名为中央处理器","{abbr}技术通过专门的硬件来执行指令和运算","{abbr}技术广泛应用于计算机软件领域","{abbr}技术是计算机软件开发中的一种关键技术"],
    "GPU": ["升级{abbr}显卡", "清理{abbr}缓存", "解决{abbr}散热问题", "更换{abbr}风扇", "优化{abbr}性能","{abbr}是一种图形处理器","{abbr}的中文名为图形处理器","{abbr}技术通过专门的硬件来加速图形渲染和处理","{abbr}技术广泛应用于计算机软件领域","{abbr}技术是计算机软件开发中的一种关键技术"],
    "RAM": ["升级{abbr}内存", "清理{abbr}缓存", "解决{abbr}散热问题", "更换{abbr}风扇", "优化{abbr}性能","{abbr}是一种随机存取存储器","{abbr}的中文名为随机存取存储器","{abbr}技术通过在芯片上集成存储单元来实现数据的快速读写","{abbr}技术广泛应用于计算机软件领域","{abbr}技术是计算机软件开发中的一种关键技术"],
    "ROM": ["升级{abbr}固件", "清理{abbr}缓存", "解决{abbr}散热问题", "更换{abbr}风扇", "优化{abbr}性能","{abbr}是一种只读存储器","{abbr}的中文名为只读存储器","{abbr}技术通过在芯片上集成存储单元来实现数据的只读存储","{abbr}技术广泛应用于计算机软件领域","{abbr}技术是计算机软件开发中的一种关键技术"],
    "OS": ["升级{abbr}系统", "清理{abbr}缓存", "解决{abbr}散热问题", "更换{abbr}风扇", "优化{abbr}性能","{abbr}是一种操作系统","{abbr}的中文名为操作系统","{abbr}技术通过提供一个软件平台来管理计算机硬件和软件资源","{abbr}技术广泛应用于计算机软件领域","{abbr}技术是计算机软件开发中的一种关键技术"],
    "PHP": ["编写{abbr}代码", "学习{abbr}基础", "优化{abbr}性能", "理解{abbr}语法", "使用{abbr}框架","{abbr}是一种服务器端脚本语言","{abbr}的中文名为PHP","{abbr}技术通过在服务器端执行脚本来生成动态网页","{abbr}技术广泛应用于网页开发领域","{abbr}技术是网页开发中的一种关键技术"],
    "ER": ["绘制{abbr}图表", "学习{abbr}图是数据库的基础", "修改{abbr}图的实体与属性", "完善{abbr}图", "使用{abbr}图"],
    "OCR": ["使用{abbr}识别文本", "扫描{abbr}文档", "提高{abbr}准确率", "处理{abbr}错误", "优化{abbr}算法"],
    "NLP": ["应用{abbr}技术", "学习{abbr}基础", "开发{abbr}模型", "优化{abbr}性能", "理解{abbr}概念"],
    "IoT": ["连接{abbr}设备", "管理{abbr}网络", "优化{abbr}安全性", "开发{abbr}应用", "理解{abbr}架构"],
    "DNS": ["配置{abbr}服务器", "理解{abbr}解析", "检查{abbr}记录", "优化{abbr}速度", "解决{abbr}问题","{abbr}是一种网络服务","{abbr}的中文名为域名系统","{abbr}技术通过将域名解析为IP地址来实现网络通信","{abbr}技术广泛应用于网页开发领域","{abbr}技术是网页开发中的一种关键技术"],
    "FTP": ["使用{abbr}传输文件", "配置{abbr}服务器", "优化{abbr}安全性", "解决{abbr}连接问题", "理解{abbr}协议"],
    "SVG": ["创建{abbr}图像", "优化{abbr}文件大小", "使用{abbr}图标", "理解{abbr}格式", "编辑{abbr}文件"],
    "PNG": ["保存为{abbr}格式", "优化{abbr}压缩", "使用{abbr}图像", "理解{abbr}特点", "转换{abbr}格式"],
    "JPEG": ["保存为{abbr}格式", "优化{abbr}质量", "使用{abbr}照片", "理解{abbr}特点", "转换{abbr}格式"],
    "MP3": ["播放{abbr}文件", "转换{abbr}格式", "优化{abbr}音质", "编辑{abbr}音频", "分享{abbr}文件"],
    "MP4": ["播放{abbr}文件", "转换{abbr}格式", "优化{abbr}画质", "编辑{abbr}视频", "分享{abbr}文件"],
    "HDMI": ["连接{abbr}线缆", "使用{abbr}接口", "优化{abbr}输出", "解决{abbr}问题", "理解{abbr}标准","{abbr}是一种视频传输接口","{abbr}的中文名为高清数字媒体接口","{abbr}技术通过使用数字信号来传输视频和音频","{abbr}技术广泛应用于电子产品领域","{abbr}技术是电子产品中的一种关键技术"],
    "VGA": ["连接{abbr}线缆", "使用{abbr}接口", "优化{abbr}输出", "解决{abbr}问题", "理解{abbr}标准","{abbr}是一种视频传输接口","{abbr}的中文名为视频图形数组","{abbr}技术通过使用模拟信号来传输视频","{abbr}技术广泛应用于电子产品领域","{abbr}技术是电子产品中的一种关键技术"],
    "4G": ["使用{abbr}网络", "优化{abbr}速度", "解决{abbr}问题", "理解{abbr}技术", "比较{abbr}和5G"],
    "5G": ["使用{abbr}网络", "优化{abbr}速度", "解决{abbr}问题", "理解{abbr}技术", "比较{abbr}和4G"],
    "VPN": ["连接{abbr}网络", "优化{abbr}速度", "解决{abbr}问题", "保护{abbr}安全", "理解{abbr}原理","{abbr}是一种网络安全设备","{abbr}的中文名为虚拟专用网络","{abbr}技术通过在公共网络中创建专用网络来保护网络安全","{abbr}技术广泛应用于网络安全领域","{abbr}技术是网络安全中的一种关键技术"],
    "LLM": ["了解{abbr}模型", "应用{abbr}技术", "开发{abbr}模型", "优化{abbr}的性能", "理解{abbr}相关概念"]
}

# 语义相关的通用模板
general_templates = [
    "请检查{abbr}状态", "更新{abbr}设置", "重启{abbr}服务",
    "配置{abbr}参数", "监控{abbr}性能", "备份{abbr}数据",
    "恢复{abbr}功能", "优化{abbr}配置", "测试{abbr}连接",
    "安装{abbr}驱动", "卸载{abbr}程序", "升级{abbr}版本",
    "修复{abbr}错误", "启用{abbr}功能", "禁用{abbr}选项",
    "扫描{abbr}设备", "清理{abbr}缓存", "同步{abbr}数据"
]

# 语义相关的混合模板
mixed_templates = [
    "同时启用{abbr1}和{abbr2}功能", "比较{abbr1}与{abbr2}性能",
    "从{abbr1}切换到{abbr2}", "{abbr1}兼容{abbr2}协议",
    "将{abbr1}数据导入{abbr2}", "{abbr1}和{abbr2}同步更新",
    "选择{abbr1}还是{abbr2}更好", "{abbr1}配合{abbr2}使用",
    "备份{abbr1}到{abbr2}设备", "{abbr1}通过{abbr2}连接"
]

# 语义相关的前缀和后缀
semantic_prefixes = {
    "请求": ["请", "麻烦", "帮忙"],
    "建议": ["建议", "推荐", "最好"],
    "需要": ["需要", "必须", "应该"],
    "可能": ["可能", "也许", "或许"],
    "确认": ["确认", "检查", "验证"]
}

semantic_suffixes = {
    "疑问": ["吗？", "呢？", "吧？"],
    "肯定": ["。", "！", "的。"],
    "语气": ["哦。", "啊！", "呢。"],
    "强调": ["！！", "。。", "~~~"]
}

# 数字拼音映射
DIGIT_PINYIN = {
    '0': 'ling2', '1': 'yi1', '2': 'er4', '3': 'san1',
    '4': 'si4', '5': 'wu3', '6': 'liu4', '7': 'qi1',
    '8': 'ba1', '9': 'jiu3'
}

def chinese_to_pinyin(text):
    """中文转拼音函数（优化版）"""
    # 处理特殊缩写：将连续大写字母拆分为单个字母
    text = re.sub(r'([A-Z]{2,})', lambda m: ' '.join(m.group(0)), text)
    
    pinyin_list = []
    for char in text:
        if '\u4e00' <= char <= '\u9fff':  # 中文字符
            p = pypinyin.pinyin(char, style=Style.TONE3, heteronym=False)
            if p:
                pinyin_list.append(p[0][0])
        elif char.isupper():  # 大写字母
            pinyin_list.append(char)
        elif char.isdigit():  # 数字
            pinyin_list.append(DIGIT_PINYIN.get(char, char))
        else:  # 其他字符
            pinyin_list.append(char)
    
    return " ".join(pinyin_list)

def generate_semantic_text(template, abbr, abbr2=None):
    """生成语义相关的文本"""
    if abbr2:
        text = template.replace("{abbr1}", abbr).replace("{abbr2}", abbr2)
    else:
        text = template.replace("{abbr}", abbr)
    
    return text

def add_semantic_enhancements(text, enhancement_prob=0.3):
    """添加语义相关的前缀和后缀"""
    enhanced_text = text
    
    # 随机添加语义相关的前缀
    if random.random() < enhancement_prob:
        prefix_category = random.choice(list(semantic_prefixes.keys()))
        prefix = random.choice(semantic_prefixes[prefix_category])
        enhanced_text = prefix + enhanced_text
    
    # 随机添加语义相关的后缀
    if random.random() < enhancement_prob:
        suffix_category = random.choice(list(semantic_suffixes.keys()))
        suffix = random.choice(semantic_suffixes[suffix_category])
        enhanced_text += suffix
    
    return enhanced_text

def generate_rich_dataset(num_samples):
    """生成丰富多样的数据集"""
    results = []
    seen_texts = set()  # 用于去重
    all_abbrs = list(abbreviation_templates.keys())

    # 统计生成数量
    generation_stats = defaultdict(int)

    # 1. 使用所有特定模板生成（确保每个模板都被使用）
    print("生成特定模板文本...")
    for abbr, templates in abbreviation_templates.items():
        for template in templates:
            text = generate_semantic_text(template, abbr)
            if text not in seen_texts:
                # 随机决定是否增强
                if random.random() < 0.3:
                    enhanced_text = add_semantic_enhancements(text, 0.4)
                else:
                    enhanced_text = text
                pinyin_text = chinese_to_pinyin(enhanced_text)
                results.append((enhanced_text, pinyin_text))
                seen_texts.add(text)
                generation_stats["specific"] += 1

    print(f"特定模板生成了 {generation_stats['specific']} 条")

    # 2. 使用通用模板生成更多变化
    print("生成通用模板文本...")
    attempts = 0
    max_attempts = num_samples * 5
    target_general = min(800, num_samples - len(results))  # 目标生成800条通用模板

    while generation_stats["general"] < target_general and attempts < max_attempts:
        template = random.choice(general_templates)
        abbr = random.choice(all_abbrs)
        text = generate_semantic_text(template, abbr)

        if text not in seen_texts:
            # 增加变化：随机组合多个增强
            enhanced_text = text
            if random.random() < 0.4:
                enhanced_text = add_semantic_enhancements(text, 0.5)

            pinyin_text = chinese_to_pinyin(enhanced_text)
            results.append((enhanced_text, pinyin_text))
            seen_texts.add(text)
            generation_stats["general"] += 1

        attempts += 1

    print(f"通用模板生成了 {generation_stats['general']} 条")

    # 3. 使用混合模板生成
    print("生成混合模板文本...")
    attempts = 0
    target_mixed = num_samples - len(results)

    while generation_stats["mixed"] < target_mixed and attempts < max_attempts:
        template = random.choice(mixed_templates)
        abbr1, abbr2 = random.sample(all_abbrs, 2)
        text = generate_semantic_text(template, abbr1, abbr2)

        if text not in seen_texts:
            enhanced_text = add_semantic_enhancements(text, 0.3)
            pinyin_text = chinese_to_pinyin(enhanced_text)
            results.append((enhanced_text, pinyin_text))
            seen_texts.add(text)
            generation_stats["mixed"] += 1

        attempts += 1

    print(f"混合模板生成了 {generation_stats['mixed']} 条")

    # 4. 如果还不够，生成创新组合
    if len(results) < num_samples:
        print("生成创新组合文本...")
        remaining = num_samples - len(results)

        # 创建更复杂的组合
        creative_templates = [
            "同时配置{abbr1}和{abbr2}参数",
            "从{abbr1}迁移到{abbr2}平台",
            "集成{abbr1}与{abbr2}服务",
            "对比{abbr1}和{abbr2}的优缺点",
            "学习{abbr1}后再学{abbr2}",
            "使用{abbr1}开发{abbr2}应用",
            "通过{abbr1}优化{abbr2}性能",
            "将{abbr1}数据导出为{abbr2}格式"
        ]

        attempts = 0
        while generation_stats["creative"] < remaining and attempts < max_attempts:
            template = random.choice(creative_templates)
            abbr1, abbr2 = random.sample(all_abbrs, 2)
            text = template.replace("{abbr1}", abbr1).replace("{abbr2}", abbr2)

            if text not in seen_texts:
                enhanced_text = add_semantic_enhancements(text, 0.25)
                pinyin_text = chinese_to_pinyin(enhanced_text)
                results.append((enhanced_text, pinyin_text))
                seen_texts.add(text)
                generation_stats["creative"] += 1

            attempts += 1

        print(f"创新组合生成了 {generation_stats['creative']} 条")

    # 打印最终统计
    print(f"\n最终生成统计:")
    print(f"  特定模板: {generation_stats['specific']} 条")
    print(f"  通用模板: {generation_stats['general']} 条")
    print(f"  混合模板: {generation_stats['mixed']} 条")
    print(f"  创新组合: {generation_stats['creative']} 条")
    print(f"  总计: {len(results)} 条")
    print(f"  去重率: 100.0%")

    return results[:num_samples]

def validate_text_quality(data):
    """验证文本质量"""
    print("\n验证文本质量...")
    
    # 检查重复
    texts = [item[0] for item in data]
    unique_texts = set(texts)
    duplicate_rate = (len(texts) - len(unique_texts)) / len(texts) * 100
    
    # 检查长度分布
    lengths = [len(text) for text in texts]
    avg_length = sum(lengths) / len(lengths)
    min_length = min(lengths)
    max_length = max(lengths)
    
    # 检查语义完整性（简单检查）
    incomplete_count = 0
    for text in texts:
        # 检查是否有明显的语义问题
        if text.count('？') > 1 or text.count('！') > 2:
            incomplete_count += 1
    
    print(f"质量报告:")
    print(f"  重复率: {duplicate_rate:.2f}%")
    print(f"  平均长度: {avg_length:.1f} 字符")
    print(f"  长度范围: {min_length}-{max_length} 字符")
    print(f"  可能有问题的文本: {incomplete_count} 条")
    
    # 显示一些示例
    print(f"\n文本示例:")
    for i, (text, pinyin) in enumerate(data[:5]):
        print(f"  {i+1}. {text}")
        print(f"     {pinyin}")

def save_data(data, chinese_file, pinyin_file):
    """保存数据到文件"""
    chinese_lines = []
    pinyin_lines = []
    
    for text, pinyin_text in data:
        chinese_lines.append(text)
        pinyin_lines.append(pinyin_text)
    
    with open(chinese_file, "w", encoding="utf-8") as f:
        f.write("\n".join(chinese_lines))
    
    with open(pinyin_file, "w", encoding="utf-8") as f:
        f.write("\n".join(pinyin_lines))
    
    print(f"\n文件保存:")
    print(f"  中文文本: {chinese_file}")
    print(f"  拼音文本: {pinyin_file}")

def main():
    """主程序"""
    print("=" * 60)
    print("改进的文本生成器")
    print("解决语义不通和重复文本问题")
    print("=" * 60)
    
    # 生成1750条数据
    target_samples = 1750
    print(f"目标生成: {target_samples} 条文本")
    
    # 生成数据集
    dataset = generate_rich_dataset(target_samples)
    
    # 验证质量
    validate_text_quality(dataset)
    
    # 保存到文件
    save_data(dataset, "sentences_improved.txt", "pinyin_improved.txt")
    
    print(f"\n🎉 生成完成！")
    print(f"实际生成: {len(dataset)} 条数据")
    print("=" * 60)

if __name__ == "__main__":
    main()
