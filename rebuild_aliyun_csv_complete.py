#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云TTS完整CSV记录重建工具
根据已生成的音频文件，重新生成完整的CSV记录
使用与原始代码相同的文本选择逻辑，但通过固定种子确保结果一致
"""

import os
import glob
import csv
import re
import random
from pathlib import Path

# 配置信息
CONFIG = {
    # 文件配置
    "TEXT_FILE": "shuffled_text_aliyun.txt",
    "PINYIN_FILE": "shuffled_text_aliyun_pinyin.txt",
    "AUDIO_DIR": "audio_output_aliyun_multi",  # 音频文件目录
    "OUTPUT_CSV": "aliyun_complete_records.csv",  # 输出的完整CSV文件
    
    # 生成配置（与原始代码保持一致）
    "TARGET_CHARS_PER_VOICE": 21000,
}

# 阿里云音色配置 - 27种音色（与原始代码保持一致）
VOICE_CONFIGS = [
    {"voice_code": "A001", "voice_name": "<PERSON><PERSON>", "name": "小云"},
    {"voice_code": "A002", "voice_name": "<PERSON><PERSON><PERSON>", "name": "小刚"},
    {"voice_code": "A003", "voice_name": "Ruoxi", "name": "若汐"},
    {"voice_code": "A004", "voice_name": "Siqi", "name": "思琪"},
    {"voice_code": "A005", "voice_name": "Sijia", "name": "思佳"},
    {"voice_code": "A006", "voice_name": "Sicheng", "name": "思程"},
    {"voice_code": "A007", "voice_name": "Aiqi", "name": "艾琪"},
    {"voice_code": "A008", "voice_name": "Aijia", "name": "艾佳"},
    {"voice_code": "A009", "voice_name": "Aicheng", "name": "艾程"},
    {"voice_code": "A010", "voice_name": "Aida", "name": "艾达"},
    {"voice_code": "A011", "voice_name": "Ninger", "name": "宁儿"},
    {"voice_code": "A012", "voice_name": "Ruilin", "name": "瑞琳"},
    {"voice_code": "A013", "voice_name": "Siyue", "name": "思悦"},
    {"voice_code": "A014", "voice_name": "Aiya", "name": "艾雅"},
    {"voice_code": "A015", "voice_name": "Aixia", "name": "艾夏"},
    {"voice_code": "A016", "voice_name": "Aimei", "name": "艾美"},
    {"voice_code": "A017", "voice_name": "Aiyu", "name": "艾语"},
    {"voice_code": "A018", "voice_name": "Aiyue", "name": "艾悦"},
    {"voice_code": "A019", "voice_name": "Aijing", "name": "艾静"},
    {"voice_code": "A020", "voice_name": "Xiaomei", "name": "小美"},
    {"voice_code": "A021", "voice_name": "Sijing", "name": "思静"},
    {"voice_code": "A022", "voice_name": "Sitong", "name": "思彤"},
    {"voice_code": "A023", "voice_name": "Xiaobei", "name": "小贝"},
    {"voice_code": "A024", "voice_name": "Aitong", "name": "艾彤"},
    {"voice_code": "A025", "voice_name": "Aiwei", "name": "艾薇"},
    {"voice_code": "A026", "voice_name": "Aibao", "name": "艾宝"},
    {"voice_code": "A027", "voice_name": "Aishuo", "name": "艾硕"},
]

def load_text_and_pinyin_files(config):
    """加载文本和拼音文件"""
    print("=== 加载文本和拼音文件 ===")
    
    # 读取文本文件
    texts = []
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    pinyins = []
    try:
        with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
            pinyins = [line.strip() for line in f if line.strip()]
        print(f"✓ 拼音文件: {len(pinyins)} 行")
    except Exception as e:
        print(f"✗ 读取拼音文件失败: {e}")
        # 如果拼音文件读取失败，创建空的拼音列表
        pinyins = [""] * len(texts)
    
    return texts, pinyins

def select_texts_for_target_chars_with_seed(texts, target_chars, voice_code):
    """
    根据目标字数选择文本，确保文本长度从2-30字符均匀分布
    使用音色代码作为随机种子，确保每次运行结果一致
    
    Args:
        texts: 文本列表
        target_chars: 目标字符数
        voice_code: 音色代码（用作随机种子）
    
    Returns:
        selected_texts: 选中的文本列表
        selected_indices: 选中文本的原始索引
        current_chars: 实际选中的字符数
    """
    # 使用音色代码的哈希值作为随机种子，确保结果一致
    seed_value = hash(voice_code) % (2**32)
    random.seed(seed_value)
    
    # 按文本长度分组
    length_groups = {}
    for i, text in enumerate(texts):
        text_length = len(text)
        # 只考虑长度在2-30之间的文本
        if 2 <= text_length <= 30:
            if text_length not in length_groups:
                length_groups[text_length] = []
            length_groups[text_length].append((i, text))
    
    # 计算每个长度需要的文本数量（均匀分布）
    available_lengths = sorted(length_groups.keys())
    if not available_lengths:
        return [], [], 0
    
    # 估算每个长度大约需要多少个文本
    total_length_categories = len(available_lengths)
    avg_chars_per_category = target_chars // total_length_categories
    
    selected_texts = []
    selected_indices = []
    current_chars = 0
    length_stats = {}
    
    # 为每个长度类别选择文本
    for text_length in available_lengths:
        group = length_groups[text_length]
        
        # 计算这个长度需要多少个文本
        target_count_for_length = max(1, avg_chars_per_category // text_length)
        actual_count = min(target_count_for_length, len(group))
        
        # 随机选择这个长度的文本
        selected_from_group = random.sample(group, actual_count)
        
        for idx, text in selected_from_group:
            if current_chars + text_length <= target_chars:
                selected_texts.append(text)
                selected_indices.append(idx)
                current_chars += text_length
                
                # 统计
                if text_length not in length_stats:
                    length_stats[text_length] = 0
                length_stats[text_length] += 1
            else:
                break
        
        # 如果已经达到目标字数，停止
        if current_chars >= target_chars * 0.95:  # 达到95%就停止
            break
    
    # 如果还没达到目标字数，随机补充一些文本
    if current_chars < target_chars * 0.9:
        remaining_chars = target_chars - current_chars
        all_remaining = []
        
        for length, group in length_groups.items():
            for idx, text in group:
                if idx not in selected_indices and len(text) <= remaining_chars:
                    all_remaining.append((idx, text, len(text)))
        
        # 按长度排序，优先选择能填满剩余空间的文本
        all_remaining.sort(key=lambda x: abs(x[2] - remaining_chars))
        
        for idx, text, text_len in all_remaining:
            if current_chars + text_len <= target_chars:
                selected_texts.append(text)
                selected_indices.append(idx)
                current_chars += text_len
                
                if text_len not in length_stats:
                    length_stats[text_len] = 0
                length_stats[text_len] += 1
                
                if current_chars >= target_chars * 0.95:
                    break
    
    return selected_texts, selected_indices, current_chars

def parse_audio_filename(filename):
    """
    解析音频文件名，提取音色代码和序号
    文件名格式: ScA0010000001.wav
    """
    # 移除扩展名
    name_without_ext = os.path.splitext(filename)[0]
    
    # 使用正则表达式解析
    # 格式: Sc + 音色代码(A001-A027) + 7位序号
    pattern = r'^Sc([A-Z]\d{3})(\d{7})$'
    match = re.match(pattern, name_without_ext)
    
    if match:
        voice_code = match.group(1)
        sequence_num = int(match.group(2))
        return voice_code, sequence_num
    else:
        return None, None

def get_voice_name_by_code(voice_code):
    """根据音色代码获取音色名称"""
    for voice in VOICE_CONFIGS:
        if voice["voice_code"] == voice_code:
            return voice["name"]
    return f"未知音色({voice_code})"

def scan_audio_files(audio_dir):
    """扫描音频目录，获取所有音频文件信息"""
    print(f"=== 扫描音频目录: {audio_dir} ===")
    
    if not os.path.exists(audio_dir):
        print(f"✗ 音频目录不存在: {audio_dir}")
        return {}
    
    # 查找所有.wav文件
    pattern = os.path.join(audio_dir, "Sc*.wav")
    audio_files = glob.glob(pattern)
    
    print(f"找到 {len(audio_files)} 个音频文件")
    
    # 按音色分组
    voice_groups = {}
    for audio_file in audio_files:
        filename = os.path.basename(audio_file)
        voice_code, sequence_num = parse_audio_filename(filename)
        
        if voice_code and sequence_num:
            if voice_code not in voice_groups:
                voice_groups[voice_code] = []
            
            voice_groups[voice_code].append({
                'filename': filename,
                'audio_name': os.path.splitext(filename)[0],
                'voice_code': voice_code,
                'sequence_num': sequence_num,
                'voice_name': get_voice_name_by_code(voice_code),
                'file_path': audio_file
            })
        else:
            print(f"⚠️ 无法解析文件名: {filename}")
    
    # 为每个音色组排序
    for voice_code in voice_groups:
        voice_groups[voice_code].sort(key=lambda x: x['sequence_num'])
    
    # 显示统计信息
    print(f"成功解析 {sum(len(files) for files in voice_groups.values())} 个音频文件")
    print("\n各音色文件数量:")
    for voice_code in sorted(voice_groups.keys()):
        voice_name = get_voice_name_by_code(voice_code)
        count = len(voice_groups[voice_code])
        print(f"  {voice_code} ({voice_name}): {count} 个文件")
    
    return voice_groups

def rebuild_complete_csv_records(voice_groups, texts, pinyins, output_csv, config):
    """重建完整的CSV记录文件"""
    print(f"\n=== 重建完整CSV记录文件: {output_csv} ===")
    
    if not voice_groups:
        print("✗ 没有音频文件信息，无法生成记录")
        return False
    
    try:
        with open(output_csv, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile, delimiter='\t')
            
            # 写入标题行
            writer.writerow(['音频名', '类型', '文本', '注音1'])
            
            total_success = 0
            total_error = 0
            
            # 为每种音色重建记录
            for voice_code in sorted(voice_groups.keys()):
                voice_files = voice_groups[voice_code]
                voice_name = get_voice_name_by_code(voice_code)
                
                print(f"\n处理音色 {voice_code} ({voice_name}): {len(voice_files)} 个文件")
                
                # 使用与原始代码相同的文本选择逻辑，但使用固定种子
                target_chars = config["TARGET_CHARS_PER_VOICE"]
                selected_texts, selected_indices, actual_chars = select_texts_for_target_chars_with_seed(
                    texts, target_chars, voice_code
                )
                
                print(f"  选择了 {len(selected_texts)} 条文本，总字数: {actual_chars}")
                
                success_count = 0
                error_count = 0
                
                # 为这个音色的每个文件匹配文本
                for file_info in voice_files:
                    audio_name = file_info['audio_name']
                    sequence_num = file_info['sequence_num']
                    
                    # 使用序号-1作为在selected_texts中的索引
                    text_index_in_selected = sequence_num - 1
                    
                    if 0 <= text_index_in_selected < len(selected_texts):
                        # 获取对应的文本和原始索引
                        text = selected_texts[text_index_in_selected]
                        original_text_index = selected_indices[text_index_in_selected]
                        pinyin = pinyins[original_text_index] if original_text_index < len(pinyins) else ""
                        
                        # 写入记录
                        writer.writerow([audio_name, 'c', text, pinyin])
                        success_count += 1
                    else:
                        # 如果索引超出范围，使用默认值
                        print(f"    ⚠️ 文件索引超出范围: {audio_name} -> 索引 {text_index_in_selected}")
                        writer.writerow([audio_name, 'c', f"文本缺失(索引{text_index_in_selected})", ""])
                        error_count += 1
                
                print(f"  成功: {success_count}, 错误: {error_count}")
                total_success += success_count
                total_error += error_count
            
            print(f"\n✓ CSV记录重建完成")
            print(f"  总成功记录: {total_success}")
            print(f"  总错误记录: {total_error}")
            print(f"  总计记录: {total_success + total_error}")
            
            return True
            
    except Exception as e:
        print(f"✗ 创建CSV文件失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("阿里云TTS完整CSV记录重建工具")
    print("根据已生成的音频文件，重新生成完整的CSV记录")
    print("=" * 60)
    
    config = CONFIG
    
    print("配置信息:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  拼音文件: {config['PINYIN_FILE']}")
    print(f"  音频目录: {config['AUDIO_DIR']}")
    print(f"  输出CSV: {config['OUTPUT_CSV']}")
    print(f"  目标字数: {config['TARGET_CHARS_PER_VOICE']} 字/音色")
    print("=" * 60)
    
    # 1. 加载文本和拼音文件
    texts, pinyins = load_text_and_pinyin_files(config)
    if texts is None:
        print("✗ 无法加载文本文件，程序退出")
        return
    
    # 2. 扫描音频文件
    voice_groups = scan_audio_files(config["AUDIO_DIR"])
    if not voice_groups:
        print("✗ 没有找到音频文件，程序退出")
        return
    
    # 3. 重建完整CSV记录
    success = rebuild_complete_csv_records(voice_groups, texts, pinyins, config["OUTPUT_CSV"], config)
    
    if success:
        print(f"\n🎉 完整记录重建完成！")
        print(f"新的CSV文件: {config['OUTPUT_CSV']}")
        print(f"包含所有已生成音频文件的完整记录")
        print(f"使用固定种子确保文本选择结果一致")
    else:
        print(f"\n❌ 记录重建失败")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
