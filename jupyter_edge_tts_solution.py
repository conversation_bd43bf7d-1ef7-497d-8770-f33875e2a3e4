# ============================================================================
# Jupyter Notebook Edge TTS 完整解决方案
# 直接复制到Jupyter Notebook中运行，替代豆包TTS
# ============================================================================

# 第一步：安装依赖（在Jupyter中运行一次即可）
# !pip install edge-tts pandas nest-asyncio

# 第二步：导入库和设置
import asyncio
import json
import logging
import uuid
import os
import time
import pandas as pd
from pathlib import Path
import nest_asyncio

# 允许在Jupyter中运行异步代码
nest_asyncio.apply()

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print("✅ 环境设置完成")

# 第三步：Edge TTS核心类
class EdgeTTSGenerator:
    """Edge TTS生成器 - 完全替代豆包TTS"""
    
    def __init__(self, output_dir="audio_output_edge"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 中文音色配置（对应豆包的D001-D050格式）
        self.voices = {
            "D001": {"voice": "zh-CN-XiaoxiaoNeural", "name": "晓晓", "gender": "女"},
            "D002": {"voice": "zh-CN-YunxiNeural", "name": "云希", "gender": "男"},
            "D003": {"voice": "zh-CN-XiaoyiNeural", "name": "晓伊", "gender": "女"},
            "D004": {"voice": "zh-CN-YunyangNeural", "name": "云扬", "gender": "男"},
            "D005": {"voice": "zh-CN-XiaohanNeural", "name": "晓涵", "gender": "女"},
            "D006": {"voice": "zh-CN-XiaomengNeural", "name": "晓梦", "gender": "女"},
            "D007": {"voice": "zh-CN-XiaomoNeural", "name": "晓墨", "gender": "女"},
            "D008": {"voice": "zh-CN-XiaoqiuNeural", "name": "晓秋", "gender": "女"},
            "D009": {"voice": "zh-CN-XiaoruiNeural", "name": "晓睿", "gender": "女"},
            "D010": {"voice": "zh-CN-XiaoshuangNeural", "name": "晓双", "gender": "女"},
            "D011": {"voice": "zh-CN-YunjianNeural", "name": "云健", "gender": "男"},
            "D012": {"voice": "zh-CN-XiaochenNeural", "name": "晓辰", "gender": "女"},
            "D013": {"voice": "zh-CN-XiaoyanNeural", "name": "晓颜", "gender": "女"},
            "D014": {"voice": "zh-CN-XiaozhenNeural", "name": "晓甄", "gender": "女"},
            "D015": {"voice": "zh-CN-YunfengNeural", "name": "云枫", "gender": "男"},
        }
        
        self.success_count = 0
        self.error_count = 0
    
    def list_voices(self):
        """显示可用音色"""
        print("可用音色列表:")
        for code, info in self.voices.items():
            print(f"  {code}: {info['name']}({info['gender']}) - {info['voice']}")
    
    async def synthesize_single(self, text, voice_code, speed=1.0, volume=1.0):
        """合成单个文本"""
        try:
            import edge_tts
            
            # 获取音色信息
            voice_info = self.voices.get(voice_code, self.voices["D001"])
            edge_voice = voice_info["voice"]
            
            # 构建SSML参数
            rate = f"{(speed - 1) * 100:+.0f}%" if speed != 1.0 else "+0%"
            volume_param = f"{(volume - 1) * 100:+.0f}%" if volume != 1.0 else "+0%"
            
            # 创建通信对象
            communicate = edge_tts.Communicate(
                text=text,
                voice=edge_voice,
                rate=rate,
                volume=volume_param
            )
            
            # 生成音频数据
            audio_data = b""
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_data += chunk["data"]
            
            if not audio_data:
                raise RuntimeError("未生成音频数据")
            
            self.success_count += 1
            return audio_data
            
        except ImportError:
            logger.error("Edge TTS未安装，请运行: !pip install edge-tts")
            self.error_count += 1
            return None
        except Exception as e:
            logger.error(f"Edge TTS合成失败: {e}")
            self.error_count += 1
            return None
    
    async def generate_voice_batch(self, voice_code, texts, speed=1.0, confirm=True):
        """生成单个音色的批量音频"""
        voice_info = self.voices.get(voice_code, self.voices["D001"])
        voice_name = voice_info["name"]
        
        print(f"\n=== 音色: {voice_name} ({voice_code}) ===")
        print(f"文本数量: {len(texts)}")
        print(f"语速: {speed}x")
        
        # 确认生成
        if confirm:
            response = input(f"是否生成 {voice_name}？(y/n): ").strip().lower()
            if response not in ['y', 'yes', '是']:
                print(f"跳过 {voice_name}")
                return 0, 0
        
        success_count = 0
        error_count = 0
        records = []
        
        start_time = time.time()
        
        for i, text in enumerate(texts, 1):
            # 文件名格式：ScD0010000001.wav
            filename = f"Sc{voice_code}{i:07d}.wav"
            filepath = self.output_dir / filename
            
            print(f"[{i}/{len(texts)}] {text[:30]}{'...' if len(text) > 30 else ''}")
            
            # 检查文件是否已存在
            if filepath.exists():
                print(f"  跳过已存在文件")
                success_count += 1
                continue
            
            # 合成音频
            audio_data = await self.synthesize_single(text, voice_code, speed)
            
            if audio_data:
                try:
                    # 保存音频文件
                    with open(filepath, "wb") as f:
                        f.write(audio_data)
                    
                    print(f"  ✅ 成功: {len(audio_data)} 字节")
                    success_count += 1
                    
                    # 记录到CSV
                    records.append({
                        "音频名": filename.replace('.wav', ''),
                        "类型": "c",
                        "文本": text,
                        "注音": "",
                        "音色": voice_name
                    })
                    
                except Exception as e:
                    print(f"  ❌ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ❌ 合成失败")
                error_count += 1
            
            # 请求间隔
            await asyncio.sleep(0.5)
            
            # 每10个显示进度
            if i % 10 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(texts) - i) * avg_time
                print(f"  进度: {i}/{len(texts)}, 预计剩余: {remaining/60:.1f}分钟")
        
        # 保存CSV记录
        if records:
            csv_path = self.output_dir / f"edge_tts_log_{voice_code}.csv"
            df = pd.DataFrame(records)
            df.to_csv(csv_path, index=False, encoding='utf-8-sig', sep='\t')
            print(f"✅ CSV保存: {csv_path}")
        
        elapsed_time = time.time() - start_time
        print(f"\n{voice_name} 完成:")
        print(f"  耗时: {elapsed_time/60:.1f}分钟")
        print(f"  成功: {success_count}")
        print(f"  失败: {error_count}")
        print(f"  成功率: {(success_count/(success_count+error_count)*100):.1f}%" if success_count+error_count > 0 else "0%")
        
        return success_count, error_count
    
    def load_texts(self, file_path, max_count=1000):
        """加载文本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                texts = [line.strip() for line in f if line.strip()][:max_count]
            print(f"✅ 加载文本: {len(texts)} 条")
            return texts
        except Exception as e:
            print(f"❌ 加载文本失败: {e}")
            return []
    
    def select_texts_by_length(self, texts, target_count=100):
        """按长度均匀选择文本"""
        if len(texts) <= target_count:
            return texts
        
        # 按长度分组
        length_groups = {}
        for text in texts:
            length = len(text)
            if length not in length_groups:
                length_groups[length] = []
            length_groups[length].append(text)
        
        # 均匀选择
        selected = []
        lengths = sorted(length_groups.keys())
        per_length = max(1, target_count // len(lengths))
        
        for length in lengths:
            group_texts = length_groups[length][:per_length]
            selected.extend(group_texts)
            if len(selected) >= target_count:
                break
        
        return selected[:target_count]
    
    def get_stats(self):
        """获取统计信息"""
        total = self.success_count + self.error_count
        success_rate = (self.success_count / total * 100) if total > 0 else 0
        return {
            "success": self.success_count,
            "error": self.error_count,
            "total": total,
            "success_rate": success_rate
        }

print("✅ Edge TTS生成器类定义完成")

# 第四步：创建生成器实例
tts_generator = EdgeTTSGenerator()

# 显示可用音色
tts_generator.list_voices()

print("\n✅ Edge TTS解决方案准备完成！")
print("\n使用方法:")
print("1. 快速测试: await quick_test()")
print("2. 单个音色: await single_voice_demo()")
print("3. 多个音色: await multi_voice_demo()")
print("4. 自定义生成: 使用 tts_generator 对象")

# 第五步：使用示例函数
async def quick_test():
    """快速测试Edge TTS"""
    print("=== Edge TTS快速测试 ===")
    
    test_texts = ["你好", "测试", "中国", "北京", "上海"]
    
    success, error = await tts_generator.generate_voice_batch(
        voice_code="D001",
        texts=test_texts,
        speed=1.0,
        confirm=False
    )
    
    print(f"✅ 快速测试完成: 成功 {success}, 失败 {error}")

async def single_voice_demo():
    """单个音色演示"""
    print("=== 单个音色演示 ===")
    
    # 示例文本
    demo_texts = [
        "你好世界",
        "这是一个测试",
        "Edge TTS语音合成",
        "中文语音效果很好",
        "支持多种音色选择"
    ]
    
    # 选择音色
    voice_code = "D002"  # 云希(男)
    speed = 1.2  # 1.2倍速
    
    success, error = await tts_generator.generate_voice_batch(
        voice_code=voice_code,
        texts=demo_texts,
        speed=speed,
        confirm=False
    )
    
    print(f"✅ 单个音色演示完成: 成功 {success}, 失败 {error}")

async def multi_voice_demo():
    """多个音色演示"""
    print("=== 多个音色演示 ===")
    
    # 从文件加载文本（如果存在）
    text_file = "../shuffled_from_rank_random.txt"
    if os.path.exists(text_file):
        all_texts = tts_generator.load_texts(text_file, max_count=500)
        selected_texts = tts_generator.select_texts_by_length(all_texts, 20)
    else:
        # 使用示例文本
        selected_texts = [
            "你好", "测试", "中国", "北京", "上海", "广州", "深圳",
            "杭州", "成都", "西安", "南京", "武汉", "天津", "重庆",
            "苏州", "长沙", "青岛", "大连", "厦门", "宁波"
        ]
    
    print(f"使用文本: {len(selected_texts)} 条")
    
    # 选择音色
    voice_codes = ["D001", "D002", "D003"]  # 3种音色
    speed = 1.0
    
    total_success = 0
    total_error = 0
    
    for i, voice_code in enumerate(voice_codes, 1):
        print(f"\n进度: {i}/{len(voice_codes)}")
        
        success, error = await tts_generator.generate_voice_batch(
            voice_code=voice_code,
            texts=selected_texts,
            speed=speed,
            confirm=True  # 每个音色确认
        )
        
        total_success += success
        total_error += error
        
        # 询问是否继续
        if i < len(voice_codes):
            response = input("继续下一个音色？(y/n): ").strip().lower()
            if response not in ['y', 'yes', '是']:
                break
    
    # 最终统计
    stats = tts_generator.get_stats()
    print(f"\n=== 多音色演示完成 ===")
    print(f"总成功: {total_success}")
    print(f"总失败: {total_error}")
    print(f"成功率: {stats['success_rate']:.1f}%")
    print(f"输出目录: {tts_generator.output_dir}")

print("\n🎯 现在可以运行以下命令:")
print("await quick_test()        # 快速测试")
print("await single_voice_demo() # 单个音色演示")
print("await multi_voice_demo()  # 多个音色演示")
