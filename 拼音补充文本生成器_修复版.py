#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拼音补充文本生成器 - 修复版
从内容文件中提取包含缺失拼音的句子
解决逗号分隔格式和儿化音匹配问题
"""

import os
import random
import re
from collections import defaultdict, Counter

# ================= 配置区域 =================
TRAIN_CONTENT_PATH = "train_content.txt"
TEST_CONTENT_PATH = "test_content.txt"
MISSING_PINYIN_PATHS = [
    "aliyun_missing.txt",
    "baiduyun_missing.txt",
    "doubao_missing.txt"
]
OUTPUT_PATHS = [
    "supplement_aliyun.txt",
    "supplement_baiduyun.txt",
    "supplement_doubao.txt"
]
PINYIN_OUTPUT_PATHS = [
    "supplement_aliyun_pinyin.txt",
    "supplement_baiduyun_pinyin.txt",
    "supplement_doubao_pinyin.txt"
]
# ===========================================

def normalize_pinyin(pinyin):
    """标准化拼音格式，处理儿化音等特殊情况"""
    pinyin = pinyin.lower().strip()
    
    # 处理儿化音：移除r后缀
    if pinyin.endswith('r') and len(pinyin) > 1:
        # 检查是否是真正的儿化音（不是er本身）
        if pinyin != 'er' and not pinyin.endswith('er'):
            base_pinyin = pinyin[:-1]
            return base_pinyin
    
    return pinyin

def load_missing_pinyins(file_path):
    """加载缺失拼音列表（支持逗号分隔格式）"""
    missing_pinyins = set()
    normalized_pinyins = set()
    
    if not os.path.exists(file_path):
        print(f"✗ 缺失拼音文件不存在: {file_path}")
        return missing_pinyins, normalized_pinyins
    
    print(f"正在加载缺失拼音文件: {file_path}")
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            
            # 处理逗号分隔的格式
            if ',' in content:
                # 按逗号分割
                pinyins = [p.strip().lower() for p in content.split(',')]
            else:
                # 按行分割
                pinyins = [line.strip().lower() for line in content.split('\n')]
            
            # 过滤空字符串和注释
            for pinyin in pinyins:
                if pinyin and not pinyin.startswith('#'):
                    missing_pinyins.add(pinyin)
                    # 同时添加标准化版本
                    normalized = normalize_pinyin(pinyin)
                    normalized_pinyins.add(normalized)
        
        print(f"✓ 加载完成: {len(missing_pinyins)} 个缺失拼音")
        print(f"  原始拼音示例: {', '.join(list(missing_pinyins)[:5])}...")
        print(f"  标准化后: {len(normalized_pinyins)} 个唯一拼音")
        print(f"  标准化示例: {', '.join(list(normalized_pinyins)[:5])}...")
    except Exception as e:
        print(f"加载缺失拼音文件失败: {e}")
    
    return missing_pinyins, normalized_pinyins

def parse_content_line(line):
    """
    解析内容行，返回(汉字列表, 原始拼音列表, 标准化拼音列表)
    格式: SSB00050001.wav	广 guang3 州 zhou1 女 nv3 ...
    """
    tokens = line.strip().split()
    if len(tokens) < 3 or len(tokens) % 2 != 1:
        return None, None, None
    
    chars = []
    raw_pinyins = []  # 带声调拼音
    normalized_pinyins = []  # 标准化拼音
    
    for i in range(1, len(tokens), 2):
        if i + 1 >= len(tokens):
            break
            
        char = tokens[i]
        raw_pinyin = tokens[i+1].lower()
        normalized = normalize_pinyin(raw_pinyin)
        
        chars.append(char)
        raw_pinyins.append(raw_pinyin)
        normalized_pinyins.append(normalized)
    
    return chars, raw_pinyins, normalized_pinyins

def load_content_sentences(file_paths):
    """加载所有内容句子"""
    all_sentences = []
    total_processed = 0
    
    for file_path in file_paths:
        if not os.path.exists(file_path):
            print(f"✗ 文件不存在: {file_path}")
            continue
            
        print(f"正在处理内容文件: {file_path}")
        file_sentences = 0
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    total_processed += 1
                    result = parse_content_line(line)
                    if not result or not result[0]:
                        continue
                    
                    chars, raw_pinyins, normalized_pinyins = result
                    sentence_text = ''.join(chars)
                    all_sentences.append((sentence_text, raw_pinyins, normalized_pinyins, chars))
                    file_sentences += 1
                    
                    # 显示进度
                    if line_num % 10000 == 0:
                        print(f"  已处理 {line_num} 行，有效句子 {file_sentences} 个")
            
            print(f"  文件完成: 处理 {total_processed} 行，有效句子 {file_sentences} 个")
        except Exception as e:
            print(f"处理内容文件失败: {e}")
    
    print(f"✓ 总计加载: {len(all_sentences)} 个有效句子")
    return all_sentences

def find_matching_sentences(sentences, missing_pinyins, normalized_missing):
    """找出包含缺失拼音的句子（支持原始和标准化匹配）"""
    matched_sentences = []
    pinyin_coverage = defaultdict(int)
    normalized_coverage = defaultdict(int)
    
    print(f"开始匹配，目标拼音数: {len(missing_pinyins)} 个")
    
    for i, sentence in enumerate(sentences):
        _, raw_pinyins, normalized_pinyins, _ = sentence
        
        # 检查原始拼音匹配
        raw_found = any(pinyin in missing_pinyins for pinyin in raw_pinyins)
        # 检查标准化拼音匹配
        normalized_found = any(pinyin in normalized_missing for pinyin in normalized_pinyins)
        
        if raw_found or normalized_found:
            matched_sentences.append(sentence)
            
            # 统计覆盖情况
            for pinyin in raw_pinyins:
                if pinyin in missing_pinyins:
                    pinyin_coverage[pinyin] += 1
            
            for pinyin in normalized_pinyins:
                if pinyin in normalized_missing:
                    normalized_coverage[pinyin] += 1
        
        # 显示进度
        if (i + 1) % 50000 == 0:
            print(f"  已检查 {i + 1}/{len(sentences)} 个句子，匹配 {len(matched_sentences)} 个")
    
    # 统计信息
    total_missing = len(missing_pinyins)
    raw_covered = len(pinyin_coverage)
    normalized_covered = len(normalized_coverage)
    
    print(f"  匹配结果: {len(matched_sentences)} 个句子包含缺失拼音")
    print(f"  原始拼音覆盖率: {raw_covered}/{total_missing} ({raw_covered/total_missing*100:.1f}%)")
    print(f"  标准化拼音覆盖率: {normalized_covered}/{len(normalized_missing)} ({normalized_covered/len(normalized_missing)*100:.1f}%)")
    
    # 显示未覆盖的拼音
    if raw_covered < total_missing:
        uncovered = missing_pinyins - set(pinyin_coverage.keys())
        print(f"  未覆盖原始拼音示例: {', '.join(list(uncovered)[:10])}...")
    
    if normalized_covered < len(normalized_missing):
        uncovered_norm = normalized_missing - set(normalized_coverage.keys())
        print(f"  未覆盖标准化拼音示例: {', '.join(list(uncovered_norm)[:10])}...")
    
    return matched_sentences

def shuffle_sentence(chars, pinyins):
    """打乱句子顺序"""
    indices = list(range(len(chars)))
    random.shuffle(indices)
    return [chars[i] for i in indices], [pinyins[i] for i in indices]

def generate_output(matched_sentences, max_sentences=10000):
    """生成输出内容"""
    print(f"生成输出，最大句子数: {max_sentences}")
    
    if len(matched_sentences) > max_sentences:
        matched_sentences = random.sample(matched_sentences, max_sentences)
        print(f"  从 {len(matched_sentences)} 个匹配句子中随机选择 {max_sentences} 个")
    
    text_lines = []
    pinyin_lines = []
    
    for sentence in matched_sentences:
        _, raw_pinyins, _, chars = sentence
        
        # 使用原始拼音（保持声调）
        shuffled_chars, shuffled_pinyins = shuffle_sentence(chars, raw_pinyins)
        
        text_lines.append(''.join(shuffled_chars))
        pinyin_lines.append(' '.join(shuffled_pinyins))
    
    return text_lines, pinyin_lines

def save_output(texts, pinyins, text_path, pinyin_path):
    """保存输出文件"""
    try:
        with open(text_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(texts))
        with open(pinyin_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(pinyins))
        print(f"✓ 保存成功: {text_path} 和 {pinyin_path}")
        
        # 统计信息
        if texts:
            lengths = [len(t) for t in texts]
            print(f"  生成句子数: {len(texts)}")
            print(f"  平均长度: {sum(lengths)/len(lengths):.1f} 字符")
            print(f"  长度范围: {min(lengths)}-{max(lengths)} 字符")
            print(f"  长度分布: {dict(Counter(lengths).most_common(5))}")
    except Exception as e:
        print(f"保存文件失败: {e}")

def main():
    print("="*60)
    print("拼音补充文本生成器 - 修复版")
    print("支持逗号分隔格式和儿化音处理")
    print("="*60)
    
    # 加载所有内容句子
    print("第一步: 加载训练和测试数据...")
    content_sentences = load_content_sentences([TRAIN_CONTENT_PATH, TEST_CONTENT_PATH])
    if not content_sentences:
        print("✗ 没有加载到任何句子")
        return
    
    # 处理每个缺失拼音文件
    for i, missing_path in enumerate(MISSING_PINYIN_PATHS):
        print("\n" + "="*60)
        print(f"第{i+2}步: 处理缺失拼音源 {i+1}: {missing_path}")
        print("="*60)
        
        # 加载缺失拼音
        missing_pinyins, normalized_missing = load_missing_pinyins(missing_path)
        if not missing_pinyins:
            continue
        
        # 查找匹配的句子
        matched = find_matching_sentences(content_sentences, missing_pinyins, normalized_missing)
        if not matched:
            print("✗ 没有找到匹配的句子")
            continue
        
        # 生成输出内容
        texts, pinyins = generate_output(matched)
        
        # 保存结果
        if i < len(OUTPUT_PATHS) and i < len(PINYIN_OUTPUT_PATHS):
            save_output(texts, pinyins, OUTPUT_PATHS[i], PINYIN_OUTPUT_PATHS[i])
    
    print("\n" + "="*60)
    print("🎉 处理完成!")
    print("="*60)

if __name__ == "__main__":
    main()
