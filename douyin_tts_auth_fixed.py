# ============================================================================
# 豆包TTS认证修复版本
# 修复HTTP API 401认证错误
# 复制此代码到您的TTS.ipynb中替换现有代码
# ============================================================================

"""
豆包语音合成大模型 - 认证修复版
修复HTTP API 401认证错误
"""

import asyncio
import json
import logging
import uuid
import os
import time
import glob
import requests
import base64
import hashlib
import hmac
from datetime import datetime
from urllib.parse import urlencode

# Jupyter异步支持
import nest_asyncio
nest_asyncio.apply()

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print("✅ 环境设置完成 - 认证修复版")

# ==================== 配置区域 ====================
DOUYIN_CONFIG = {
    # API配置
    "APPID": "9862368305",
    "ACCESS_TOKEN": "bbxNaAzRzBSkySjouDItXl6tBUUyNd2W",
    
    # 尝试不同的API端点
    "HTTP_ENDPOINTS": [
        "https://openspeech.bytedance.com/api/v1/tts",
        "https://openspeech.bytedance.com/api/v1/tts/submit",
        "https://openspeech.bytedance.com/api/v1/tts/synthesis",
    ],
    
    # 音频格式配置
    "ENCODING": "wav",
    
    # 文件配置
    "TEXT_FILE": "shuffled_from_rank_random.txt",
    "PINYIN_FILE": "shuffled_from_rank_random_pinyin.txt",
    "OUTPUT_DIR": "audio_output_auth_fixed",
    "CSV_FILE": "douyin_auth_fixed_log.csv",
    "REQUEST_INTERVAL": 2.0,
    
    # 生成配置
    "TARGET_CHARS_PER_VOICE": 100,  # 先测试少量
}

# 音色配置 - 只测试一种
VOICE_CONFIGS = [
    {"voice_type": "BV001_streaming", "name": "通用女声", "code": "001"},
]

print("✅ 配置加载完成")

def get_cluster(voice: str) -> str:
    """获取集群名称"""
    if voice.startswith("S_"):
        return "volcano_icl"
    return "volcano_tts"

class DouyinTTSAuthFixed:
    """豆包TTS认证修复版"""
    
    def __init__(self, config):
        self.config = config
        self.success_count = 0
        self.error_count = 0
    
    def get_auth_headers_v1(self):
        """认证方式1: Bearer;token"""
        return {
            "Authorization": f"Bearer;{self.config['ACCESS_TOKEN']}",
            "Content-Type": "application/json"
        }
    
    def get_auth_headers_v2(self):
        """认证方式2: Bearer token"""
        return {
            "Authorization": f"Bearer {self.config['ACCESS_TOKEN']}",
            "Content-Type": "application/json"
        }
    
    def get_auth_headers_v3(self):
        """认证方式3: 直接token"""
        return {
            "Authorization": self.config['ACCESS_TOKEN'],
            "Content-Type": "application/json"
        }
    
    def get_auth_headers_v4(self):
        """认证方式4: X-Token"""
        return {
            "X-Token": self.config['ACCESS_TOKEN'],
            "Content-Type": "application/json"
        }
    
    def test_auth_methods(self, text, voice_type):
        """测试不同的认证方式"""
        auth_methods = [
            ("Bearer;token", self.get_auth_headers_v1),
            ("Bearer token", self.get_auth_headers_v2),
            ("Direct token", self.get_auth_headers_v3),
            ("X-Token", self.get_auth_headers_v4),
        ]
        
        # 确定集群
        cluster = get_cluster(voice_type)
        
        # 准备请求数据
        request_data = {
            "app": {
                "appid": self.config["APPID"],
                "token": self.config["ACCESS_TOKEN"],
                "cluster": cluster,
            },
            "user": {
                "uid": str(uuid.uuid4()),
            },
            "audio": {
                "voice_type": voice_type,
                "encoding": self.config["ENCODING"],
            },
            "request": {
                "reqid": str(uuid.uuid4()),
                "text": text,
                "operation": "submit",
            },
        }
        
        print(f"🔐 测试不同认证方式...")
        
        for endpoint in self.config["HTTP_ENDPOINTS"]:
            print(f"\n📍 测试端点: {endpoint}")
            
            for auth_name, auth_func in auth_methods:
                print(f"  🔑 测试认证: {auth_name}")
                
                headers = auth_func()
                
                try:
                    response = requests.post(
                        endpoint,
                        headers=headers,
                        json=request_data,
                        timeout=10
                    )
                    
                    print(f"    📥 响应: {response.status_code}")
                    
                    if response.status_code == 200:
                        print(f"    ✅ 认证成功！")
                        print(f"    📄 响应头: {dict(response.headers)}")
                        
                        # 检查响应内容
                        content_type = response.headers.get('content-type', '')
                        print(f"    📋 内容类型: {content_type}")
                        
                        if 'audio' in content_type or 'wav' in content_type:
                            audio_data = response.content
                            print(f"    🎵 直接音频: {len(audio_data)} 字节")
                            return audio_data, endpoint, headers
                        else:
                            try:
                                json_response = response.json()
                                print(f"    📄 JSON响应: {json.dumps(json_response, indent=2, ensure_ascii=False)}")
                                
                                # 查找音频数据
                                if 'data' in json_response:
                                    if isinstance(json_response['data'], str):
                                        # Base64编码的音频
                                        audio_data = base64.b64decode(json_response['data'])
                                        print(f"    🎵 Base64音频: {len(audio_data)} 字节")
                                        return audio_data, endpoint, headers
                                    elif isinstance(json_response['data'], dict) and 'audio' in json_response['data']:
                                        audio_data = base64.b64decode(json_response['data']['audio'])
                                        print(f"    🎵 嵌套音频: {len(audio_data)} 字节")
                                        return audio_data, endpoint, headers
                                
                                print(f"    ⚠️ JSON中未找到音频数据")
                                
                            except json.JSONDecodeError:
                                print(f"    ❌ 无法解析JSON")
                                print(f"    📄 原始响应: {response.text[:200]}...")
                    
                    elif response.status_code == 401:
                        print(f"    ❌ 认证失败: {response.text}")
                    elif response.status_code == 403:
                        print(f"    ❌ 权限不足: {response.text}")
                    elif response.status_code == 404:
                        print(f"    ❌ 端点不存在: {response.text}")
                    else:
                        print(f"    ❌ 其他错误: {response.text}")
                
                except requests.exceptions.Timeout:
                    print(f"    ⏰ 请求超时")
                except requests.exceptions.RequestException as e:
                    print(f"    ❌ 请求异常: {e}")
                
                # 短暂延迟
                time.sleep(1)
        
        print(f"❌ 所有认证方式都失败")
        return None, None, None

def load_text_files(config):
    """加载文本文件"""
    print("=== 加载文本文件 ===")
    
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
        return texts
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return []

async def test_auth_fix():
    """测试认证修复"""
    print("=" * 60)
    print("豆包TTS认证修复测试")
    print("=" * 60)
    
    config = DOUYIN_CONFIG
    
    # 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)
    
    # 加载文本
    texts = load_text_files(config)
    if not texts:
        print("❌ 没有可用文本")
        return
    
    # 选择测试文本和音色
    test_text = texts[0]
    voice_config = VOICE_CONFIGS[0]
    
    print(f"测试配置:")
    print(f"  文本: '{test_text}'")
    print(f"  音色: {voice_config['name']} ({voice_config['voice_type']})")
    print(f"  APPID: {config['APPID']}")
    print(f"  TOKEN: {config['ACCESS_TOKEN'][:20]}...")
    
    # 创建TTS对象
    tts = DouyinTTSAuthFixed(config)
    
    # 测试认证
    print(f"\n🔐 开始测试认证方式...")
    audio_data, working_endpoint, working_headers = tts.test_auth_methods(
        test_text, voice_config["voice_type"]
    )
    
    if audio_data:
        # 保存测试文件
        test_file = os.path.join(config["OUTPUT_DIR"], "auth_test_001.wav")
        with open(test_file, "wb") as f:
            f.write(audio_data)
        
        print(f"\n🎉 认证修复成功！")
        print(f"✅ 工作的端点: {working_endpoint}")
        print(f"✅ 工作的认证头: {working_headers}")
        print(f"✅ 测试文件保存: {test_file}")
        print(f"✅ 音频大小: {len(audio_data)} 字节")
        
        return True, working_endpoint, working_headers
    else:
        print(f"\n❌ 认证修复失败")
        print(f"可能的原因:")
        print(f"1. API密钥无效或过期")
        print(f"2. 账户没有TTS服务权限")
        print(f"3. 账户余额不足")
        print(f"4. API端点已变更")
        print(f"5. 需要联系豆包技术支持")
        
        return False, None, None

async def main():
    """主函数 - 认证修复版"""
    print("🔐 豆包TTS认证修复程序")
    print("此程序将测试不同的认证方式和API端点")
    
    # 测试认证修复
    success, working_endpoint, working_headers = await test_auth_fix()
    
    if success:
        print(f"\n🎯 后续建议:")
        print(f"1. 使用工作的端点: {working_endpoint}")
        print(f"2. 使用工作的认证头: {working_headers}")
        print(f"3. 可以继续批量生成音频")
    else:
        print(f"\n🔧 故障排除建议:")
        print(f"1. 检查豆包控制台中的API密钥状态")
        print(f"2. 确认账户是否有TTS服务权限")
        print(f"3. 检查账户余额和配额")
        print(f"4. 联系豆包技术支持获取最新API文档")
        print(f"5. 考虑使用其他TTS服务作为备选方案")

print("✅ 认证修复程序准备完成")

# 运行认证修复程序
print("\n🚀 开始运行豆包TTS认证修复程序...")
await main()

print("\n📋 认证修复测试完成！")
print("请根据上面的结果进行后续处理。")
