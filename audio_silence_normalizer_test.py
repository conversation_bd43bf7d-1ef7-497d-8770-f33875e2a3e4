#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频静音时间标准化测试工具
处理少量文件进行功能验证
"""

import os
import wave
import numpy as np
from pathlib import Path
import pandas as pd
from typing import Dict, List, Tuple, Optional
import warnings
import shutil
from datetime import datetime

warnings.filterwarnings('ignore')

# ==================== 测试配置参数 ====================
TEST_CONFIG = {
    # 静音检测参数
    "SILENCE_THRESHOLD": 0.01,  # 静音阈值（相对于最大振幅）
    "MIN_SILENCE_DURATION": 0.01,  # 最小静音持续时间（秒）
    
    # 目标静音时间范围
    "TARGET_SILENCE_MIN": 0.15,  # 150ms
    "TARGET_SILENCE_MAX": 0.25,  # 250ms
    "TARGET_SILENCE_DEFAULT": 0.2,  # 默认200ms
    
    # 处理参数
    "BACKUP_ORIGINAL": True,  # 是否备份原文件
    "TEST_COUNT": 5,  # 测试文件数量
}

class AudioSilenceNormalizerTest:
    """音频静音时间标准化测试器"""
    
    def __init__(self, config=None):
        self.config = config or TEST_CONFIG
        self.processed_count = 0
        self.error_count = 0
        self.backup_dir = None
        
    def detect_silence_boundaries(self, audio_data: np.ndarray, sample_rate: int) -> Tuple[int, int]:
        """检测音频开头和结尾的静音边界"""
        if len(audio_data) == 0:
            return 0, 0
        
        # 计算静音阈值
        max_amplitude = np.max(np.abs(audio_data))
        silence_threshold = max_amplitude * self.config["SILENCE_THRESHOLD"]
        
        # 找到开头的非静音位置
        start_index = 0
        for i in range(len(audio_data)):
            if np.abs(audio_data[i]) > silence_threshold:
                start_index = i
                break
        
        # 找到结尾的非静音位置
        end_index = len(audio_data) - 1
        for i in range(len(audio_data) - 1, -1, -1):
            if np.abs(audio_data[i]) > silence_threshold:
                end_index = i
                break
        
        return start_index, end_index
    
    def generate_silence(self, duration_seconds: float, sample_rate: int, sample_width: int) -> np.ndarray:
        """生成指定时长的静音数据"""
        silence_samples = int(duration_seconds * sample_rate)
        
        if sample_width == 1:
            return np.zeros(silence_samples, dtype=np.uint8) + 128  # 8位无符号
        elif sample_width == 2:
            return np.zeros(silence_samples, dtype=np.int16)  # 16位有符号
        elif sample_width == 4:
            return np.zeros(silence_samples, dtype=np.int32)  # 32位有符号
        else:
            return np.zeros(silence_samples, dtype=np.float32)  # 浮点
    
    def normalize_audio_silence(self, file_path: str) -> Dict:
        """标准化单个音频文件的静音时间"""
        try:
            # 读取原始音频文件
            with wave.open(file_path, 'rb') as wav_file:
                frames = wav_file.getnframes()
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()
                
                # 读取音频数据
                audio_data = wav_file.readframes(frames)
                
                # 转换为numpy数组
                if sample_width == 1:
                    dtype = np.uint8
                elif sample_width == 2:
                    dtype = np.int16
                elif sample_width == 4:
                    dtype = np.int32
                else:
                    dtype = np.float32
                
                audio_array = np.frombuffer(audio_data, dtype=dtype)
            
            # 检测静音边界
            start_index, end_index = self.detect_silence_boundaries(audio_array, sample_rate)
            
            # 计算原始静音时长
            original_start_silence = start_index / sample_rate
            original_end_silence = (len(audio_array) - 1 - end_index) / sample_rate
            
            # 提取非静音部分
            content_audio = audio_array[start_index:end_index + 1]
            
            # 确定目标静音时长
            target_silence = self.config["TARGET_SILENCE_DEFAULT"]
            
            # 生成标准化的静音
            start_silence = self.generate_silence(target_silence, sample_rate, sample_width)
            end_silence = self.generate_silence(target_silence, sample_rate, sample_width)
            
            # 合并音频：开头静音 + 内容 + 结尾静音
            normalized_audio = np.concatenate([start_silence, content_audio, end_silence])
            
            # 备份原文件（如果需要）
            if self.config["BACKUP_ORIGINAL"] and self.backup_dir:
                backup_path = os.path.join(self.backup_dir, os.path.basename(file_path))
                shutil.copy2(file_path, backup_path)
            
            # 写入标准化后的音频
            with wave.open(file_path, 'wb') as wav_file:
                wav_file.setnchannels(channels)
                wav_file.setsampwidth(sample_width)
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(normalized_audio.tobytes())
            
            # 计算处理结果
            new_duration = len(normalized_audio) / sample_rate
            original_duration = len(audio_array) / sample_rate
            
            result = {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'status': 'success',
                'original_duration': original_duration,
                'new_duration': new_duration,
                'original_start_silence_ms': original_start_silence * 1000,
                'original_end_silence_ms': original_end_silence * 1000,
                'target_silence_ms': target_silence * 1000,
                'content_duration': len(content_audio) / sample_rate,
                'silence_normalized': True,
                'size_change': len(normalized_audio) - len(audio_array),
                'file_size_kb': os.path.getsize(file_path) / 1024
            }
            
            self.processed_count += 1
            return result
            
        except Exception as e:
            self.error_count += 1
            return {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'status': 'error',
                'error': str(e),
                'silence_normalized': False
            }
    
    def create_backup_directory(self, base_dir: str) -> str:
        """创建备份目录"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = os.path.join(base_dir, f"backup_silence_test_{timestamp}")
        os.makedirs(backup_dir, exist_ok=True)
        return backup_dir
    
    def process_test_files(self, directory: str) -> pd.DataFrame:
        """处理测试文件"""
        # 获取前几个wav文件进行测试
        wav_files = list(Path(directory).glob("Sc*.wav"))
        
        if not wav_files:
            print(f"在目录 {directory} 中未找到wav文件")
            return pd.DataFrame()
        
        # 选择测试文件
        test_files = wav_files[:self.config["TEST_COUNT"]]
        
        print(f"测试处理 {len(test_files)} 个音频文件")
        
        # 创建备份目录
        if self.config["BACKUP_ORIGINAL"]:
            self.backup_dir = self.create_backup_directory(directory)
            print(f"备份目录: {self.backup_dir}")
        
        # 处理文件
        results = []
        
        for i, file_path in enumerate(test_files, 1):
            print(f"\n[{i}/{len(test_files)}] 处理: {file_path.name}")
            result = self.normalize_audio_silence(str(file_path))
            results.append(result)
            
            # 显示处理结果
            if result['status'] == 'success':
                original_start = result['original_start_silence_ms']
                original_end = result['original_end_silence_ms']
                target = result['target_silence_ms']
                print(f"  ✓ 开头静音: {original_start:.0f}ms -> {target:.0f}ms")
                print(f"  ✓ 结尾静音: {original_end:.0f}ms -> {target:.0f}ms")
                print(f"  ✓ 时长变化: {result['original_duration']:.2f}s -> {result['new_duration']:.2f}s")
            else:
                print(f"  ✗ 错误: {result['error']}")
        
        return pd.DataFrame(results)

def print_test_summary(df: pd.DataFrame):
    """打印测试结果摘要"""
    if df.empty:
        print("没有处理结果")
        return
    
    print("\n" + "="*60)
    print("静音标准化测试结果摘要")
    print("="*60)
    
    # 统计结果
    success_df = df[df['status'] == 'success']
    error_df = df[df['status'] == 'error']
    
    print(f"总文件数: {len(df)}")
    print(f"处理成功: {len(success_df)}")
    print(f"处理失败: {len(error_df)}")
    
    if len(success_df) > 0:
        print(f"\n处理效果:")
        print(f"目标静音时长: {success_df['target_silence_ms'].iloc[0]:.0f}ms")
        
        print(f"\n开头静音调整:")
        print(f"  调整前平均: {success_df['original_start_silence_ms'].mean():.1f}ms")
        print(f"  调整后统一: {success_df['target_silence_ms'].iloc[0]:.0f}ms")
        
        print(f"结尾静音调整:")
        print(f"  调整前平均: {success_df['original_end_silence_ms'].mean():.1f}ms")
        print(f"  调整后统一: {success_df['target_silence_ms'].iloc[0]:.0f}ms")
        
        print(f"\n时长变化:")
        print(f"  平均原始时长: {success_df['original_duration'].mean():.2f}秒")
        print(f"  平均新时长: {success_df['new_duration'].mean():.2f}秒")
        print(f"  平均时长变化: {(success_df['new_duration'] - success_df['original_duration']).mean():.2f}秒")
    
    if len(error_df) > 0:
        print(f"\n处理失败的文件:")
        for _, row in error_df.iterrows():
            print(f"  {row['file_name']}: {row['error']}")

def main():
    """主函数"""
    print("=" * 60)
    print("音频静音时间标准化测试工具")
    print("=" * 60)
    
    # 配置参数
    config = TEST_CONFIG.copy()
    
    print("测试配置:")
    print(f"  测试文件数量: {config['TEST_COUNT']} 个")
    print(f"  目标静音时长: {config['TARGET_SILENCE_DEFAULT']*1000:.0f}ms")
    print(f"  是否备份原文件: {config['BACKUP_ORIGINAL']}")
    
    print(f"\n⚠️  注意: 这是测试版本，只会处理前{config['TEST_COUNT']}个文件")
    
    # 确认处理
    try:
        confirm = input(f"是否开始测试处理？(y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("已取消测试")
            return
    except KeyboardInterrupt:
        print("\n程序被中断")
        return
    
    # 创建处理器并开始处理
    normalizer = AudioSilenceNormalizerTest(config)
    
    print(f"\n开始测试处理...")
    start_time = datetime.now()
    
    # 处理文件
    results_df = normalizer.process_test_files(".")
    
    end_time = datetime.now()
    processing_time = (end_time - start_time).total_seconds()
    
    if not results_df.empty:
        # 保存处理结果
        result_file = f"silence_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        results_df.to_csv(result_file, index=False, encoding='utf-8-sig')
        print(f"\n测试结果已保存到: {result_file}")
        
        # 打印摘要
        print_test_summary(results_df)
        
        print(f"\n测试完成!")
        print(f"总耗时: {processing_time:.1f}秒")
        
        # 建议下一步
        success_count = len(results_df[results_df['status'] == 'success'])
        if success_count > 0:
            print(f"\n💡 测试成功！")
            print(f"如果效果满意，可以使用 audio_silence_normalizer.py 处理所有文件")
        else:
            print(f"\n⚠️ 测试失败，请检查配置")
    else:
        print("未找到可处理的音频文件")

if __name__ == "__main__":
    main()
