#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包语音合成大模型WebSocket API调用程序 - Jupyter版本
每种音色生成1小时音频（约24000字）
适配Jupyter Notebook环境
"""

import json
import logging
import uuid
import os
import time
import glob
from datetime import datetime
import asyncio
import nest_asyncio

# 允许在Jupyter中运行asyncio
nest_asyncio.apply()

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ==================== 配置区域 ====================
DOUYIN_CONFIG = {
    # API配置
    "APPID": "9862368305",
    "ACCESS_TOKEN": "bbxNaAzRzBSkySjouDItXl6tBUUyNd2W",
    
    # WebSocket配置
    "ENDPOINT": "wss://openspeech.bytedance.com/api/v1/tts/ws_binary",
    
    # 音频格式配置
    "ENCODING": "wav",
    
    # 文件配置
    "TEXT_FILE": "shuffled_from_rank_modified.txt",
    "PINYIN_FILE": "shuffled_from_rank_modified_pinyin.txt",
    "OUTPUT_DIR": "audio_output_1h",
    "CSV_FILE": "douyin_1h_audio_log.csv",
    "REQUEST_INTERVAL": 1.0,  # WebSocket请求间隔
    
    # 生成配置
    "TARGET_CHARS_PER_VOICE": 24000,  # 每种音色目标字数（约1小时）
}

# 音色配置 - 10种音色，每种生成约24000字
VOICE_CONFIGS = [
    {"voice_type": "BV001_streaming", "name": "通用女声", "code": "001"},
    {"voice_type": "BV002_streaming", "name": "通用男声", "code": "002"},
    {"voice_type": "BV701_streaming", "name": "擎苍", "code": "701"},
    {"voice_type": "BV119_streaming", "name": "通用赘婿", "code": "119"},
    {"voice_type": "BV102_streaming", "name": "儒雅青年", "code": "102"},
    {"voice_type": "BV113_streaming", "name": "甜宠少御", "code": "113"},
    {"voice_type": "BV115_streaming", "name": "古风少御", "code": "115"},
    {"voice_type": "BV007_streaming", "name": "亲切女声", "code": "007"},
    {"voice_type": "BV056_streaming", "name": "阳光男声", "code": "056"},
    {"voice_type": "BV005_streaming", "name": "活泼女声", "code": "005"},
]

def get_cluster(voice: str) -> str:
    """获取集群名称"""
    if voice.startswith("S_"):
        return "volcano_icl"
    return "volcano_tts"

class DouyinTTSWebSocketJupyter:
    """豆包TTS WebSocket核心类 - Jupyter版本"""
    
    def __init__(self, config):
        self.config = config
        self.headers = {
            "Authorization": f"Bearer;{config['ACCESS_TOKEN']}",
        }
    
    async def synthesize_text(self, text, voice_type):
        """使用WebSocket合成单个文本"""
        try:
            # 动态导入websockets（如果未安装会提示）
            try:
                import websockets
            except ImportError:
                print("❌ 需要安装websockets库: pip install websockets")
                return None
            
            # 连接WebSocket
            logger.info(f"连接到 {self.config['ENDPOINT']}")
            websocket = await websockets.connect(
                self.config["ENDPOINT"], 
                additional_headers=self.headers, 
                max_size=10 * 1024 * 1024
            )
            
            try:
                # 确定集群
                cluster = get_cluster(voice_type)
                
                # 准备请求负载
                request = {
                    "app": {
                        "appid": self.config["APPID"],
                        "token": self.config["ACCESS_TOKEN"],
                        "cluster": cluster,
                    },
                    "user": {
                        "uid": str(uuid.uuid4()),
                    },
                    "audio": {
                        "voice_type": voice_type,
                        "encoding": self.config["ENCODING"],
                    },
                    "request": {
                        "reqid": str(uuid.uuid4()),
                        "text": text,
                        "operation": "submit",
                        "with_timestamp": "1",
                        "extra_param": json.dumps({
                            "disable_markdown_filter": False,
                        }),
                    },
                }
                
                # 发送请求
                await websocket.send(json.dumps(request))
                
                # 接收音频数据
                audio_data = bytearray()
                response_count = 0
                max_responses = 50  # 最大响应数量限制
                
                while response_count < max_responses:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                        response_count += 1
                        
                        if isinstance(response, bytes):
                            audio_data.extend(response)
                            # 如果收到足够的音频数据，可以结束
                            if len(audio_data) > 1000:  # 至少1KB数据
                                break
                        elif isinstance(response, str):
                            # 处理JSON响应
                            try:
                                json_resp = json.loads(response)
                                if 'data' in json_resp:
                                    import base64
                                    audio_chunk = base64.b64decode(json_resp['data'])
                                    audio_data.extend(audio_chunk)
                            except json.JSONDecodeError:
                                pass
                        
                    except asyncio.TimeoutError:
                        logger.warning("接收响应超时")
                        break
                
                # 检查是否收到音频数据
                if not audio_data:
                    raise RuntimeError("未收到音频数据")
                
                return bytes(audio_data)
                
            finally:
                await websocket.close()
                
        except Exception as e:
            logger.error(f"WebSocket合成失败: {e}")
            return None

def load_text_files(config):
    """加载文本和拼音文件"""
    print("=== 加载文本文件 ===")
    
    # 读取文本文件
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    return texts, pinyins

def select_texts_for_target_chars(texts, target_chars):
    """根据目标字数选择文本"""
    selected_texts = []
    selected_indices = []
    current_chars = 0
    
    for i, text in enumerate(texts):
        text_length = len(text)
        if current_chars + text_length <= target_chars:
            selected_texts.append(text)
            selected_indices.append(i)
            current_chars += text_length
        else:
            # 如果加上这个文本会超过目标字数，检查是否接近目标
            if target_chars - current_chars > text_length // 2:
                selected_texts.append(text)
                selected_indices.append(i)
                current_chars += text_length
            break
    
    return selected_texts, selected_indices, current_chars

def get_existing_files(output_dir, voice_code):
    """获取指定音色已存在的文件"""
    pattern = os.path.join(output_dir, f"*{voice_code}*.wav")
    audio_files = glob.glob(pattern)
    return len(audio_files)

async def test_single_voice():
    """测试单个音色"""
    print("=== 豆包语音WebSocket测试 ===")
    
    config = DOUYIN_CONFIG
    
    # 检查文件
    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return False
    
    # 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)
    
    # 读取测试文本
    try:
        with open(config['TEXT_FILE'], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 加载文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return False
    
    # 创建TTS对象
    tts = DouyinTTSWebSocketJupyter(config)
    
    # 测试文本
    test_text = texts[0] if texts else "这是一个测试文本"
    voice_type = "BV001_streaming"
    
    print(f"\n开始测试:")
    print(f"  文本: {test_text}")
    print(f"  音色: {voice_type}")
    
    # 合成语音
    audio_data = await tts.synthesize_text(test_text, voice_type)
    
    if audio_data:
        try:
            # 保存音频文件
            audio_path = os.path.join(config["OUTPUT_DIR"], "test_websocket.wav")
            with open(audio_path, "wb") as f:
                f.write(audio_data)
            print(f"✓ 测试成功！音频已保存到: {audio_path}")
            print(f"✓ 音频大小: {len(audio_data)} 字节")
            return True
        except Exception as e:
            print(f"✗ 保存音频失败: {e}")
            return False
    else:
        print(f"✗ 测试失败")
        return False

async def generate_voice_batch_jupyter(voice_config, texts, pinyins, config, batch_num, max_files=5):
    """生成单个音色批次的音频 - Jupyter版本（限制文件数量）"""
    voice_type = voice_config["voice_type"]
    voice_name = voice_config["name"]
    voice_code = voice_config["code"]
    
    print(f"\n=== 批次 {batch_num}: {voice_name} ({voice_type}) ===")
    print(f"限制生成: {max_files} 个文件（测试用）")
    
    # 选择前几个文本进行测试
    selected_texts = texts[:max_files]
    selected_indices = list(range(max_files))
    actual_chars = sum(len(text) for text in selected_texts)
    
    print(f"选择文本: {len(selected_texts)} 条")
    print(f"实际字数: {actual_chars} 字")
    
    if not selected_texts:
        print("✗ 没有可用的文本")
        return False
    
    # 创建TTS对象
    tts = DouyinTTSWebSocketJupyter(config)
    
    # 统计信息
    success_count = 0
    error_count = 0
    start_time = time.time()
    
    # 打开CSV文件进行追加
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    csv_exists = os.path.exists(csv_path)
    
    with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
        # 写入标题行（如果文件不存在）
        if not csv_exists:
            csvfile.write('音频名\t类型\t文本\t注音\t音色\n')
        
        for i, (text_idx, text) in enumerate(zip(selected_indices, selected_texts), 1):
            pinyin = pinyins[text_idx] if text_idx < len(pinyins) else ""
            
            # 音频文件名格式：音色代码 + 序号
            audio_name = f"{voice_code}{i:04d}"
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")
            
            print(f"[{i}/{len(selected_texts)}] {audio_name}: {text[:50]}{'...' if len(text) > 50 else ''}")
            
            # 检查文件是否已存在
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                continue
            
            # 生成音频
            audio_data = await tts.synthesize_text(text, voice_type)
            
            if audio_data:
                try:
                    # 保存音频文件
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)
                    
                    # 写入CSV记录
                    csvfile.write(f"{audio_name}\tc\t{text}\t{pinyin}\t{voice_name}\n")
                    csvfile.flush()
                    
                    print(f"  ✓ 成功 ({len(audio_data)} 字节)")
                    success_count += 1
                    
                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1
            
            # 请求间隔
            await asyncio.sleep(config["REQUEST_INTERVAL"])
    
    elapsed_time = time.time() - start_time
    print(f"\n批次 {batch_num} 完成:")
    print(f"  耗时: {elapsed_time/60:.1f}分钟")
    print(f"  成功: {success_count}")
    print(f"  失败: {error_count}")
    
    return error_count == 0

# Jupyter专用的主函数
async def main_jupyter():
    """Jupyter专用主函数"""
    print("=" * 60)
    print("豆包语音合成大模型WebSocket API - Jupyter测试版")
    print("=" * 60)
    
    config = DOUYIN_CONFIG
    
    # 测试单个音色
    success = await test_single_voice()
    
    if success:
        print("\n🎉 基本测试成功！")
        
        # 可以继续测试批量生成（限制数量）
        print("\n是否继续测试批量生成？")
        
        # 加载文本文件
        texts, pinyins = load_text_files(config)
        if texts:
            # 测试第一个音色，生成3个文件
            voice_config = VOICE_CONFIGS[0]
            await generate_voice_batch_jupyter(voice_config, texts, pinyins, config, 1, max_files=3)
    else:
        print("\n⚠️ 基本测试失败")

# 在Jupyter中运行的函数
def run_jupyter_test():
    """在Jupyter中运行测试"""
    try:
        # 检查是否已经有事件循环
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # 如果有运行中的循环，创建任务
            task = loop.create_task(main_jupyter())
            return task
        else:
            # 如果没有运行中的循环，直接运行
            return asyncio.run(main_jupyter())
    except RuntimeError:
        # 如果出现运行时错误，使用nest_asyncio
        return asyncio.run(main_jupyter())
