#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文本分配逻辑
验证按长度均匀分配到多个音色的算法
"""

import os

# 配置
TEST_CONFIG = {
    "TEXT_FILE": "../shuffled_from_rank_random.txt",
    "PINYIN_FILE": "../shuffled_from_rank_random_pinyin.txt",
    "MIN_CHARS_PER_VOICE": 18000,
    "MAX_CHARS_PER_VOICE": 24000,
}

# 简化的音色配置（用于测试）
VOICE_CONFIGS = [
    {"voice_code": "D001", "name": "婉曲大叔"},
    {"voice_code": "D002", "name": "黛梦传媒"},
    {"voice_code": "D003", "name": "国州德哥"},
    {"voice_code": "D004", "name": "北京小爷"},
    {"voice_code": "D005", "name": "少年子心"},
    {"voice_code": "D006", "name": "美丽女友"},
    {"voice_code": "D007", "name": "深夜播客"},
    {"voice_code": "D008", "name": "撒娇女友"},
    {"voice_code": "D009", "name": "远亲女友"},
    {"voice_code": "D010", "name": "豪宇小哥"},
]

def load_text_files(config):
    """加载文本和拼音文件"""
    print("=== 加载文本文件 ===")
    
    # 读取文本文件
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    return texts, pinyins

def group_texts_by_length(texts, pinyins):
    """按文本长度分组"""
    length_groups = {}
    
    for i, text in enumerate(texts):
        length = len(text)
        if length not in length_groups:
            length_groups[length] = []
        
        pinyin = pinyins[i] if i < len(pinyins) else ""
        length_groups[length].append({
            'text': text,
            'pinyin': pinyin,
            'index': i
        })
    
    return length_groups

def calculate_voice_distribution(texts, config):
    """计算需要多少种音色以及每种音色的文本分配"""
    total_chars = sum(len(text) for text in texts)
    min_chars = config["MIN_CHARS_PER_VOICE"]
    max_chars = config["MAX_CHARS_PER_VOICE"]
    
    # 计算需要的音色数量
    min_voices_needed = total_chars // max_chars + (1 if total_chars % max_chars > 0 else 0)
    max_voices_possible = total_chars // min_chars
    
    # 选择一个合理的音色数量
    target_voices = min(len(VOICE_CONFIGS), max_voices_possible)
    target_chars_per_voice = total_chars // target_voices
    
    print(f"文本总字数: {total_chars}")
    print(f"最少需要音色: {min_voices_needed}")
    print(f"最多可用音色: {max_voices_possible}")
    print(f"实际使用音色: {target_voices}")
    print(f"每种音色平均字数: {target_chars_per_voice}")
    
    return target_voices, target_chars_per_voice

def distribute_texts_to_voices(length_groups, target_voices, target_chars_per_voice):
    """将文本按长度均匀分配给各个音色"""
    voice_assignments = [[] for _ in range(target_voices)]
    voice_char_counts = [0] * target_voices
    
    # 按长度从小到大处理
    for length in sorted(length_groups.keys()):
        texts_of_length = length_groups[length]
        
        print(f"处理长度 {length} 字的文本: {len(texts_of_length)} 条")
        
        # 将这个长度的文本均匀分配给各个音色
        for i, text_info in enumerate(texts_of_length):
            # 找到当前字数最少的音色
            voice_idx = voice_char_counts.index(min(voice_char_counts))
            
            # 检查是否会超过目标字数太多
            if voice_char_counts[voice_idx] + length <= target_chars_per_voice * 1.2:  # 允许20%的超出
                voice_assignments[voice_idx].append(text_info)
                voice_char_counts[voice_idx] += length
            else:
                # 如果会超出太多，找下一个最少的音色
                sorted_indices = sorted(range(target_voices), key=lambda x: voice_char_counts[x])
                assigned = False
                for voice_idx in sorted_indices:
                    if voice_char_counts[voice_idx] + length <= target_chars_per_voice * 1.2:
                        voice_assignments[voice_idx].append(text_info)
                        voice_char_counts[voice_idx] += length
                        assigned = True
                        break
                
                if not assigned:
                    # 如果都会超出，分配给字数最少的
                    voice_idx = voice_char_counts.index(min(voice_char_counts))
                    voice_assignments[voice_idx].append(text_info)
                    voice_char_counts[voice_idx] += length
    
    return voice_assignments, voice_char_counts

def analyze_distribution(voice_assignments, voice_char_counts, target_voices):
    """分析分配结果"""
    print(f"\n=== 分配结果分析 ===")
    
    for i in range(target_voices):
        voice_config = VOICE_CONFIGS[i]
        char_count = voice_char_counts[i]
        text_count = len(voice_assignments[i])
        
        # 分析长度分布
        length_dist = {}
        for text_info in voice_assignments[i]:
            length = len(text_info['text'])
            length_dist[length] = length_dist.get(length, 0) + 1
        
        min_length = min(length_dist.keys()) if length_dist else 0
        max_length = max(length_dist.keys()) if length_dist else 0
        
        print(f"\n{voice_config['voice_code']} {voice_config['name']}:")
        print(f"  文本数量: {text_count} 条")
        print(f"  总字数: {char_count} 字")
        print(f"  长度范围: {min_length}-{max_length} 字")
        print(f"  长度种类: {len(length_dist)} 种")
        
        # 显示前5个长度的分布
        sorted_lengths = sorted(length_dist.items())[:5]
        print(f"  长度分布: {sorted_lengths}")

def test_text_distribution():
    """测试文本分配"""
    print("=" * 60)
    print("文本分配逻辑测试")
    print("=" * 60)
    
    config = TEST_CONFIG
    
    # 1. 加载文本文件
    texts, pinyins = load_text_files(config)
    if texts is None:
        return
    
    # 2. 按长度分组文本
    print(f"\n=== 分析文本长度分布 ===")
    length_groups = group_texts_by_length(texts, pinyins)
    
    print(f"文本长度范围: {min(length_groups.keys())}-{max(length_groups.keys())} 字")
    print(f"长度种类数: {len(length_groups)}")
    
    # 显示长度分布统计
    print(f"\n长度分布统计:")
    for length in sorted(length_groups.keys())[:15]:  # 显示前15个长度
        count = len(length_groups[length])
        total_chars = count * length
        print(f"  {length}字: {count} 条 (共{total_chars}字)")
    
    # 3. 计算音色分配
    print(f"\n=== 计算音色分配 ===")
    target_voices, target_chars_per_voice = calculate_voice_distribution(texts, config)
    
    # 4. 分配文本到各个音色
    print(f"\n=== 分配文本到音色 ===")
    voice_assignments, voice_char_counts = distribute_texts_to_voices(
        length_groups, target_voices, target_chars_per_voice
    )
    
    # 5. 分析分配结果
    analyze_distribution(voice_assignments, voice_char_counts, target_voices)
    
    # 6. 总结
    total_assigned_chars = sum(voice_char_counts)
    total_assigned_texts = sum(len(assignments) for assignments in voice_assignments)
    
    print(f"\n=== 总结 ===")
    print(f"原始文本: {len(texts)} 条, {sum(len(text) for text in texts)} 字")
    print(f"分配文本: {total_assigned_texts} 条, {total_assigned_chars} 字")
    print(f"使用音色: {target_voices} 种")
    print(f"平均每种音色: {total_assigned_chars // target_voices} 字")
    
    # 检查分配均衡性
    min_chars = min(voice_char_counts)
    max_chars = max(voice_char_counts)
    balance_ratio = min_chars / max_chars if max_chars > 0 else 0
    
    print(f"字数范围: {min_chars} - {max_chars} 字")
    print(f"均衡度: {balance_ratio:.2%} (越接近100%越均衡)")
    
    if balance_ratio >= 0.8:
        print("✅ 分配均衡度良好")
    else:
        print("⚠️ 分配不够均衡，可能需要调整算法")

def main():
    """主函数"""
    test_text_distribution()

if __name__ == "__main__":
    main()
