# ============================================================================
# 豆包TTS错误调试版本 - 显示详细错误信息
# 复制此代码到您的Jupyter Notebook中替换现有代码
# ============================================================================

"""
豆包语音合成大模型WebSocket API调用程序 - 错误调试版
显示详细错误信息，帮助诊断问题
"""

import asyncio
import json
import logging
import uuid
import os
import time
import glob
from datetime import datetime
import websockets
from protocols import MsgType, full_client_request, receive_message

# Jupyter异步支持
import nest_asyncio
nest_asyncio.apply()

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print("✅ 环境设置完成")

# ==================== 配置区域 ====================
DOUYIN_CONFIG = {
    # API配置
    "APPID": "9862368305",
    "ACCESS_TOKEN": "bbxNaAzRzBSkySjouDItXl6tBUUyNd2W",
    
    # WebSocket配置
    "ENDPOINT": "wss://openspeech.bytedance.com/api/v1/tts/ws_binary",
    
    # 音频格式配置
    "ENCODING": "wav",
    
    # 文件配置
    "TEXT_FILE": "shuffled_from_rank_random.txt",
    "PINYIN_FILE": "shuffled_from_rank_random_pinyin.txt",
    "OUTPUT_DIR": "audio_output_debug",
    "CSV_FILE": "douyin_debug_audio_log.csv",
    "REQUEST_INTERVAL": 3.0,  # 增加到3秒
    
    # 生成配置
    "TARGET_CHARS_PER_VOICE": 100,  # 先测试少量文本
}

# 音色配置 - 只用一种基础音色测试
VOICE_CONFIGS = [
    {"voice_type": "BV001_streaming", "name": "通用女声", "code": "001"},
]

print("✅ 配置加载完成")

def get_cluster(voice: str) -> str:
    """获取集群名称"""
    if voice.startswith("S_"):
        return "volcano_icl"
    return "volcano_tts"

class DouyinTTSDebug:
    """豆包TTS调试版 - 显示详细错误信息"""
    
    def __init__(self, config):
        self.config = config
        self.headers = {
            "Authorization": f"Bearer;{config['ACCESS_TOKEN']}",
        }
        self.success_count = 0
        self.error_count = 0
    
    async def synthesize_text_debug(self, text, voice_type):
        """调试版合成文本 - 显示详细错误信息"""
        try:
            # 确定集群
            cluster = get_cluster(voice_type)
            
            print(f"🔗 连接参数:")
            print(f"  文本: '{text}'")
            print(f"  音色: {voice_type}")
            print(f"  集群: {cluster}")
            print(f"  APPID: {self.config['APPID']}")
            print(f"  TOKEN: {self.config['ACCESS_TOKEN'][:20]}...")

            # 连接到服务器
            logger.info(f"连接到 {self.config['ENDPOINT']}")
            websocket = await websockets.connect(
                self.config["ENDPOINT"], 
                additional_headers=self.headers, 
                max_size=10 * 1024 * 1024
            )
            
            try:
                # 获取连接信息
                logid = websocket.response.headers.get('x-tt-logid', 'N/A')
                logger.info(f"已连接到WebSocket服务器, Logid: {logid}")

                # 准备请求负载
                request = {
                    "app": {
                        "appid": self.config["APPID"],
                        "token": self.config["ACCESS_TOKEN"],
                        "cluster": cluster,
                    },
                    "user": {
                        "uid": str(uuid.uuid4()),
                    },
                    "audio": {
                        "voice_type": voice_type,
                        "encoding": self.config["ENCODING"],
                    },
                    "request": {
                        "reqid": str(uuid.uuid4()),
                        "text": text,
                        "operation": "submit",
                        "with_timestamp": "1",
                        "extra_param": json.dumps({
                            "disable_markdown_filter": False,
                        }),
                    },
                }
                
                print(f"📤 发送请求:")
                print(json.dumps(request, indent=2, ensure_ascii=False))

                # 发送请求
                await full_client_request(websocket, json.dumps(request).encode())

                # 接收音频数据
                audio_data = bytearray()
                message_count = 0
                
                while True:
                    msg = await receive_message(websocket)
                    message_count += 1
                    
                    print(f"📥 收到消息 {message_count}:")
                    print(f"  类型: {msg.type} ({MsgType(msg.type).name if msg.type in MsgType else '未知'})")
                    print(f"  序号: {msg.sequence}")
                    print(f"  负载大小: {msg.payload_size}")

                    if msg.type == MsgType.FrontEndResultServer:
                        print(f"  内容: 前端结果消息")
                        continue
                    elif msg.type == MsgType.AudioOnlyServer:
                        print(f"  内容: 音频数据 ({len(msg.payload)} 字节)")
                        audio_data.extend(msg.payload)
                        if msg.sequence < 0:  # 最后一条消息
                            print(f"  ✅ 收到最后一条音频消息")
                            break
                    elif msg.type == MsgType.ErrorServer:
                        print(f"  内容: 错误消息")
                        try:
                            error_data = json.loads(msg.payload.decode('utf-8'))
                            print(f"  错误详情: {json.dumps(error_data, indent=4, ensure_ascii=False)}")
                            error_msg = error_data.get('message', '未知错误')
                            error_code = error_data.get('code', 'N/A')
                            raise RuntimeError(f"服务器错误 [{error_code}]: {error_msg}")
                        except json.JSONDecodeError:
                            error_text = msg.payload.decode('utf-8', errors='ignore')
                            print(f"  错误内容: {error_text}")
                            raise RuntimeError(f"服务器错误: {error_text}")
                    else:
                        print(f"  内容: 未知消息类型")
                        print(f"  原始数据: {msg.payload[:100]}...")
                        raise RuntimeError(f"未知消息类型: {msg.type}")

                # 检查是否收到音频数据
                if not audio_data:
                    raise RuntimeError("未收到音频数据")

                print(f"✅ 音频合成成功: {len(audio_data)} 字节")
                self.success_count += 1
                return bytes(audio_data)

            finally:
                await websocket.close()
                logger.info("连接已关闭")
                
        except Exception as e:
            logger.error(f"豆包TTS合成失败: {e}")
            self.error_count += 1
            return None

def load_text_files(config):
    """加载文本文件"""
    print("=== 加载文本文件 ===")
    
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
        return texts
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return []

async def test_single_synthesis():
    """测试单个文本合成"""
    print("=" * 60)
    print("豆包TTS单个文本测试 - 调试版")
    print("=" * 60)
    
    config = DOUYIN_CONFIG
    
    # 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)
    
    # 加载文本
    texts = load_text_files(config)
    if not texts:
        print("❌ 没有可用文本")
        return
    
    # 选择第一个文本进行测试
    test_text = texts[0]
    voice_config = VOICE_CONFIGS[0]
    
    print(f"测试配置:")
    print(f"  文本: '{test_text}'")
    print(f"  音色: {voice_config['name']} ({voice_config['voice_type']})")
    
    # 创建TTS对象
    tts = DouyinTTSDebug(config)
    
    # 测试合成
    print(f"\n🚀 开始测试合成...")
    audio_data = await tts.synthesize_text_debug(test_text, voice_config["voice_type"])
    
    if audio_data:
        # 保存测试文件
        test_file = os.path.join(config["OUTPUT_DIR"], "test_001.wav")
        with open(test_file, "wb") as f:
            f.write(audio_data)
        print(f"✅ 测试成功！文件保存: {test_file}")
    else:
        print(f"❌ 测试失败")
    
    return audio_data is not None

async def test_api_permissions():
    """测试API权限"""
    print("=" * 60)
    print("豆包API权限测试")
    print("=" * 60)
    
    config = DOUYIN_CONFIG
    
    # 测试不同的音色
    test_voices = [
        "BV001_streaming",  # 基础女声
        "BV002_streaming",  # 基础男声
    ]
    
    test_text = "你好"
    
    for voice in test_voices:
        print(f"\n🎵 测试音色: {voice}")
        
        tts = DouyinTTSDebug(config)
        audio_data = await tts.synthesize_text_debug(test_text, voice)
        
        if audio_data:
            print(f"✅ {voice} 可用")
        else:
            print(f"❌ {voice} 不可用")
        
        # 间隔
        await asyncio.sleep(2)

async def main():
    """主函数 - 调试版"""
    print("🔍 豆包TTS错误调试程序")
    print("此程序将显示详细的错误信息，帮助诊断问题")
    
    # 1. 测试单个文本合成
    print(f"\n第一步：测试单个文本合成")
    success = await test_single_synthesis()
    
    if success:
        print(f"🎉 单个文本测试成功！")
    else:
        print(f"❌ 单个文本测试失败，开始权限测试...")
        
        # 2. 测试API权限
        print(f"\n第二步：测试API权限")
        await test_api_permissions()
    
    print(f"\n🔍 调试建议:")
    print(f"1. 检查错误消息中的具体错误代码和描述")
    print(f"2. 确认API密钥是否有TTS服务权限")
    print(f"3. 确认账户余额是否充足")
    print(f"4. 联系豆包技术支持获取帮助")

print("✅ 调试程序准备完成")

# 运行调试程序
print("\n🚀 开始运行豆包TTS调试程序...")
await main()

print("\n📋 调试完成！")
print("请查看上面的详细错误信息，并根据建议进行处理。")
