#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
讯飞TTS多音色测试版 - 测试不同音色的可用性
"""

import websocket
import datetime
import hashlib
import base64
import hmac
import json
from urllib.parse import urlencode
import time
import ssl
from wsgiref.handlers import format_date_time
from datetime import datetime
from time import mktime
import _thread as thread
import os
import wave
import csv

# ==================== 测试配置 ====================
TEST_CONFIG = {
    # API配置 
    "APPID": "fee2aa97",           # 应用ID
    "APISecret": "YWYzODMwN2ExNjhlMDBmN2ExMzE5MWQz",   # API密钥
    "APIKey": "a4727f9636df58ee310a31d2b857361a",         # API Key
     
    # 语音参数配置
    "SPEED": "50",                    # 语速 (0-100)
    "VOLUME": "80",                   # 音量 (0-100)
    "PITCH": "50",                    # 音调 (0-100)
    
    # 文件配置
    "TEXT_FILE": "../shuffled_from_rank_random.txt",
    "PINYIN_FILE": "../shuffled_from_rank_random_pinyin.txt",
    "OUTPUT_DIR": "audio_output",
    "CSV_FILE": "test_xunfei_audio_info.csv",
    "REQUEST_INTERVAL": 1.0,          # 测试时稍微慢一点
}

# 测试音色配置 - 5种音色各生成1个文件
TEST_VOICES = [
    {"voice_type": "x4_xiaoyan", "name": "讯飞小燕"},
    {"voice_type": "x4_yezi", "name": "讯飞小露"},
    {"voice_type": "aisjiuxu", "name": "讯飞许久"},
    {"voice_type": "aisjinger", "name": "讯飞小婧"},
    {"voice_type": "aisbabyxu", "name": "讯飞许小宝"},
]

class XunfeiTTSTest:
    """讯飞TTS测试核心类"""
    
    def __init__(self, config):
        self.config = config
        self.audio_data = b""
        self.status = 0  # 0: 初始, 1: 连接成功, 2: 完成, -1: 错误
        self.host = "ws-api.xfyun.cn"
        self.path = "/v2/tts"
        self.url = f"wss://{self.host}{self.path}"
        
    def create_url(self):
        """生成带鉴权的WebSocket URL"""
        now = datetime.now()
        date = format_date_time(mktime(now.timetuple()))
        
        signature_origin = f"host: {self.host}\n"
        signature_origin += f"date: {date}\n"
        signature_origin += f"GET {self.path} HTTP/1.1"
        
        signature_sha = hmac.new(
            self.config["APISecret"].encode('utf-8'),
            signature_origin.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')
        
        authorization_origin = f'api_key="{self.config["APIKey"]}", algorithm="hmac-sha256", headers="host date request-line", signature="{signature_sha}"'
        authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode(encoding='utf-8')
        
        v = {
            "authorization": authorization,
            "date": date,
            "host": self.host
        }
        
        return self.url + '?' + urlencode(v)
    
    def on_message(self, ws, message):
        """WebSocket消息回调"""
        try:
            message = json.loads(message)
            code = message["code"]
            
            if code != 0:
                print(f"  ✗ API错误: {message['message']} (code: {code})")
                self.status = -1
            else:
                data = message.get("data")
                if data:
                    audio = data["audio"]
                    audio_data = base64.b64decode(audio)
                    self.audio_data += audio_data
                    
                    if data["status"] == 2:  # 传输完成
                        self.status = 2
                        ws.close()
        except Exception as e:
            print(f"  ✗ 消息处理错误: {e}")
            self.status = -1
    
    def on_error(self, ws, error):
        """WebSocket错误回调"""
        print(f"  ✗ WebSocket错误: {error}")
        self.status = -1
    
    def on_close(self, ws, close_status_code, close_msg):
        """WebSocket关闭回调"""
        pass
    
    def on_open(self, ws):
        """WebSocket连接成功回调"""
        def run(*args):
            data = {
                "common": {"app_id": self.config["APPID"]},
                "business": {
                    "aue": "raw",
                    "auf": "audio/L16;rate=16000",
                    "vcn": self.voice_name,
                    "speed": self.config["SPEED"],
                    "volume": self.config["VOLUME"],
                    "pitch": self.config["PITCH"],
                    "tte": "UTF8"
                },
                "data": {
                    "status": 2,
                    "text": base64.b64encode(self.text.encode('utf-8')).decode('utf-8')
                }
            }
            ws.send(json.dumps(data))
        
        thread.start_new_thread(run, ())
        self.status = 1
    
    def synthesize_text(self, text, voice_name, timeout=30):
        """合成单个文本"""
        self.text = text
        self.voice_name = voice_name
        self.audio_data = b""
        self.status = 0
        
        websocket.enableTrace(False)
        ws_url = self.create_url()
        ws = websocket.WebSocketApp(
            ws_url,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close,
            on_open=self.on_open
        )
        
        ws.run_forever(sslopt={"cert_reqs": ssl.CERT_NONE})
        
        start_time = time.time()
        while self.status not in [2, -1] and (time.time() - start_time) < timeout:
            time.sleep(0.1)
        
        return self.audio_data if self.status == 2 else None

def save_audio_as_wav(audio_data, output_path, sample_rate=16000):
    """保存PCM音频数据为WAV文件"""
    with wave.open(output_path, 'wb') as wav_file:
        wav_file.setnchannels(1)  # 单声道
        wav_file.setsampwidth(2)  # 16位
        wav_file.setframerate(sample_rate)  # 采样率
        wav_file.writeframes(audio_data)

def test_xunfei_voices():
    """测试讯飞多音色功能"""
    print("=" * 60)
    print("讯飞TTS多音色测试")
    print("=" * 60)
    
    config = TEST_CONFIG
    
    # 检查必要文件
    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return False
    
    # 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)
    
    # 读取测试文本
    try:
        with open(config['TEXT_FILE'], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 加载文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return False
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(config['PINYIN_FILE']):
        try:
            with open(config['PINYIN_FILE'], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 加载拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    # 创建TTS对象
    tts = XunfeiTTSTest(config)
    
    # 统计信息
    success_count = 0
    error_count = 0
    
    # 创建CSV文件
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    with open(csv_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.writer(csvfile, quoting=csv.QUOTE_ALL)
        writer.writerow(['音频名', '类型', '文本', '注音1', '音色'])
        
        print(f"\n开始测试 {len(TEST_VOICES)} 种音色:")
        
        for i, voice_config in enumerate(TEST_VOICES, 1):
            voice_type = voice_config["voice_type"]
            voice_name = voice_config["name"]
            
            # 使用第i个文本进行测试
            text_index = i - 1
            text = texts[text_index] if text_index < len(texts) else "这是一个测试文本"
            pinyin = pinyins[text_index] if text_index < len(pinyins) else ""
            
            audio_name = f"test_{voice_type}"
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")
            
            print(f"\n[{i}/{len(TEST_VOICES)}] 测试 {voice_name} ({voice_type})")
            print(f"  文本: {text[:50]}{'...' if len(text) > 50 else ''}")
            
            # 生成音频
            audio_data = tts.synthesize_text(text, voice_type)
            if audio_data:
                try:
                    # 保存音频文件
                    save_audio_as_wav(audio_data, audio_path)
                    
                    # 写入CSV记录
                    writer.writerow([audio_name, 'c', text, pinyin, voice_name])
                    csvfile.flush()
                    
                    print(f"  ✓ 成功 ({len(audio_data)} 字节) -> {audio_name}.wav")
                    success_count += 1
                    
                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1
            
            # 请求间隔
            time.sleep(config["REQUEST_INTERVAL"])
    
    print(f"\n=== 测试完成 ===")
    print(f"成功: {success_count}/{len(TEST_VOICES)}")
    print(f"失败: {error_count}")
    print(f"输出目录: {config['OUTPUT_DIR']}")
    print(f"CSV记录: {csv_path}")
    
    if success_count == len(TEST_VOICES):
        print("🎉 所有音色测试成功！可以开始批量生成")
        return True
    else:
        print("⚠️ 部分音色测试失败，请检查配置")
        return False

def main():
    """主函数"""
    success = test_xunfei_voices()
    
    if success:
        print(f"\n💡 测试成功！")
        print(f"现在可以运行 xunfei_tts_multi_voice_1000.py 进行批量生成")
    else:
        print(f"\n⚠️ 测试失败，请检查配置和网络连接")

if __name__ == "__main__":
    main()
