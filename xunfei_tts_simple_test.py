#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
讯飞TTS简单连接测试
"""

import websocket
import datetime
import hashlib
import base64
import hmac
import json
from urllib.parse import urlencode
import time
import ssl
from wsgiref.handlers import format_date_time
from datetime import datetime
from time import mktime
import _thread as thread
import os
import wave

# ==================== 简化测试配置 ====================
SIMPLE_CONFIG = {
    # API配置 
    "APPID": "fee2aa97",
    "APISecret": "YWYzODMwN2ExNjhlMDBmN2ExMzE5MWQz",
    "APIKey": "a4727f9636df58ee310a31d2b857361a",
     
    # 语音参数配置
    "VOICE_NAME": "x4_xiaoyan",
    "SPEED": "50",
    "VOLUME": "80", 
    "PITCH": "50",
}

class SimpleXunfeiTTS:
    """简化的讯飞TTS测试类"""
    
    def __init__(self, config):
        self.config = config
        self.audio_data = b""
        self.status = 0
        self.error_msg = ""
        self.host = "ws-api.xfyun.cn"
        self.path = "/v2/tts"
        self.url = f"wss://{self.host}{self.path}"
        
    def create_url(self):
        """生成带鉴权的WebSocket URL"""
        now = datetime.now()
        date = format_date_time(mktime(now.timetuple()))
        
        signature_origin = f"host: {self.host}\n"
        signature_origin += f"date: {date}\n"
        signature_origin += f"GET {self.path} HTTP/1.1"
        
        signature_sha = hmac.new(
            self.config["APISecret"].encode('utf-8'),
            signature_origin.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')
        
        authorization_origin = f'api_key="{self.config["APIKey"]}", algorithm="hmac-sha256", headers="host date request-line", signature="{signature_sha}"'
        authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode(encoding='utf-8')
        
        v = {
            "authorization": authorization,
            "date": date,
            "host": self.host
        }
        
        url = self.url + '?' + urlencode(v)
        print(f"WebSocket URL: {url[:100]}...")
        return url
    
    def on_message(self, ws, message):
        """WebSocket消息回调"""
        try:
            print(f"收到消息: {message[:200]}...")
            message = json.loads(message)
            code = message["code"]
            
            if code != 0:
                self.error_msg = f"API错误: {message['message']} (code: {code})"
                print(f"  ✗ {self.error_msg}")
                self.status = -1
            else:
                data = message.get("data")
                if data:
                    audio = data["audio"]
                    audio_data = base64.b64decode(audio)
                    self.audio_data += audio_data
                    print(f"  收到音频数据: {len(audio_data)} 字节")
                    
                    if data["status"] == 2:  # 传输完成
                        print(f"  传输完成，总音频数据: {len(self.audio_data)} 字节")
                        self.status = 2
                        ws.close()
        except Exception as e:
            self.error_msg = f"消息处理错误: {e}"
            print(f"  ✗ {self.error_msg}")
            self.status = -1
    
    def on_error(self, ws, error):
        """WebSocket错误回调"""
        self.error_msg = f"WebSocket错误: {error}"
        print(f"  ✗ {self.error_msg}")
        self.status = -1
    
    def on_close(self, ws, close_status_code, close_msg):
        """WebSocket关闭回调"""
        print(f"  WebSocket连接已关闭: {close_status_code}, {close_msg}")
    
    def on_open(self, ws):
        """WebSocket连接成功回调"""
        print(f"  ✓ WebSocket连接成功")
        
        def run(*args):
            data = {
                "common": {"app_id": self.config["APPID"]},
                "business": {
                    "aue": "raw",
                    "auf": "audio/L16;rate=16000",
                    "vcn": self.config["VOICE_NAME"],
                    "speed": self.config["SPEED"],
                    "volume": self.config["VOLUME"],
                    "pitch": self.config["PITCH"],
                    "tte": "UTF8"
                },
                "data": {
                    "status": 2,
                    "text": base64.b64encode(self.text.encode('utf-8')).decode('utf-8')
                }
            }
            print(f"  发送请求数据: {json.dumps(data, ensure_ascii=False)[:200]}...")
            ws.send(json.dumps(data))
        
        thread.start_new_thread(run, ())
        self.status = 1
    
    def synthesize_text(self, text, timeout=30):
        """合成单个文本"""
        print(f"\n开始合成文本: {text}")
        self.text = text
        self.audio_data = b""
        self.status = 0
        self.error_msg = ""
        
        try:
            websocket.enableTrace(True)  # 启用详细日志
            ws_url = self.create_url()
            
            print(f"创建WebSocket连接...")
            ws = websocket.WebSocketApp(
                ws_url,
                on_message=self.on_message,
                on_error=self.on_error,
                on_close=self.on_close,
                on_open=self.on_open
            )
            
            print(f"开始WebSocket连接...")
            ws.run_forever(sslopt={"cert_reqs": ssl.CERT_NONE})
            
            print(f"等待处理完成...")
            start_time = time.time()
            while self.status not in [2, -1] and (time.time() - start_time) < timeout:
                time.sleep(0.1)
            
            if self.status == 2:
                print(f"✓ 合成成功，音频数据: {len(self.audio_data)} 字节")
                return self.audio_data
            elif self.status == -1:
                print(f"✗ 合成失败: {self.error_msg}")
                return None
            else:
                print(f"✗ 合成超时")
                return None
                
        except Exception as e:
            print(f"✗ 合成异常: {e}")
            return None

def save_audio_as_wav(audio_data, output_path, sample_rate=16000):
    """保存PCM音频数据为WAV文件"""
    with wave.open(output_path, 'wb') as wav_file:
        wav_file.setnchannels(1)  # 单声道
        wav_file.setsampwidth(2)  # 16位
        wav_file.setframerate(sample_rate)  # 采样率
        wav_file.writeframes(audio_data)

def test_simple_connection():
    """简单连接测试"""
    print("=" * 60)
    print("讯飞TTS简单连接测试")
    print("=" * 60)
    
    config = SIMPLE_CONFIG
    
    print("配置信息:")
    print(f"  APPID: {config['APPID']}")
    print(f"  APIKey: {config['APIKey'][:10]}...")
    print(f"  APISecret: {config['APISecret'][:10]}...")
    print(f"  发音人: {config['VOICE_NAME']}")
    
    # 创建输出目录
    output_dir = "audio_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建TTS对象
    tts = SimpleXunfeiTTS(config)
    
    # 测试文本
    test_text = "这是一个简单的测试文本，用于验证讯飞语音合成功能是否正常工作。"
    
    print(f"\n开始测试...")
    print(f"测试文本: {test_text}")
    
    # 合成语音
    audio_data = tts.synthesize_text(test_text)
    
    if audio_data:
        try:
            # 保存音频文件
            audio_path = os.path.join(output_dir, "simple_test.wav")
            save_audio_as_wav(audio_data, audio_path)
            print(f"✓ 测试成功！音频已保存到: {audio_path}")
            print(f"✓ 音频大小: {len(audio_data)} 字节")
            return True
        except Exception as e:
            print(f"✗ 保存音频失败: {e}")
            return False
    else:
        print(f"✗ 测试失败")
        return False

def main():
    """主函数"""
    success = test_simple_connection()
    
    if success:
        print(f"\n🎉 讯飞TTS连接测试成功！")
        print(f"可以继续进行多音色测试")
    else:
        print(f"\n⚠️ 讯飞TTS连接测试失败")
        print(f"请检查:")
        print(f"  1. 网络连接是否正常")
        print(f"  2. API配置是否正确")
        print(f"  3. 防火墙是否阻止了WebSocket连接")

if __name__ == "__main__":
    main()
