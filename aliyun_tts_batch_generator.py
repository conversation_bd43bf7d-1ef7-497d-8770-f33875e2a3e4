#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云TTS批量生成器 - 生成第7001-8000条音频文件
"""

import requests
import json
import time
import csv
import os
import uuid
import hashlib
import hmac
import base64
from datetime import datetime
from urllib.parse import quote
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# ==================== 阿里云配置 ====================
ALIYUN_CONFIG = {
    "ACCESS_KEY_ID": "LTAI5t7reocF8UzbqkiReQQy",
    "ACCESS_KEY_SECRET": "******************************",
    "APP_KEY": "9LNJJ3RhO4Ys8O35",
    
    # 语音参数配置
    "VOICE": "xiaoyun",
    "FORMAT": "wav",
    "SAMPLE_RATE": 16000,
    "VOLUME": 50,
    "SPEECH_RATE": 0,
    "PITCH_RATE": 0,
    
    # 文件配置
    "TEXT_FILE": "shuffled_from_rank_random.txt",
    "PINYIN_FILE": "shuffled_from_rank_random_pinyin.txt",
    "OUTPUT_DIR": "audio_output",
    "CSV_FILE": "audio_info_8001_9000.csv",
    "REQUEST_INTERVAL": 0.3,  # 请求间隔（秒）
    
    # 生成范围配置
    "START_INDEX": 8001,
    "END_INDEX": 9000,
    "TOTAL_COUNT": 1000
}

def generate_signature(method, uri, params, access_key_secret):
    """生成阿里云API签名"""
    # 排序参数
    sorted_params = sorted(params.items())
    query_string = '&'.join([f"{quote(k, safe='')}={quote(str(v), safe='')}" for k, v in sorted_params])
    
    # 构建待签名字符串
    string_to_sign = f"{method}&{quote(uri, safe='')}&{quote(query_string, safe='')}"
    
    # 计算签名
    signature = base64.b64encode(
        hmac.new(
            (access_key_secret + '&').encode(),
            string_to_sign.encode(),
            hashlib.sha1
        ).digest()
    ).decode()
    
    return signature

class AliyunTTSBatch:
    """阿里云TTS批量生成器"""
    
    def __init__(self, config):
        self.config = config
        self.access_key_id = config["ACCESS_KEY_ID"]
        self.access_key_secret = config["ACCESS_KEY_SECRET"]
        self.app_key = config["APP_KEY"]
        self.access_token = None
        self.token_expire_time = 0
    
    def _get_access_token(self):
        """获取阿里云访问令牌"""
        # 检查现有令牌是否还有效（提前5分钟刷新）
        current_time = time.time()
        if self.access_token and self.token_expire_time > current_time + 300:
            return self.access_token
        
        print("正在获取新的访问令牌...")
        
        # 使用正确的API端点
        domain = "nls-meta.cn-shanghai.aliyuncs.com"
        uri = "/"
        url = f"https://{domain}{uri}"
        
        # 构建请求参数
        timestamp = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')
        nonce = str(uuid.uuid4())
        
        params = {
            'AccessKeyId': self.access_key_id,
            'Action': 'CreateToken',
            'Format': 'JSON',
            'RegionId': 'cn-shanghai',
            'SignatureMethod': 'HMAC-SHA1',
            'SignatureNonce': nonce,
            'SignatureVersion': '1.0',
            'Timestamp': timestamp,
            'Version': '2019-02-28'
        }
        
        # 生成签名
        signature = generate_signature('POST', uri, params, self.access_key_secret)
        params['Signature'] = signature
        
        try:
            response = requests.post(url, data=params, timeout=10, verify=False)
            
            if response.status_code == 200:
                result = response.json()
                
                if 'Token' in result and 'Id' in result['Token']:
                    self.access_token = result['Token']['Id']
                    self.token_expire_time = result['Token']['ExpireTime']
                    print(f"✓ 成功获取访问令牌: {self.access_token[:20]}...")
                    return self.access_token
                else:
                    print(f"✗ 令牌响应格式错误: {result}")
                    return None
            else:
                print(f"✗ 获取令牌失败: {response.status_code}")
                print(f"错误内容: {response.text}")
                return None
                
        except Exception as e:
            print(f"✗ 获取令牌异常: {e}")
            return None
    
    def synthesize_text(self, text):
        """合成单个文本为语音"""
        # 确保有有效的访问令牌
        if not self._get_access_token():
            return None
        
        url = "https://nls-gateway.cn-shanghai.aliyuncs.com/stream/v1/tts"
        
        headers = {
            'Content-Type': 'application/json',
            'X-NLS-Token': self.access_token
        }
        
        data = {
            'appkey': self.app_key,
            'text': text,
            'voice': self.config["VOICE"],
            'format': self.config["FORMAT"],
            'sample_rate': self.config["SAMPLE_RATE"],
            'volume': self.config["VOLUME"],
            'speech_rate': self.config["SPEECH_RATE"],
            'pitch_rate': self.config["PITCH_RATE"]
        }
        
        try:
            response = requests.post(
                url,
                headers=headers,
                data=json.dumps(data),
                timeout=30,
                verify=False
            )
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                if 'audio' in content_type or 'application/octet-stream' in content_type:
                    return response.content
                else:
                    # 可能是错误响应
                    try:
                        error_info = response.json()
                        print(f"  ✗ API错误: {error_info}")
                    except:
                        print(f"  ✗ 未知响应格式: {response.text[:200]}")
                    return None
            else:
                print(f"  ✗ HTTP错误 {response.status_code}: {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            print(f"  ✗ 请求超时")
            return None
        except Exception as e:
            print(f"  ✗ 请求异常: {e}")
            return None

def load_text_files(config):
    """加载文本和拼音文件"""
    print("=== 加载文本文件 ===")
    
    # 读取文本文件
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    return texts, pinyins

def get_existing_files(config):
    """获取已存在的音频文件"""
    existing_indices = set()
    
    for i in range(config['START_INDEX'], config['END_INDEX'] + 1):
        filename = f"Sc{i:07d}.wav"
        if os.path.exists(filename):
            existing_indices.add(i)
    
    return existing_indices

def generate_audio_batch(config):
    """批量生成音频文件"""
    print("=== 阿里云TTS音频批量生成 ===")
    print(f"生成范围: {config['START_INDEX']} - {config['END_INDEX']}")
    print(f"总计: {config['TOTAL_COUNT']} 个文件")
    print("=" * 60)
    
    # 1. 加载文本文件
    texts, pinyins = load_text_files(config)
    if texts is None:
        return False
    
    # 检查文件行数是否足够
    if len(texts) < config['END_INDEX']:
        print(f"✗ 文本文件行数不足: 需要{config['END_INDEX']}行，实际{len(texts)}行")
        return False
    
    # 2. 检查已存在的文件
    existing_files = get_existing_files(config)
    print(f"已存在音频文件: {len(existing_files)} 个")
    
    # 3. 计算需要生成的文件
    indices_to_generate = []
    for i in range(config['START_INDEX'], config['END_INDEX'] + 1):
        if i not in existing_files:
            indices_to_generate.append(i)
    
    if not indices_to_generate:
        print("✓ 所有文件已存在，无需生成")
        return True
    
    print(f"需要生成: {len(indices_to_generate)} 个文件")
    
    # 4. 确认生成
    try:
        confirm = input(f"是否开始生成 {len(indices_to_generate)} 个音频文件？(y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("已取消生成")
            return False
    except KeyboardInterrupt:
        print("\n程序被中断")
        return False
    
    # 5. 初始化TTS客户端
    try:
        tts = AliyunTTSBatch(config)
    except Exception as e:
        print(f"✗ 初始化TTS客户端失败: {e}")
        return False
    
    # 6. 开始生成
    success_count = 0
    error_count = 0
    
    csv_path = config["CSV_FILE"]
    csv_exists = os.path.exists(csv_path)
    
    print(f"\n开始生成音频文件...")
    start_time = time.time()
    
    # 写入CSV记录（制表符分隔格式）
    with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
        if not csv_exists:
            csvfile.write('音频名\t类型\t文本\t注音1\n')
    
        for i, index in enumerate(indices_to_generate, 1):
            # 获取文本和拼音
            text_index = index - 1  # 转换为数组索引
            text = texts[text_index]
            pinyin = pinyins[text_index] if text_index < len(pinyins) else ""
            audio_name = f"Sc{index:07d}"
            
            print(f"[{i}/{len(indices_to_generate)}] {audio_name}: {text[:30]}{'...' if len(text) > 30 else ''}")
            
            # 检查文件是否已存在（双重检查）
            audio_path = f"{audio_name}.wav"
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                continue
            
            # 生成音频（带重试）
            audio_data = None
            for retry in range(3):
                audio_data = tts.synthesize_text(text)
                if audio_data:
                    break
                if retry < 2:
                    print(f"  重试 {retry + 1}/3...")
                    time.sleep(2)
            
            if audio_data:
                try:
                    # 保存音频文件
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)
                    
                    # 写入CSV记录（制表符分隔格式）
                    csvfile.write(f"{audio_name}\tc\t{text}\t{pinyin}\n")
                    csvfile.flush()  # 立即写入文件
                    print(f"  ✓ 成功 ({len(audio_data)} 字节)")
                    success_count += 1
                    
                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1
            
            # 请求间隔
            time.sleep(config["REQUEST_INTERVAL"])
            
            # 每50个显示进度
            if i % 50 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(indices_to_generate) - i) * avg_time
                print(f"  进度: {i}/{len(indices_to_generate)}, 预计剩余: {remaining/60:.1f}分钟")
    
    elapsed_time = time.time() - start_time
    print(f"\n=== 生成完成 ===")
    print(f"耗时: {elapsed_time/60:.1f}分钟")
    print(f"成功: {success_count}")
    print(f"失败: {error_count}")
    print(f"CSV记录文件: {csv_path}")
    
    return error_count == 0

def main():
    """主函数"""
    print("=" * 60)
    print("阿里云TTS批量生成器 - 第7001-8000条")
    print("=" * 60)
    
    config = ALIYUN_CONFIG
    
    # 显示配置信息
    print("配置信息:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  拼音文件: {config['PINYIN_FILE']}")
    print(f"  生成范围: {config['START_INDEX']} - {config['END_INDEX']}")
    print(f"  输出目录: {config['OUTPUT_DIR']}")
    print(f"  CSV文件: {config['CSV_FILE']}")
    print(f"  语音类型: {config['VOICE']}")
    print("=" * 60)
    
    # 检查必要文件
    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return
    
    if not os.path.exists(config['PINYIN_FILE']):
        print(f"✗ 拼音文件不存在: {config['PINYIN_FILE']}")
        return
    
    # 开始生成
    success = generate_audio_batch(config)
    
    if success:
        print("\n🎉 音频生成任务完成！")
    else:
        print("\n⚠️ 音频生成任务未完全成功")
    
    # 最终统计
    final_existing = get_existing_files(config)
    print(f"\n最终统计:")
    print(f"  目标范围({config['START_INDEX']}-{config['END_INDEX']})内文件: {len(final_existing)}/{config['TOTAL_COUNT']}")
    
    if len(final_existing) == config['TOTAL_COUNT']:
        print("✓ 所有目标文件生成完成！")
    else:
        missing_count = config['TOTAL_COUNT'] - len(final_existing)
        print(f"⚠️ 还缺少 {missing_count} 个文件")

if __name__ == "__main__":
    main()
