#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
讯飞TTS修改版测试 - 验证文本索引映射逻辑
"""

import os

# ==================== 测试配置 ====================
TEST_CONFIG = {
    # 文件配置
    "TEXT_FILE": "../shuffled_from_rank_random.txt",
    "PINYIN_FILE": "../shuffled_from_rank_random_pinyin.txt",
    
    # 生成范围配置
    "START_INDEX": 10001,  # CSV中记录的文件名起始
    "END_INDEX": 11000,    # CSV中记录的文件名结束
    "TEXT_START_INDEX": 1, # 文本文件中的起始行（第1条）
    "TEXT_END_INDEX": 1000, # 文本文件中的结束行（第1000条）
    "TOTAL_COUNT": 1000
}

# 音色配置 - 5种音色，每种生成200条
VOICE_CONFIGS = [
    {"voice_type": "x4_xiaoyan", "name": "讯飞小燕", "start": 10001, "end": 10200},
    {"voice_type": "x4_yezi", "name": "讯飞小露", "start": 10201, "end": 10400},
    {"voice_type": "aisjiuxu", "name": "讯飞许久", "start": 10401, "end": 10600},
    {"voice_type": "aisjinger", "name": "讯飞小婧", "start": 10601, "end": 10800},
    {"voice_type": "aisbabyxu", "name": "讯飞许小宝", "start": 10801, "end": 11000},
]

def test_text_mapping():
    """测试文本索引映射逻辑"""
    print("=" * 60)
    print("讯飞TTS文本索引映射测试")
    print("=" * 60)
    
    config = TEST_CONFIG
    
    # 检查文件
    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return False
    
    # 读取文本文件
    try:
        with open(config['TEXT_FILE'], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 加载文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return False
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(config['PINYIN_FILE']):
        try:
            with open(config['PINYIN_FILE'], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 加载拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    print(f"\n配置信息:")
    print(f"  文件名范围: Sc{config['START_INDEX']:07d}.wav - Sc{config['END_INDEX']:07d}.wav")
    print(f"  使用文本: 第{config['TEXT_START_INDEX']}条 - 第{config['TEXT_END_INDEX']}条")
    print(f"  总数: {config['TOTAL_COUNT']} 个文件")
    
    print(f"\n音色配置:")
    for i, voice in enumerate(VOICE_CONFIGS, 1):
        count = voice["end"] - voice["start"] + 1
        print(f"  {i}. {voice['name']} ({voice['voice_type']}): Sc{voice['start']:07d}-Sc{voice['end']:07d} ({count}条)")
    
    print(f"\n=== 测试文本索引映射 ===")
    
    # 测试每个音色的前3个和后3个文件的文本映射
    for voice_config in VOICE_CONFIGS:
        voice_name = voice_config["name"]
        start_index = voice_config["start"]
        end_index = voice_config["end"]
        
        print(f"\n{voice_name} ({voice_config['voice_type']}):")
        
        # 测试前3个文件
        test_indices = [start_index, start_index + 1, start_index + 2, end_index - 2, end_index - 1, end_index]
        
        for index in test_indices:
            if index > end_index:
                continue
                
            # 计算对应的文本索引（使用第1-1000条文本）
            text_offset = index - config["START_INDEX"]  # 相对于起始位置的偏移
            text_index = config["TEXT_START_INDEX"] - 1 + text_offset  # 文本文件中的实际索引
            
            audio_name = f"Sc{index:07d}"
            
            if text_index < len(texts):
                text = texts[text_index]
                pinyin = pinyins[text_index] if text_index < len(pinyins) else ""
                print(f"  {audio_name}: 文本第{text_index + 1}行 -> {text[:30]}{'...' if len(text) > 30 else ''}")
            else:
                print(f"  {audio_name}: ✗ 文本索引超出范围 ({text_index + 1})")
    
    # 验证范围
    print(f"\n=== 验证范围 ===")
    
    # 第一个文件应该对应文本第1行
    first_index = config["START_INDEX"]
    first_text_offset = first_index - config["START_INDEX"]
    first_text_index = config["TEXT_START_INDEX"] - 1 + first_text_offset
    print(f"第一个文件 Sc{first_index:07d}: 应该使用文本第{first_text_index + 1}行")
    if first_text_index < len(texts):
        print(f"  实际文本: {texts[first_text_index][:50]}...")
    
    # 最后一个文件应该对应文本第1000行
    last_index = config["END_INDEX"]
    last_text_offset = last_index - config["START_INDEX"]
    last_text_index = config["TEXT_START_INDEX"] - 1 + last_text_offset
    print(f"最后一个文件 Sc{last_index:07d}: 应该使用文本第{last_text_index + 1}行")
    if last_text_index < len(texts):
        print(f"  实际文本: {texts[last_text_index][:50]}...")
    
    # 检查是否正确映射到1-1000行
    if first_text_index == 0 and last_text_index == 999:
        print(f"\n✅ 文本索引映射正确!")
        print(f"   Sc{config['START_INDEX']:07d}.wav -> 文本第1行")
        print(f"   Sc{config['END_INDEX']:07d}.wav -> 文本第1000行")
        return True
    else:
        print(f"\n❌ 文本索引映射错误!")
        print(f"   期望: 第1行 -> 第1000行")
        print(f"   实际: 第{first_text_index + 1}行 -> 第{last_text_index + 1}行")
        return False

def main():
    """主函数"""
    success = test_text_mapping()
    
    if success:
        print(f"\n🎉 文本索引映射测试成功!")
        print(f"可以运行修改后的主程序进行生成")
    else:
        print(f"\n⚠️ 文本索引映射测试失败，需要检查逻辑")

if __name__ == "__main__":
    main()
