# 豆包TTS多音色均衡生成器 - 确认版

## 🎯 功能特点

### ✅ 核心功能
- **多音色均衡分配**: 自动将文本按长度均匀分配给多个音色
- **长度完整覆盖**: 每种音色都包含2-30字的完整长度分布
- **智能确认机制**: 每种音色生成前后都有确认步骤
- **标准文件命名**: ScD0010000001.wav格式，符合要求
- **断点续传**: 支持中断后继续生成

### 🎛️ 确认机制
1. **生成前确认**: 每种音色开始前询问是否生成
2. **跳过功能**: 可以跳过不需要的音色
3. **继续确认**: 每种音色完成后询问是否继续
4. **中断保护**: 支持Ctrl+C安全中断

## 📁 文件结构

```
audio_output/
├── douyin_tts_multi_voice_balanced.py    # 主程序（已修改确认功能）
├── douyin_tts_confirm_test.py            # 确认功能测试程序
├── test_text_distribution.py             # 文本分配测试程序
├── protocols.py                          # WebSocket协议模块
├── README_多音色确认版.md                # 本说明文档
└── audio_output_balanced/                # 音频输出目录
    ├── ScD0010000001.wav                 # 音频文件
    ├── ScD0010000002.wav
    └── douyin_balanced_audio_log.csv     # 生成记录
```

## 🚀 使用方法

### 第一步：准备环境
确保有以下文件：
- `../shuffled_from_rank_random.txt` - 文本文件
- `../shuffled_from_rank_random_pinyin.txt` - 拼音文件
- `protocols.py` - WebSocket协议模块

### 第二步：运行程序
```bash
python douyin_tts_multi_voice_balanced.py
```

### 第三步：交互式确认
程序会依次询问：

1. **初始确认**
```
是否开始生成 X 种音色的音频？(y/n):
```

2. **每种音色生成前确认**
```
准备生成第 1/X 种音色:
  音色: D001 - 婉曲大叔
  文本数量: 3334 条
  预计字数: 104663 字
是否开始生成 婉曲大叔？(y/n):
```

3. **每种音色完成后确认**
```
✅ 婉曲大叔 生成完成
是否继续生成下一个音色？(y/n):
```

## 📊 输出格式

### 音频文件命名
- **格式**: `ScD{音色编码}{序号}.wav`
- **示例**: 
  - `ScD0010000001.wav` - D001音色第1个文件
  - `ScD0010000002.wav` - D001音色第2个文件
  - `ScD0020000001.wav` - D002音色第1个文件

### CSV记录格式
```csv
音频名	类型	文本	注音	音色
ScD0010000001	c	被使	bei4 shi3	婉曲大叔
ScD0010000002	c	上是	shang4 shi4	婉曲大叔
ScD0020000001	c	不我	bu4 wo3	黛梦传媒
```

## 🎵 音色配置

支持50种豆包音色，包括：

| 编码 | 音色类型 | 名称 |
|------|----------|------|
| D001 | zh_female_wanqudashu_moon_bigtts | 婉曲大叔 |
| D002 | zh_female_daimengchuanmei_moon_bigtts | 黛梦传媒 |
| D003 | zh_male_guozhoudege_moon_bigtts | 国州德哥 |
| ... | ... | ... |
| D050 | zh_female_yingtaowanzi_mars_bigtts | 樱桃丸子 |

## ⚙️ 配置参数

```python
DOUYIN_CONFIG = {
    "MIN_CHARS_PER_VOICE": 18000,  # 每种音色最少字数
    "MAX_CHARS_PER_VOICE": 24000,  # 每种音色最多字数
    "REQUEST_INTERVAL": 1.0,       # 请求间隔（秒）
}
```

## 📈 生成统计

程序会显示详细的生成统计：

```
批次 1 完成:
  耗时: 45.32分钟
  成功: 3334
  失败: 0
  实际生成字数: 104663
  成功率: 100.0%
  生成文件: ScD0010000001.wav - ScD0013334.wav
```

## 🔧 测试工具

### 1. 确认功能测试
```bash
python douyin_tts_confirm_test.py
```
- 模拟完整的确认流程
- 测试跳过和继续功能
- 验证交互逻辑

### 2. 文本分配测试
```bash
python test_text_distribution.py
```
- 验证文本分配算法
- 检查长度分布均衡性
- 分析音色分配结果

## 💡 使用建议

### 1. 分批生成
- 建议每次生成3-5种音色
- 避免一次性生成所有音色
- 可以根据需要跳过某些音色

### 2. 监控进度
- 程序每100个文件显示一次进度
- 注意观察成功率和错误信息
- 如有问题及时中断调整

### 3. 资源管理
- 确保网络连接稳定
- 预留足够的磁盘空间
- 避免在高峰期运行

## 🚨 注意事项

1. **网络要求**: 需要稳定的网络连接访问豆包API
2. **API限制**: 注意API调用频率限制
3. **存储空间**: 每种音色约需要1-2GB存储空间
4. **中断恢复**: 程序支持断点续传，重新运行会跳过已存在文件

## 📞 故障排除

### 常见问题

1. **文件不存在错误**
   - 检查文本文件路径是否正确
   - 确认文件编码为UTF-8

2. **WebSocket连接失败**
   - 检查网络连接
   - 验证API密钥是否有效

3. **音频生成失败**
   - 检查文本内容是否包含特殊字符
   - 尝试减少请求频率

### 调试模式
修改配置启用详细日志：
```python
logging.basicConfig(level=logging.DEBUG)
```

## 🎉 完成标志

当看到以下信息时，表示生成完成：
```
🎉 所有 X 种音色生成完成！

=== 最终统计 ===
总计生成文件: 33340
总计字数: 1046925 字
估算总时长: 43.62 小时
```

---

**版本**: 确认版 v1.0  
**更新时间**: 2025-07-29  
**作者**: Augment Agent
