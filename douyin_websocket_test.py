#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包语音WebSocket API测试程序
测试连接和基本功能
"""

import asyncio
import json
import logging
import uuid
import os
import websockets

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 测试配置
TEST_CONFIG = {
    "APPID": "9862368305",
    "ACCESS_TOKEN": "bbxNaAzRzBSkySjouDItXl6tBUUyNd2W",
    "ENDPOINT": "wss://openspeech.bytedance.com/api/v1/tts/ws_binary",
    "ENCODING": "wav",
}

def get_cluster(voice: str) -> str:
    """获取集群名称"""
    if voice.startswith("S_"):
        return "volcano_icl"
    return "volcano_tts"

async def test_websocket_connection():
    """测试WebSocket连接"""
    print("=== 豆包语音WebSocket连接测试 ===")
    
    config = TEST_CONFIG
    headers = {
        "Authorization": f"Bearer;{config['ACCESS_TOKEN']}",
    }
    
    try:
        print(f"连接到: {config['ENDPOINT']}")
        websocket = await websockets.connect(
            config["ENDPOINT"], 
            additional_headers=headers, 
            max_size=10 * 1024 * 1024
        )
        
        print("✓ WebSocket连接成功")
        
        try:
            # 测试发送一个简单的请求
            voice_type = "BV001_streaming"
            test_text = "这是一个测试文本"
            
            cluster = get_cluster(voice_type)
            
            request = {
                "app": {
                    "appid": config["APPID"],
                    "token": config["ACCESS_TOKEN"],
                    "cluster": cluster,
                },
                "user": {
                    "uid": str(uuid.uuid4()),
                },
                "audio": {
                    "voice_type": voice_type,
                    "encoding": config["ENCODING"],
                },
                "request": {
                    "reqid": str(uuid.uuid4()),
                    "text": test_text,
                    "operation": "submit",
                    "with_timestamp": "1",
                    "extra_param": json.dumps({
                        "disable_markdown_filter": False,
                    }),
                },
            }
            
            print(f"发送测试请求: {test_text}")
            await websocket.send(json.dumps(request))
            
            # 接收响应
            audio_data = bytearray()
            response_count = 0
            
            try:
                while response_count < 10:  # 最多接收10个响应
                    response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                    response_count += 1
                    
                    print(f"收到响应 {response_count}: {type(response)}, 大小: {len(response) if hasattr(response, '__len__') else 'N/A'}")
                    
                    if isinstance(response, bytes):
                        audio_data.extend(response)
                        print(f"  累计音频数据: {len(audio_data)} 字节")
                        
                        # 如果收到足够的音频数据，可以结束
                        if len(audio_data) > 1000:
                            print("  收到足够的音频数据，结束接收")
                            break
                    elif isinstance(response, str):
                        try:
                            json_resp = json.loads(response)
                            print(f"  JSON响应: {json_resp}")
                        except json.JSONDecodeError:
                            print(f"  文本响应: {response[:100]}...")
                    
            except asyncio.TimeoutError:
                print("  接收响应超时")
            
            if audio_data:
                # 保存测试音频
                test_file = "test_websocket_audio.wav"
                with open(test_file, "wb") as f:
                    f.write(audio_data)
                print(f"✓ 测试成功！音频已保存到: {test_file}")
                print(f"✓ 音频大小: {len(audio_data)} 字节")
                return True
            else:
                print("✗ 未收到音频数据")
                return False
                
        finally:
            await websocket.close()
            print("WebSocket连接已关闭")
            
    except websockets.exceptions.InvalidStatusCode as e:
        print(f"✗ WebSocket连接失败 - 状态码错误: {e}")
        return False
    except websockets.exceptions.ConnectionClosed as e:
        print(f"✗ WebSocket连接被关闭: {e}")
        return False
    except Exception as e:
        print(f"✗ WebSocket连接异常: {e}")
        return False

async def test_multiple_voices():
    """测试多种音色"""
    print("\n=== 测试多种音色 ===")
    
    test_voices = [
        "BV001_streaming",  # 通用女声
        "BV002_streaming",  # 通用男声
        "BV701_streaming",  # 擎苍
    ]
    
    for voice in test_voices:
        print(f"\n测试音色: {voice}")
        
        config = TEST_CONFIG
        headers = {
            "Authorization": f"Bearer;{config['ACCESS_TOKEN']}",
        }
        
        try:
            websocket = await websockets.connect(
                config["ENDPOINT"], 
                additional_headers=headers, 
                max_size=10 * 1024 * 1024
            )
            
            try:
                test_text = f"这是{voice}音色的测试"
                cluster = get_cluster(voice)
                
                request = {
                    "app": {
                        "appid": config["APPID"],
                        "token": config["ACCESS_TOKEN"],
                        "cluster": cluster,
                    },
                    "user": {
                        "uid": str(uuid.uuid4()),
                    },
                    "audio": {
                        "voice_type": voice,
                        "encoding": config["ENCODING"],
                    },
                    "request": {
                        "reqid": str(uuid.uuid4()),
                        "text": test_text,
                        "operation": "submit",
                        "with_timestamp": "1",
                        "extra_param": json.dumps({
                            "disable_markdown_filter": False,
                        }),
                    },
                }
                
                await websocket.send(json.dumps(request))
                
                # 简单接收测试
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    if isinstance(response, bytes) and len(response) > 0:
                        print(f"  ✓ {voice} 测试成功 ({len(response)} 字节)")
                    else:
                        print(f"  ✗ {voice} 测试失败 - 无音频数据")
                except asyncio.TimeoutError:
                    print(f"  ✗ {voice} 测试超时")
                    
            finally:
                await websocket.close()
                
        except Exception as e:
            print(f"  ✗ {voice} 测试异常: {e}")
        
        # 请求间隔
        await asyncio.sleep(1)

async def main():
    """主函数"""
    print("豆包语音WebSocket API测试程序")
    print("=" * 50)
    
    # 基本连接测试
    success = await test_websocket_connection()
    
    if success:
        print("\n🎉 基本连接测试成功！")
        
        # 多音色测试
        await test_multiple_voices()
        
        print("\n💡 测试完成！")
        print("如果测试成功，可以运行完整的1小时音频生成程序")
    else:
        print("\n⚠️ 基本连接测试失败")
        print("请检查网络连接和API配置")

if __name__ == "__main__":
    asyncio.run(main())
