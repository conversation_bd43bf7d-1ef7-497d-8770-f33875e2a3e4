#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频静音时间标准化工具
检测音频前后静音时间，并将其标准化到150ms-250ms范围内
"""

import os
import wave
import numpy as np
from pathlib import Path
import pandas as pd
from typing import Dict, List, Tuple, Optional
import warnings
import shutil
from datetime import datetime

warnings.filterwarnings('ignore')

# ==================== 配置参数 ====================
SILENCE_CONFIG = {
    # 静音检测参数
    "SILENCE_THRESHOLD": 0.01,  # 静音阈值（相对于最大振幅）
    "MIN_SILENCE_DURATION": 0.01,  # 最小静音持续时间（秒）
    
    # 目标静音时间范围
    "TARGET_SILENCE_MIN": 0.15,  # 150ms
    "TARGET_SILENCE_MAX": 0.25,  # 250ms
    "TARGET_SILENCE_DEFAULT": 0.2,  # 默认200ms
    
    # 处理参数
    "BACKUP_ORIGINAL": True,  # 是否备份原文件
    "BATCH_SIZE": 100,  # 批处理大小
    "SAMPLE_RATE": 16000,  # 标准采样率
}

class AudioSilenceNormalizer:
    """音频静音时间标准化器"""
    
    def __init__(self, config=None):
        self.config = config or SILENCE_CONFIG
        self.processed_count = 0
        self.error_count = 0
        self.backup_dir = None
        
    def detect_silence_boundaries(self, audio_data: np.ndarray, sample_rate: int) -> Tuple[int, int]:
        """
        检测音频开头和结尾的静音边界
        
        Args:
            audio_data: 音频数据数组
            sample_rate: 采样率
            
        Returns:
            (start_index, end_index): 非静音部分的开始和结束索引
        """
        if len(audio_data) == 0:
            return 0, 0
        
        # 计算静音阈值
        max_amplitude = np.max(np.abs(audio_data))
        silence_threshold = max_amplitude * self.config["SILENCE_THRESHOLD"]
        
        # 找到开头的非静音位置
        start_index = 0
        for i in range(len(audio_data)):
            if np.abs(audio_data[i]) > silence_threshold:
                start_index = i
                break
        
        # 找到结尾的非静音位置
        end_index = len(audio_data) - 1
        for i in range(len(audio_data) - 1, -1, -1):
            if np.abs(audio_data[i]) > silence_threshold:
                end_index = i
                break
        
        return start_index, end_index
    
    def calculate_silence_duration(self, silence_samples: int, sample_rate: int) -> float:
        """计算静音时长（秒）"""
        return silence_samples / sample_rate
    
    def generate_silence(self, duration_seconds: float, sample_rate: int, sample_width: int) -> np.ndarray:
        """
        生成指定时长的静音数据
        
        Args:
            duration_seconds: 静音时长（秒）
            sample_rate: 采样率
            sample_width: 样本宽度（字节）
            
        Returns:
            静音数据数组
        """
        silence_samples = int(duration_seconds * sample_rate)
        
        if sample_width == 1:
            return np.zeros(silence_samples, dtype=np.uint8) + 128  # 8位无符号
        elif sample_width == 2:
            return np.zeros(silence_samples, dtype=np.int16)  # 16位有符号
        elif sample_width == 4:
            return np.zeros(silence_samples, dtype=np.int32)  # 32位有符号
        else:
            return np.zeros(silence_samples, dtype=np.float32)  # 浮点
    
    def normalize_audio_silence(self, file_path: str) -> Dict:
        """
        标准化单个音频文件的静音时间
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            处理结果字典
        """
        try:
            # 读取原始音频文件
            with wave.open(file_path, 'rb') as wav_file:
                frames = wav_file.getnframes()
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()
                
                # 读取音频数据
                audio_data = wav_file.readframes(frames)
                
                # 转换为numpy数组
                if sample_width == 1:
                    dtype = np.uint8
                elif sample_width == 2:
                    dtype = np.int16
                elif sample_width == 4:
                    dtype = np.int32
                else:
                    dtype = np.float32
                
                audio_array = np.frombuffer(audio_data, dtype=dtype)
            
            # 检测静音边界
            start_index, end_index = self.detect_silence_boundaries(audio_array, sample_rate)
            
            # 计算原始静音时长
            original_start_silence = self.calculate_silence_duration(start_index, sample_rate)
            original_end_silence = self.calculate_silence_duration(len(audio_array) - 1 - end_index, sample_rate)
            
            # 提取非静音部分
            content_audio = audio_array[start_index:end_index + 1]
            
            # 确定目标静音时长
            target_silence = self.config["TARGET_SILENCE_DEFAULT"]
            
            # 生成标准化的静音
            start_silence = self.generate_silence(target_silence, sample_rate, sample_width)
            end_silence = self.generate_silence(target_silence, sample_rate, sample_width)
            
            # 合并音频：开头静音 + 内容 + 结尾静音
            normalized_audio = np.concatenate([start_silence, content_audio, end_silence])
            
            # 备份原文件（如果需要）
            if self.config["BACKUP_ORIGINAL"] and self.backup_dir:
                backup_path = os.path.join(self.backup_dir, os.path.basename(file_path))
                shutil.copy2(file_path, backup_path)
            
            # 写入标准化后的音频
            with wave.open(file_path, 'wb') as wav_file:
                wav_file.setnchannels(channels)
                wav_file.setsampwidth(sample_width)
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(normalized_audio.tobytes())
            
            # 计算处理结果
            new_duration = len(normalized_audio) / sample_rate
            original_duration = len(audio_array) / sample_rate
            
            result = {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'status': 'success',
                'original_duration': original_duration,
                'new_duration': new_duration,
                'original_start_silence': original_start_silence,
                'original_end_silence': original_end_silence,
                'target_silence': target_silence,
                'content_duration': len(content_audio) / sample_rate,
                'silence_normalized': True,
                'size_change': len(normalized_audio) - len(audio_array)
            }
            
            self.processed_count += 1
            return result
            
        except Exception as e:
            self.error_count += 1
            return {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'status': 'error',
                'error': str(e),
                'silence_normalized': False
            }
    
    def create_backup_directory(self, base_dir: str) -> str:
        """创建备份目录"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = os.path.join(base_dir, f"backup_silence_norm_{timestamp}")
        os.makedirs(backup_dir, exist_ok=True)
        return backup_dir
    
    def process_directory(self, directory: str, file_pattern: str = "*.wav") -> pd.DataFrame:
        """
        批量处理目录中的音频文件
        
        Args:
            directory: 音频文件目录
            file_pattern: 文件匹配模式
            
        Returns:
            处理结果DataFrame
        """
        # 获取所有匹配的音频文件
        audio_files = list(Path(directory).glob(file_pattern))
        
        if not audio_files:
            print(f"在目录 {directory} 中未找到匹配 {file_pattern} 的文件")
            return pd.DataFrame()
        
        print(f"找到 {len(audio_files)} 个音频文件")
        
        # 创建备份目录
        if self.config["BACKUP_ORIGINAL"]:
            self.backup_dir = self.create_backup_directory(directory)
            print(f"备份目录: {self.backup_dir}")
        
        # 批量处理
        results = []
        batch_size = self.config["BATCH_SIZE"]
        
        for i in range(0, len(audio_files), batch_size):
            batch_files = audio_files[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(audio_files) + batch_size - 1) // batch_size
            
            print(f"\n处理批次 {batch_num}/{total_batches} ({len(batch_files)} 个文件)")
            
            for j, file_path in enumerate(batch_files, 1):
                print(f"  [{j}/{len(batch_files)}] 处理: {file_path.name}")
                result = self.normalize_audio_silence(str(file_path))
                results.append(result)
                
                # 显示进度
                if result['status'] == 'success':
                    original_start = result['original_start_silence'] * 1000  # 转换为ms
                    original_end = result['original_end_silence'] * 1000
                    target = result['target_silence'] * 1000
                    print(f"    静音: {original_start:.0f}ms -> {target:.0f}ms (开头), {original_end:.0f}ms -> {target:.0f}ms (结尾)")
                else:
                    print(f"    ✗ 错误: {result['error']}")
        
        return pd.DataFrame(results)

def print_processing_summary(df: pd.DataFrame):
    """打印处理结果摘要"""
    if df.empty:
        print("没有处理结果")
        return
    
    print("\n" + "="*60)
    print("音频静音标准化处理摘要")
    print("="*60)
    
    # 统计结果
    success_df = df[df['status'] == 'success']
    error_df = df[df['status'] == 'error']
    
    print(f"总文件数: {len(df)}")
    print(f"处理成功: {len(success_df)}")
    print(f"处理失败: {len(error_df)}")
    
    if len(success_df) > 0:
        print(f"\n静音标准化统计:")
        print(f"目标静音时长: {success_df['target_silence'].iloc[0] * 1000:.0f}ms")
        print(f"平均原始开头静音: {success_df['original_start_silence'].mean() * 1000:.1f}ms")
        print(f"平均原始结尾静音: {success_df['original_end_silence'].mean() * 1000:.1f}ms")
        
        print(f"\n时长变化:")
        print(f"平均原始时长: {success_df['original_duration'].mean():.2f}秒")
        print(f"平均新时长: {success_df['new_duration'].mean():.2f}秒")
        
        # 统计需要调整的文件
        start_out_of_range = success_df[
            (success_df['original_start_silence'] < 0.15) | 
            (success_df['original_start_silence'] > 0.25)
        ]
        end_out_of_range = success_df[
            (success_df['original_end_silence'] < 0.15) | 
            (success_df['original_end_silence'] > 0.25)
        ]
        
        print(f"\n需要调整的文件:")
        print(f"开头静音超出范围: {len(start_out_of_range)} 个")
        print(f"结尾静音超出范围: {len(end_out_of_range)} 个")
    
    if len(error_df) > 0:
        print(f"\n处理失败的文件:")
        for _, row in error_df.iterrows():
            print(f"  {row['file_name']}: {row['error']}")

def main():
    """主函数"""
    print("=" * 60)
    print("音频静音时间标准化工具")
    print("=" * 60)
    
    # 配置参数
    config = SILENCE_CONFIG.copy()
    
    print("配置参数:")
    print(f"  静音阈值: {config['SILENCE_THRESHOLD']}")
    print(f"  目标静音范围: {config['TARGET_SILENCE_MIN']*1000:.0f}-{config['TARGET_SILENCE_MAX']*1000:.0f}ms")
    print(f"  默认静音时长: {config['TARGET_SILENCE_DEFAULT']*1000:.0f}ms")
    print(f"  是否备份原文件: {config['BACKUP_ORIGINAL']}")
    
    # 获取用户输入
    directory = input("\n请输入音频文件目录路径 (默认为当前目录): ").strip()
    if not directory:
        directory = "."
    
    if not os.path.exists(directory):
        print(f"错误: 目录 {directory} 不存在")
        return
    
    # 确认处理
    try:
        confirm = input(f"是否开始处理目录 {directory} 中的音频文件？(y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("已取消处理")
            return
    except KeyboardInterrupt:
        print("\n程序被中断")
        return
    
    # 创建处理器并开始处理
    normalizer = AudioSilenceNormalizer(config)
    
    print(f"\n开始处理音频文件...")
    start_time = datetime.now()
    
    # 处理文件
    results_df = normalizer.process_directory(directory)
    
    end_time = datetime.now()
    processing_time = (end_time - start_time).total_seconds()
    
    if not results_df.empty:
        # 保存处理结果
        result_file = f"silence_normalization_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        results_df.to_csv(result_file, index=False, encoding='utf-8-sig')
        print(f"\n处理结果已保存到: {result_file}")
        
        # 打印摘要
        print_processing_summary(results_df)
        
        print(f"\n处理完成!")
        print(f"总耗时: {processing_time:.1f}秒")
        print(f"处理速度: {len(results_df)/processing_time:.1f} 文件/秒")
    else:
        print("未找到可处理的音频文件")

if __name__ == "__main__":
    main()
