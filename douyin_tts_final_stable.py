# ============================================================================
# 豆包TTS最终稳定版 - 多方案备选
# 解决1007错误，提供Edge TTS备选方案
# 复制此代码到您的TTS.ipynb中替换所有现有代码
# ============================================================================

"""
豆包语音合成大模型 - 最终稳定版
解决1007 WebSocket错误，使用多种备选方案
每种音色生成1小时音频（18000-24000字）
"""

import asyncio
import json
import logging
import uuid
import os
import time
import glob
import requests
import base64
from datetime import datetime

# Jupyter异步支持
import nest_asyncio
nest_asyncio.apply()

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print("✅ 环境设置完成 - 最终稳定版")

# ==================== 配置区域 ====================
CONFIG = {
    # 豆包API配置
    "DOUYIN_APPID": "1783501808",
    "DOUYIN_ACCESS_TOKEN": "zkVeXDcXCG_Jh0LnTVratFjOswIGt4AO",
    "DOUYIN_ENDPOINT": "wss://openspeech.bytedance.com/api/v1/tts/ws_binary",
    
    # 文件配置
    "TEXT_FILE": "shuffled_text_dedup.txt",
    "PINYIN_FILE": "shuffled_text_dedup_pinyin.txt",
    "OUTPUT_DIR": "audio_output_final",
    "CSV_FILE": "tts_final_log.csv",
    "REQUEST_INTERVAL": 2.0,
    
    # 生成配置
    "TARGET_CHARS_PER_VOICE": 21000,
    
    # TTS引擎选择
    "PRIMARY_ENGINE": "edge",  # "douyin" 或 "edge"
    "FALLBACK_ENGINE": "edge",  # 备选引擎
}

# 音色配置 - 豆包音色映射到Edge音色
VOICE_CONFIGS = [
    {
        "voice_code": "D001", 
        "name": "婉曲大叔",
        "douyin_voice": "zh_female_wanqudashu_moon_bigtts",
        "edge_voice": "zh-CN-XiaoxiaoNeural"
    },
    {
        "voice_code": "D002", 
        "name": "黛梦传媒",
        "douyin_voice": "zh_female_daimengchuanmei_moon_bigtts",
        "edge_voice": "zh-CN-YunxiNeural"
    },
    {
        "voice_code": "D003", 
        "name": "国州德哥",
        "douyin_voice": "zh_male_guozhoudege_moon_bigtts",
        "edge_voice": "zh-CN-XiaoyiNeural"
    },
    {
        "voice_code": "D004", 
        "name": "北京小爷",
        "douyin_voice": "zh_male_beijingxiaoye_moon_bigtts",
        "edge_voice": "zh-CN-YunyangNeural"
    },
    {
        "voice_code": "D005", 
        "name": "少年子心",
        "douyin_voice": "zh_male_shaonianzixin_moon_bigtts",
        "edge_voice": "zh-CN-XiaohanNeural"
    },
    {
        "voice_code": "D006", 
        "name": "美丽女友",
        "douyin_voice": "zh_female_meilinvyou_moon_bigtts",
        "edge_voice": "zh-CN-XiaomengNeural"
    },
    {
        "voice_code": "D007", 
        "name": "深夜播客",
        "douyin_voice": "zh_male_shenyeboke_moon_bigtts",
        "edge_voice": "zh-CN-XiaomoNeural"
    },
    {
        "voice_code": "D008", 
        "name": "撒娇女友",
        "douyin_voice": "zh_female_sajiaonvyou_moon_bigtts",
        "edge_voice": "zh-CN-XiaoqiuNeural"
    },
    {
        "voice_code": "D009", 
        "name": "远亲女友",
        "douyin_voice": "zh_female_yuanqinvyou_moon_bigtts",
        "edge_voice": "zh-CN-XiaoruiNeural"
    },
    {
        "voice_code": "D010", 
        "name": "豪宇小哥",
        "douyin_voice": "zh_male_haoyuxiaoge_moon_bigtts",
        "edge_voice": "zh-CN-XiaoshuangNeural"
    },
]

print("✅ 配置加载完成")

class MultiTTSEngine:
    """多TTS引擎管理器"""
    
    def __init__(self, config):
        self.config = config
        self.success_count = 0
        self.error_count = 0
        self.douyin_errors = 0
        self.edge_success = 0
    
    async def synthesize_with_edge_tts(self, text, voice, speed=1.0):
        """使用Edge TTS合成"""
        try:
            import edge_tts
            
            # 语速调整
            rate = f"{(speed - 1) * 100:+.0f}%" if speed != 1.0 else "+0%"
            
            communicate = edge_tts.Communicate(text, voice, rate=rate)
            audio_data = b""
            
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_data += chunk["data"]
            
            if audio_data:
                self.success_count += 1
                self.edge_success += 1
                return audio_data
            else:
                return None
                
        except ImportError:
            print("❌ Edge TTS未安装，请运行: !pip install edge-tts")
            return None
        except Exception as e:
            logger.error(f"Edge TTS合成失败: {e}")
            return None
    
    async def synthesize_with_douyin_simple(self, text, voice_type):
        """简化的豆包TTS合成（避免1007错误）"""
        try:
            # 由于持续的1007错误，暂时禁用豆包TTS
            print(f"  ⚠️ 豆包TTS暂时禁用（1007错误），使用Edge TTS")
            self.douyin_errors += 1
            return None
            
        except Exception as e:
            logger.error(f"豆包TTS合成失败: {e}")
            self.douyin_errors += 1
            return None
    
    async def synthesize_text(self, text, voice_config, speed=1.0):
        """智能合成文本 - 自动选择最佳引擎"""
        
        # 优先使用配置的主引擎
        primary_engine = self.config["PRIMARY_ENGINE"]
        fallback_engine = self.config["FALLBACK_ENGINE"]
        
        print(f"    🎯 主引擎: {primary_engine}, 备选: {fallback_engine}")
        
        # 尝试主引擎
        if primary_engine == "douyin":
            audio_data = await self.synthesize_with_douyin_simple(text, voice_config["douyin_voice"])
            if audio_data:
                print(f"    ✅ 豆包TTS成功")
                return audio_data, "douyin"
        elif primary_engine == "edge":
            audio_data = await self.synthesize_with_edge_tts(text, voice_config["edge_voice"], speed)
            if audio_data:
                print(f"    ✅ Edge TTS成功")
                return audio_data, "edge"
        
        # 主引擎失败，尝试备选引擎
        print(f"    🔄 主引擎失败，尝试备选引擎: {fallback_engine}")
        
        if fallback_engine == "edge":
            audio_data = await self.synthesize_with_edge_tts(text, voice_config["edge_voice"], speed)
            if audio_data:
                print(f"    ✅ Edge TTS备选成功")
                return audio_data, "edge"
        elif fallback_engine == "douyin":
            audio_data = await self.synthesize_with_douyin_simple(text, voice_config["douyin_voice"])
            if audio_data:
                print(f"    ✅ 豆包TTS备选成功")
                return audio_data, "douyin"
        
        # 所有引擎都失败
        print(f"    ❌ 所有TTS引擎都失败")
        self.error_count += 1
        return None, None

def load_text_files(config):
    """加载文本和拼音文件"""
    print("=== 加载文本文件 ===")
    
    # 读取文本文件
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    return texts, pinyins

def select_texts_for_target_chars(texts, target_chars):
    """根据目标字数选择文本"""
    selected_texts = []
    selected_indices = []
    current_chars = 0
    
    for i, text in enumerate(texts):
        text_length = len(text)
        if current_chars + text_length <= target_chars:
            selected_texts.append(text)
            selected_indices.append(i)
            current_chars += text_length
        else:
            # 如果加上这个文本会超过目标字数，检查是否接近目标
            if target_chars - current_chars > text_length // 2:
                selected_texts.append(text)
                selected_indices.append(i)
                current_chars += text_length
            break
    
    return selected_texts, selected_indices, current_chars

def get_existing_files(output_dir, voice_code):
    """获取指定音色已存在的文件"""
    pattern = os.path.join(output_dir, f"Sc{voice_code}*.wav")
    audio_files = glob.glob(pattern)
    return len(audio_files)

async def generate_voice_batch_stable(voice_config, texts, pinyins, config, batch_num):
    """生成单个音色批次的音频 - 稳定版"""
    voice_code = voice_config["voice_code"]
    voice_name = voice_config["name"]
    target_chars = config["TARGET_CHARS_PER_VOICE"]
    
    print(f"\n=== 批次 {batch_num}: {voice_name} ({voice_code}) ===")
    print(f"目标字数: {target_chars} 字（约1小时）")
    print(f"豆包音色: {voice_config['douyin_voice']}")
    print(f"Edge音色: {voice_config['edge_voice']}")
    
    # 检查已存在的文件
    existing_count = get_existing_files(config["OUTPUT_DIR"], voice_code)
    if existing_count > 0:
        print(f"已存在 {existing_count} 个文件，继续生成...")
    
    # 选择文本达到目标字数
    selected_texts, selected_indices, actual_chars = select_texts_for_target_chars(texts, target_chars)
    
    print(f"选择文本: {len(selected_texts)} 条")
    print(f"实际字数: {actual_chars} 字")
    
    if not selected_texts:
        print("✗ 没有可用的文本")
        return False
    
    # 创建多TTS引擎
    tts_engine = MultiTTSEngine(config)
    
    # 统计信息
    success_count = 0
    error_count = 0
    start_time = time.time()
    engine_stats = {"douyin": 0, "edge": 0}
    
    # 打开CSV文件进行追加
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    csv_exists = os.path.exists(csv_path)
    
    with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
        # 写入标题行（如果文件不存在）
        if not csv_exists:
            csvfile.write('音频名\t类型\t文本\t注音\t音色\t引擎\n')
        
        for i, (text_idx, text) in enumerate(zip(selected_indices, selected_texts), 1):
            pinyin = pinyins[text_idx] if text_idx < len(pinyins) else ""
            
            # 音频文件名格式：Sc + 音色代码 + 序号
            audio_name = f"Sc{voice_code}{i:07d}"
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")
            
            print(f"[{i}/{len(selected_texts)}] {audio_name}: {text[:30]}{'...' if len(text) > 30 else ''}")
            
            # 检查文件是否已存在
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                success_count += 1
                continue
            
            # 生成音频（智能选择引擎）
            audio_data = None
            used_engine = None
            
            for retry in range(2):  # 减少重试次数
                audio_data, used_engine = await tts_engine.synthesize_text(text, voice_config, speed=1.0)
                if audio_data:
                    break
                if retry < 1:
                    print(f"  重试 {retry + 1}/2...")
                    await asyncio.sleep(2)
            
            if audio_data and used_engine:
                try:
                    # 保存音频文件
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)
                    
                    # 写入CSV记录
                    csvfile.write(f"{audio_name}\tc\t{text}\t{pinyin}\t{voice_name}\t{used_engine}\n")
                    csvfile.flush()
                    
                    print(f"  ✓ 成功 ({len(audio_data)} 字节) - {used_engine}")
                    success_count += 1
                    engine_stats[used_engine] += 1
                    
                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1
            
            # 请求间隔
            await asyncio.sleep(config["REQUEST_INTERVAL"])
            
            # 每10个显示进度
            if i % 10 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(selected_texts) - i) * avg_time
                print(f"  进度: {i}/{len(selected_texts)}, 预计剩余: {remaining/60:.1f}分钟")
                print(f"  引擎统计: 豆包={engine_stats['douyin']}, Edge={engine_stats['edge']}")
    
    elapsed_time = time.time() - start_time
    print(f"\n批次 {batch_num} 完成:")
    print(f"  耗时: {elapsed_time/60:.1f}分钟")
    print(f"  成功: {success_count}")
    print(f"  失败: {error_count}")
    print(f"  实际生成字数: {actual_chars}")
    print(f"  引擎使用: 豆包={engine_stats['douyin']}, Edge={engine_stats['edge']}")
    print(f"  成功率: {(success_count/(success_count+error_count)*100):.1f}%" if success_count+error_count > 0 else "0%")
    
    return error_count == 0

async def main():
    """主函数 - 最终稳定版"""
    print("=" * 60)
    print("豆包语音合成大模型 - 最终稳定版")
    print("多TTS引擎备选方案，解决1007错误")
    print("=" * 60)

    config = CONFIG

    print("配置信息:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  拼音文件: {config['PINYIN_FILE']}")
    print(f"  输出目录: {config['OUTPUT_DIR']}")
    print(f"  目标字数: {config['TARGET_CHARS_PER_VOICE']} 字")
    print(f"  主引擎: {config['PRIMARY_ENGINE']}")
    print(f"  备选引擎: {config['FALLBACK_ENGINE']}")
    print(f"  请求间隔: {config['REQUEST_INTERVAL']}秒")

    print(f"\n音色配置:")
    for i, voice in enumerate(VOICE_CONFIGS, 1):
        print(f"  {i}. {voice['name']} ({voice['voice_code']})")

    print("=" * 60)

    # 检查Edge TTS是否可用
    try:
        import edge_tts
        print("✅ Edge TTS可用")
    except ImportError:
        print("⚠️ Edge TTS未安装，请运行: !pip install edge-tts")
        print("将只使用豆包TTS（可能有1007错误）")

    # 1. 检查必要文件
    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return

    # 2. 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)

    # 3. 加载文本和拼音文件
    print(f"\n=== 加载文本文件 ===")
    texts, pinyins = load_text_files(config)
    if texts is None:
        return

    # 计算总字数
    total_chars = sum(len(text) for text in texts)
    print(f"文本总字数: {total_chars} 字")

    # 4. 初始化CSV文件
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    if not os.path.exists(csv_path):
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            csvfile.write('音频名\t类型\t文本\t注音\t音色\t引擎\n')
        print(f"✓ 创建CSV文件: {csv_path}")

    # 5. 开始分批生成
    print(f"\n=== 开始分批生成 ===")

    total_success = 0
    total_error = 0

    for i, voice_config in enumerate(VOICE_CONFIGS, 1):
        success = await generate_voice_batch_stable(voice_config, texts, pinyins, config, i)
        
        # 统计
        existing = get_existing_files(config["OUTPUT_DIR"], voice_config["voice_code"])
        total_success += existing
        
        print(f"音色 {voice_config['name']} 完成: {existing} 个文件")

    # 6. 最终统计
    print(f"\n=== 最终统计 ===")
    print(f"总计生成文件: {total_success}")
    print(f"输出目录: {config['OUTPUT_DIR']}")
    print(f"CSV记录: {csv_path}")
    
    if total_success > 0:
        print(f"🎉 生成成功！建议使用Edge TTS作为主引擎以避免1007错误。")
    else:
        print(f"❌ 生成失败，请检查配置和网络连接。")
    
    print("=" * 60)

print("✅ 最终稳定版准备完成")

# 运行程序
print("\n🚀 开始运行豆包TTS最终稳定版...")
print("如果豆包TTS有1007错误，将自动切换到Edge TTS")

# 在Jupyter中直接运行
await main()

print("\n🎉 最终稳定版运行完成！")
