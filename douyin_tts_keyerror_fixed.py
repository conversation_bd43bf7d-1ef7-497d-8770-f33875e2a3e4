# ============================================================================
# 豆包TTS KeyError修复版本
# 修复 TARGET_CHARS_PER_VOICE 键错误
# 复制此代码到您的TTS.ipynb中替换现有代码
# ============================================================================

"""
豆包语音合成大模型WebSocket API调用程序 - 多音色均衡版
每种音色生成1小时音频（18000-24000字），文本长度从2-30均匀分布
"""

import asyncio
import json
import logging
import uuid
import os
import time
import glob
from datetime import datetime
import websockets
from protocols import MsgType, full_client_request, receive_message

# Jupyter异步支持
import nest_asyncio
nest_asyncio.apply()

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print("✅ 环境设置完成 - KeyError修复版")

# ==================== 配置区域 ====================
DOUYIN_CONFIG = {
    # API配置
    "APPID": "1783501808",
    "ACCESS_TOKEN": "zkVeXDcXCG_Jh0LnTVratFjOswIGt4AO",
    
    # WebSocket配置
    "ENDPOINT": "wss://openspeech.bytedance.com/api/v1/tts/ws_binary",
    
    # 音频格式配置
    "ENCODING": "wav",
    
    # 文件配置
    "TEXT_FILE": "shuffled_text_dedup.txt",
    "PINYIN_FILE": "shuffled_text_dedup_pinyin.txt",
    "OUTPUT_DIR": "audio_output_balanced",
    "CSV_FILE": "douyin_balanced_audio_log.csv",
    "REQUEST_INTERVAL": 2.0,  # WebSocket请求间隔（增加到2秒）
    
    # 生成配置 - 修复键名
    "MIN_CHARS_PER_VOICE": 18000,  # 每种音色最少字数
    "MAX_CHARS_PER_VOICE": 24000,  # 每种音色最多字数
    "TARGET_CHARS_PER_VOICE": 21000,  # 每种音色目标字数（中间值）
}

# 豆包音色配置 - 50种音色
VOICE_CONFIGS = [
    {"voice_code": "D001", "voice_type": "zh_female_wanqudashu_moon_bigtts", "name": "婉曲大叔"},
    {"voice_code": "D002", "voice_type": "zh_female_daimengchuanmei_moon_bigtts", "name": "黛梦传媒"},
    {"voice_code": "D003", "voice_type": "zh_male_guozhoudege_moon_bigtts", "name": "国州德哥"},
    {"voice_code": "D004", "voice_type": "zh_male_beijingxiaoye_moon_bigtts", "name": "北京小爷"},
    {"voice_code": "D005", "voice_type": "zh_male_shaonianzixin_moon_bigtts", "name": "少年子心"},
    {"voice_code": "D006", "voice_type": "zh_female_meilinvyou_moon_bigtts", "name": "美丽女友"},
    {"voice_code": "D007", "voice_type": "zh_male_shenyeboke_moon_bigtts", "name": "深夜播客"},
    {"voice_code": "D008", "voice_type": "zh_female_sajiaonvyou_moon_bigtts", "name": "撒娇女友"},
    {"voice_code": "D009", "voice_type": "zh_female_yuanqinvyou_moon_bigtts", "name": "远亲女友"},
    {"voice_code": "D010", "voice_type": "zh_male_haoyuxiaoge_moon_bigtts", "name": "豪宇小哥"},
    {"voice_code": "D011", "voice_type": "zh_male_guangxiyuanzhou_moon_bigtts", "name": "广西远州"},
    {"voice_code": "D012", "voice_type": "zh_female_meituojieer_moon_bigtts", "name": "美托姐儿"},
    {"voice_code": "D013", "voice_type": "zh_male_yuzhouzixuan_moon_bigtts", "name": "宇宙子轩"},
    {"voice_code": "D014", "voice_type": "zh_female_linjianvhai_moon_bigtts", "name": "邻家女孩"},
    {"voice_code": "D015", "voice_type": "zh_female_gaolengyujie_moon_bigtts", "name": "高冷御姐"},
    {"voice_code": "D016", "voice_type": "zh_male_yuanboxiaoshu_moon_bigtts", "name": "渊博小叔"},
    {"voice_code": "D017", "voice_type": "zh_male_yangguangqingnian_moon_bigtts", "name": "阳光青年"},
    {"voice_code": "D018", "voice_type": "zh_male_aojiaobazong_moon_bigtts", "name": "傲娇霸总"},
    {"voice_code": "D019", "voice_type": "zh_male_jingqiangkanye_moon_bigtts", "name": "京腔侃爷"},
    {"voice_code": "D020", "voice_type": "zh_female_shuangkuaisisi_moon_bigtts", "name": "爽快思思"},
    {"voice_code": "D021", "voice_type": "zh_male_wennuanahu_moon_bigtts", "name": "温暖阿虎"},
    {"voice_code": "D022", "voice_type": "zh_female_wanwanxiaohe_moon_bigtts", "name": "婉婉小荷"},
    {"voice_code": "D023", "voice_type": "ICL_zh_female_bingruoshaonv_tob", "name": "冰若少女"},
    {"voice_code": "D024", "voice_type": "ICL_zh_female_huoponvhai_tob", "name": "活泼女孩"},
    {"voice_code": "D025", "voice_type": "ICL_zh_female_heainainai_tob", "name": "和蔼奶奶"},
    {"voice_code": "D026", "voice_type": "ICL_zh_female_linjuayi_tob", "name": "邻居阿姨"},
    {"voice_code": "D027", "voice_type": "zh_female_wenrouxiaoya_moon_bigtts", "name": "温柔小雅"},
    {"voice_code": "D028", "voice_type": "zh_female_tianmeixiaoyuan_moon_bigtts", "name": "甜美小园"},
    {"voice_code": "D029", "voice_type": "zh_female_qingchezizi_moon_bigtts", "name": "清澈紫紫"},
    {"voice_code": "D030", "voice_type": "zh_male_dongfanghaoran_moon_bigtts", "name": "东方浩然"},
    {"voice_code": "D031", "voice_type": "zh_male_jieshuoxiaoming_moon_bigtts", "name": "解说小明"},
    {"voice_code": "D032", "voice_type": "zh_female_kailangjiejie_moon_bigtts", "name": "开朗姐姐"},
    {"voice_code": "D033", "voice_type": "zh_male_linjiananhai_moon_bigtts", "name": "邻家男孩"},
    {"voice_code": "D034", "voice_type": "zh_female_tianmeiyueyue_moon_bigtts", "name": "甜美月月"},
    {"voice_code": "D035", "voice_type": "zh_female_xinlingjitang_moon_bigtts", "name": "心灵鸡汤"},
    {"voice_code": "D036", "voice_type": "zh_female_cancan_mars_bigtts", "name": "灿灿"},
    {"voice_code": "D037", "voice_type": "zh_male_tiancaitongsheng_mars_bigtts", "name": "天才童声"},
    {"voice_code": "D038", "voice_type": "zh_male_naiqimengwa_mars_bigtts", "name": "奶气萌娃"},
    {"voice_code": "D039", "voice_type": "zh_male_sunwukong_mars_bigtts", "name": "孙悟空"},
    {"voice_code": "D040", "voice_type": "zh_male_xionger_mars_bigtts", "name": "熊二"},
    {"voice_code": "D041", "voice_type": "zh_female_peiqi_mars_bigtts", "name": "佩奇"},
    {"voice_code": "D042", "voice_type": "zh_female_zhixingnvsheng_mars_bigtts", "name": "知性女声"},
    {"voice_code": "D043", "voice_type": "zh_female_qingxinnvsheng_mars_bigtts", "name": "清新女声"},
    {"voice_code": "D044", "voice_type": "zh_male_changtianyi_mars_bigtts", "name": "长天一"},
    {"voice_code": "D045", "voice_type": "zh_female_popo_mars_bigtts", "name": "婆婆"},
    {"voice_code": "D046", "voice_type": "zh_female_wuzetian_mars_bigtts", "name": "武则天"},
    {"voice_code": "D047", "voice_type": "zh_female_shaoergushi_mars_bigtts", "name": "少儿故事"},
    {"voice_code": "D048", "voice_type": "zh_male_silang_mars_bigtts", "name": "四郎"},
    {"voice_code": "D049", "voice_type": "zh_female_gujie_mars_bigtts", "name": "古姐"},
    {"voice_code": "D050", "voice_type": "zh_female_yingtaowanzi_mars_bigtts", "name": "樱桃丸子"},
]

print("✅ 配置加载完成")

def get_cluster(voice: str) -> str:
    """获取集群名称"""
    if voice.startswith("ICL_"):
        return "volcano_icl"
    return "volcano_tts"

class DouyinTTSWebSocketFixed:
    """豆包TTS WebSocket修复版"""
    
    def __init__(self, config):
        self.config = config
        self.headers = {
            "Authorization": f"Bearer;{config['ACCESS_TOKEN']}",
        }
        self.success_count = 0
        self.error_count = 0
    
    async def connect_websocket_compatible(self):
        """兼容不同版本websockets库的连接方法"""
        endpoint = self.config["ENDPOINT"]
        
        # 方法1: 尝试新版本的additional_headers
        try:
            logger.info(f"尝试方法1: additional_headers")
            websocket = await websockets.connect(
                endpoint, 
                additional_headers=self.headers, 
                max_size=10 * 1024 * 1024
            )
            logger.info(f"✅ 方法1成功")
            return websocket
        except TypeError as e:
            if "additional_headers" in str(e):
                logger.info(f"方法1失败: {e}")
            else:
                raise e
        
        # 方法2: 尝试旧版本的extra_headers
        try:
            logger.info(f"尝试方法2: extra_headers")
            websocket = await websockets.connect(
                endpoint, 
                extra_headers=self.headers, 
                max_size=10 * 1024 * 1024
            )
            logger.info(f"✅ 方法2成功")
            return websocket
        except TypeError as e:
            if "extra_headers" in str(e):
                logger.info(f"方法2失败: {e}")
            else:
                raise e
        
        # 方法3: 尝试只设置max_size
        try:
            logger.info(f"尝试方法3: 只设置max_size")
            websocket = await websockets.connect(
                endpoint, 
                max_size=10 * 1024 * 1024
            )
            logger.info(f"✅ 方法3成功")
            return websocket
        except Exception as e:
            logger.info(f"方法3失败: {e}")
        
        # 方法4: 最基础的连接
        try:
            logger.info(f"尝试方法4: 基础连接")
            websocket = await websockets.connect(endpoint)
            logger.info(f"✅ 方法4成功")
            return websocket
        except Exception as e:
            logger.error(f"所有连接方法都失败: {e}")
            raise e

    async def synthesize_text_compatible(self, text, voice_type):
        """兼容性合成文本方法"""
        try:
            # 确定集群
            cluster = get_cluster(voice_type)

            # 连接到服务器（使用兼容性方法）
            logger.info(f"连接到 {self.config['ENDPOINT']}")
            websocket = await self.connect_websocket_compatible()
            
            try:
                # 获取连接信息
                try:
                    logid = websocket.response.headers.get('x-tt-logid', 'N/A')
                    logger.info(f"已连接到WebSocket服务器, Logid: {logid}")
                except:
                    logger.info(f"已连接到WebSocket服务器")

                # 准备请求负载
                request = {
                    "app": {
                        "appid": self.config["APPID"],
                        "token": self.config["ACCESS_TOKEN"],
                        "cluster": cluster,
                    },
                    "user": {
                        "uid": str(uuid.uuid4()),
                    },
                    "audio": {
                        "voice_type": voice_type,
                        "encoding": self.config["ENCODING"],
                    },
                    "request": {
                        "reqid": str(uuid.uuid4()),
                        "text": text,
                        "operation": "submit",
                        "with_timestamp": "1",
                        "extra_param": json.dumps({
                            "disable_markdown_filter": False,
                        }),
                    },
                }

                # 发送请求
                await full_client_request(websocket, json.dumps(request).encode())

                # 接收音频数据
                audio_data = bytearray()
                while True:
                    msg = await receive_message(websocket)

                    if msg.type == MsgType.FrontEndResultServer:
                        continue
                    elif msg.type == MsgType.AudioOnlyServer:
                        audio_data.extend(msg.payload)
                        if msg.sequence < 0:  # 最后一条消息
                            break
                    elif msg.type == MsgType.ErrorServer:
                        # 解析错误消息
                        try:
                            error_data = json.loads(msg.payload.decode('utf-8'))
                            error_msg = error_data.get('message', '未知错误')
                            error_code = error_data.get('code', 'N/A')
                            raise RuntimeError(f"服务器错误 [{error_code}]: {error_msg}")
                        except json.JSONDecodeError:
                            error_text = msg.payload.decode('utf-8', errors='ignore')
                            raise RuntimeError(f"服务器错误: {error_text}")
                    else:
                        raise RuntimeError(f"未知消息类型: {msg.type}")

                # 检查是否收到音频数据
                if not audio_data:
                    raise RuntimeError("未收到音频数据")

                self.success_count += 1
                return bytes(audio_data)

            finally:
                await websocket.close()
                
        except Exception as e:
            logger.error(f"豆包TTS合成失败: {e}")
            self.error_count += 1
            return None
        
def load_text_files(config):
    """加载文本和拼音文件"""
    print("=== 加载文本文件 ===")
    
    # 读取文本文件
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    return texts, pinyins

def select_texts_for_target_chars(texts, target_chars):
    """根据目标字数选择文本"""
    selected_texts = []
    selected_indices = []
    current_chars = 0
    
    for i, text in enumerate(texts):
        text_length = len(text)
        if current_chars + text_length <= target_chars:
            selected_texts.append(text)
            selected_indices.append(i)
            current_chars += text_length
        else:
            # 如果加上这个文本会超过目标字数，检查是否接近目标
            if target_chars - current_chars > text_length // 2:
                selected_texts.append(text)
                selected_indices.append(i)
                current_chars += text_length
            break
    
    return selected_texts, selected_indices, current_chars

def get_existing_files(output_dir, voice_code):
    """获取指定音色已存在的文件"""
    pattern = os.path.join(output_dir, f"Sc{voice_code}*.wav")
    audio_files = glob.glob(pattern)
    return len(audio_files)

async def generate_voice_batch_fixed(voice_config, texts, pinyins, config, batch_num):
    """生成单个音色批次的音频 - 修复版"""
    voice_type = voice_config["voice_type"]
    voice_name = voice_config["name"]
    voice_code = voice_config["voice_code"]  # 修复：使用voice_code而不是code
    target_chars = config["TARGET_CHARS_PER_VOICE"]  # 现在这个键存在了
    
    print(f"\n=== 批次 {batch_num}: {voice_name} ({voice_type}) ===")
    print(f"音色代码: {voice_code}")
    print(f"目标字数: {target_chars} 字（约1小时）")
    
    # 检查已存在的文件
    existing_count = get_existing_files(config["OUTPUT_DIR"], voice_code)
    if existing_count > 0:
        print(f"已存在 {existing_count} 个文件，继续生成...")
    
    # 选择文本达到目标字数
    selected_texts, selected_indices, actual_chars = select_texts_for_target_chars(texts, target_chars)
    
    print(f"选择文本: {len(selected_texts)} 条")
    print(f"实际字数: {actual_chars} 字")
    
    if not selected_texts:
        print("✗ 没有可用的文本")
        return False
    
    # Jupyter环境中自动生成
    print(f"自动生成 {voice_name}")
    
    # 创建TTS对象
    tts = DouyinTTSWebSocketFixed(config)
    
    # 统计信息
    success_count = 0
    error_count = 0
    start_time = time.time()
    
    # 打开CSV文件进行追加
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    csv_exists = os.path.exists(csv_path)
    
    with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
        # 写入标题行（如果文件不存在）
        if not csv_exists:
            csvfile.write('音频名\t类型\t文本\t注音\t音色\n')
        
        for i, (text_idx, text) in enumerate(zip(selected_indices, selected_texts), 1):
            pinyin = pinyins[text_idx] if text_idx < len(pinyins) else ""
            
            # 音频文件名格式：Sc + 音色代码 + 序号
            audio_name = f"Sc{voice_code}{i:07d}"
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")
            
            print(f"[{i}/{len(selected_texts)}] {audio_name}: {text[:30]}{'...' if len(text) > 30 else ''}")
            
            # 检查文件是否已存在
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                success_count += 1
                continue
            
            # 生成音频（使用兼容性方法，带重试）
            audio_data = None
            for retry in range(3):
                audio_data = await tts.synthesize_text_compatible(text, voice_type)
                if audio_data:
                    break
                if retry < 2:
                    print(f"  重试 {retry + 1}/3...")
                    await asyncio.sleep(3)
            
            if audio_data:
                try:
                    # 保存音频文件
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)
                    
                    # 写入CSV记录
                    csvfile.write(f"{audio_name}\tc\t{text}\t{pinyin}\t{voice_name}\n")
                    csvfile.flush()
                    
                    print(f"  ✓ 成功 ({len(audio_data)} 字节)")
                    success_count += 1
                    
                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1
            
            # 请求间隔
            await asyncio.sleep(config["REQUEST_INTERVAL"])
            
            # 每10个显示进度
            if i % 10 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(selected_texts) - i) * avg_time
                print(f"  进度: {i}/{len(selected_texts)}, 预计剩余: {remaining/60:.1f}分钟")
    
    elapsed_time = time.time() - start_time
    print(f"\n批次 {batch_num} 完成:")
    print(f"  耗时: {elapsed_time/60:.1f}分钟")
    print(f"  成功: {success_count}")
    print(f"  失败: {error_count}")
    print(f"  实际生成字数: {actual_chars}")
    print(f"  成功率: {(success_count/(success_count+error_count)*100):.1f}%" if success_count+error_count > 0 else "0%")
    
    return error_count == 0

async def main():
    """主函数 - KeyError修复版"""
    print("=" * 60)
    print("豆包语音合成大模型WebSocket API - KeyError修复版")
    print("修复TARGET_CHARS_PER_VOICE键错误和voice_code字段问题")
    print("=" * 60)

    config = DOUYIN_CONFIG

    print("配置信息:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  拼音文件: {config['PINYIN_FILE']}")
    print(f"  输出目录: {config['OUTPUT_DIR']}")
    print(f"  目标字数: {config['TARGET_CHARS_PER_VOICE']} 字")
    print(f"  字数范围: {config['MIN_CHARS_PER_VOICE']}-{config['MAX_CHARS_PER_VOICE']} 字")
    print(f"  请求间隔: {config['REQUEST_INTERVAL']}秒")

    print(f"\n音色配置 (前10个):")
    for i, voice in enumerate(VOICE_CONFIGS[:10], 1):
        print(f"  {i}. {voice['name']} ({voice['voice_code']}) - {voice['voice_type']}")
    print(f"  ... 总共 {len(VOICE_CONFIGS)} 种音色")

    print("=" * 60)

    # 1. 检查必要文件
    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return

    if not os.path.exists(config['PINYIN_FILE']):
        print(f"⚠️ 拼音文件不存在: {config['PINYIN_FILE']}")
        print("将继续处理，但拼音字段将为空")

    # 2. 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)

    # 3. 加载文本和拼音文件
    print(f"\n=== 加载文本文件 ===")
    texts, pinyins = load_text_files(config)
    if texts is None:
        return

    # 计算总字数
    total_chars = sum(len(text) for text in texts)
    print(f"文本总字数: {total_chars} 字")

    # 4. 初始化CSV文件
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    if not os.path.exists(csv_path):
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            csvfile.write('音频名\t类型\t文本\t注音\t音色\n')
        print(f"✓ 创建CSV文件: {csv_path}")

    # 5. 开始分批生成
    print(f"\n=== 开始分批生成 ===")

    for i, voice_config in enumerate(VOICE_CONFIGS, 1):
        success = await generate_voice_batch_fixed(voice_config, texts, pinyins, config, i)
        if not success:
            print(f"⚠️ {voice_config['name']}生成失败，可能部分文件未完成")
        
        # 自动继续下一个音色
        print(f"自动继续下一个音色")

    # 6. 最终统计
    print(f"\n=== 最终统计 ===")

    total_generated = 0
    for voice in VOICE_CONFIGS:
        existing = get_existing_files(config["OUTPUT_DIR"], voice["voice_code"])
        total_generated += existing
        print(f"{voice['name']}: {existing} 个文件")

    print(f"\n总计生成文件: {total_generated}")
    print(f"输出目录: {config['OUTPUT_DIR']}")
    print(f"CSV记录: {csv_path}")
    print("=" * 60)

print("✅ KeyError修复版准备完成")

# 运行程序
print("\n🚀 开始运行豆包TTS KeyError修复版...")
await main()

print("\n🎉 KeyError修复版运行完成！")
