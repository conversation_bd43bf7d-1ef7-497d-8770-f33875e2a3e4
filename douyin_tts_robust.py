#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包TTS多音色生成 - 网络优化版
专门解决WebSocket连接错误和网络稳定性问题
"""

import asyncio
import json
import logging
import uuid
import os
import time
import random
from datetime import datetime
import websockets
from protocols import MsgType, full_client_request, receive_message, is_audio_message, is_final_message, extract_audio_data

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 网络优化配置
ROBUST_CONFIG = {
    # API配置
    "APPID": "9862368305",
    "ACCESS_TOKEN": "bbxNaAzRzBSkySjouDItXl6tBUUyNd2W",
    
    # WebSocket配置
    "ENDPOINT": "wss://openspeech.bytedance.com/api/v1/tts/ws_binary",
    
    # 音频格式配置
    "ENCODING": "wav",
    
    # 文件配置
    "TEXT_FILE": "../shuffled_from_rank_random.txt",
    "PINYIN_FILE": "../shuffled_from_rank_random_pinyin.txt",
    "OUTPUT_DIR": "audio_output_robust",
    "CSV_FILE": "douyin_robust_audio_log.csv",
    
    # 网络优化配置
    "REQUEST_INTERVAL": 3.0,        # 请求间隔增加到3秒
    "RETRY_INTERVAL": 5.0,          # 重试间隔5秒
    "MAX_RETRIES": 5,               # 最大重试次数
    "CONNECTION_TIMEOUT": 30,       # 连接超时30秒
    "PING_INTERVAL": 30,            # Ping间隔30秒
    "PING_TIMEOUT": 10,             # Ping超时10秒
    "CLOSE_TIMEOUT": 15,            # 关闭超时15秒
    
    # 生成配置
    "MIN_CHARS_PER_VOICE": 18000,
    "MAX_CHARS_PER_VOICE": 24000,
}

# 测试音色配置（先用3种音色测试）
TEST_VOICE_CONFIGS = [
    {"voice_code": "D001", "voice_type": "zh_female_wanqudashu_moon_bigtts", "name": "婉曲大叔"},
    {"voice_code": "D002", "voice_type": "zh_female_daimengchuanmei_moon_bigtts", "name": "黛梦传媒"},
    {"voice_code": "D003", "voice_type": "zh_male_guozhoudege_moon_bigtts", "name": "国州德哥"},
]

def get_cluster(voice: str) -> str:
    """获取集群名称"""
    if voice.startswith("ICL_"):
        return "volcano_icl"
    return "volcano_tts"

class RobustDouyinTTS:
    """网络优化的豆包TTS类"""
    
    def __init__(self, config):
        self.config = config
        self.headers = {
            "Authorization": f"Bearer;{config['ACCESS_TOKEN']}",
        }
        self.success_count = 0
        self.error_count = 0
    
    async def synthesize_text_with_backoff(self, text, voice_type):
        """带指数退避的文本合成"""
        max_retries = self.config["MAX_RETRIES"]
        base_delay = self.config["RETRY_INTERVAL"]
        
        for attempt in range(max_retries):
            try:
                logger.info(f"尝试合成文本 (第 {attempt + 1}/{max_retries} 次): {text[:30]}...")
                
                # 创建WebSocket连接
                websocket = await asyncio.wait_for(
                    websockets.connect(
                        self.config["ENDPOINT"],
                        additional_headers=self.headers,
                        max_size=10 * 1024 * 1024,
                        ping_interval=self.config["PING_INTERVAL"],
                        ping_timeout=self.config["PING_TIMEOUT"],
                        close_timeout=self.config["CLOSE_TIMEOUT"]
                    ),
                    timeout=self.config["CONNECTION_TIMEOUT"]
                )
                
                try:
                    # 确定集群
                    cluster = get_cluster(voice_type)
                    
                    # 准备请求
                    request = {
                        "app": {
                            "appid": self.config["APPID"],
                            "token": self.config["ACCESS_TOKEN"],
                            "cluster": cluster,
                        },
                        "user": {
                            "uid": str(uuid.uuid4()),
                        },
                        "audio": {
                            "voice_type": voice_type,
                            "encoding": self.config["ENCODING"],
                        },
                        "request": {
                            "reqid": str(uuid.uuid4()),
                            "text": text,
                            "operation": "submit",
                            "with_timestamp": "1",
                            "extra_param": json.dumps({
                                "disable_markdown_filter": False,
                            }),
                        },
                    }
                    
                    # 发送请求
                    await full_client_request(websocket, json.dumps(request).encode())
                    
                    # 接收音频数据
                    audio_data = bytearray()
                    timeout_count = 0
                    max_timeout = 10  # 最多等待10次消息超时
                    
                    while True:
                        try:
                            # 设置接收消息超时
                            msg = await asyncio.wait_for(receive_message(websocket), timeout=10.0)
                            timeout_count = 0  # 重置超时计数
                            
                            if msg.type == MsgType.FrontEndResultServer:
                                continue
                            elif is_audio_message(msg):
                                audio_chunk = extract_audio_data(msg)
                                audio_data.extend(audio_chunk)
                                if is_final_message(msg):
                                    break
                            elif msg.type == MsgType.ErrorServer:
                                error_msg = msg.payload.decode()
                                raise RuntimeError(f"服务器错误: {error_msg}")
                            else:
                                logger.warning(f"未知消息类型: {msg.type}")
                                
                        except asyncio.TimeoutError:
                            timeout_count += 1
                            logger.warning(f"接收消息超时 ({timeout_count}/{max_timeout})")
                            if timeout_count >= max_timeout:
                                raise RuntimeError("接收消息超时次数过多")
                    
                    # 检查音频数据
                    if not audio_data:
                        raise RuntimeError("未收到音频数据")
                    
                    logger.info(f"合成成功，音频大小: {len(audio_data)} 字节")
                    self.success_count += 1
                    return bytes(audio_data)
                    
                finally:
                    try:
                        await websocket.close()
                    except Exception as e:
                        logger.warning(f"关闭WebSocket连接失败: {e}")
                        
            except Exception as e:
                self.error_count += 1
                logger.error(f"合成失败 (第 {attempt + 1}/{max_retries} 次): {e}")
                
                if attempt < max_retries - 1:
                    # 指数退避：每次重试等待时间翻倍，加上随机抖动
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                    logger.info(f"等待 {delay:.1f} 秒后重试...")
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"所有重试失败，放弃合成文本: {text[:50]}")
                    return None
        
        return None
    
    def get_stats(self):
        """获取统计信息"""
        total = self.success_count + self.error_count
        success_rate = (self.success_count / total * 100) if total > 0 else 0
        return {
            "success": self.success_count,
            "error": self.error_count,
            "total": total,
            "success_rate": success_rate
        }

async def test_robust_synthesis():
    """测试网络优化的合成功能"""
    print("=" * 60)
    print("豆包TTS网络优化测试")
    print("=" * 60)
    
    config = ROBUST_CONFIG
    
    # 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)
    
    # 加载测试文本
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()][:50]  # 只测试前50条
        print(f"✓ 加载测试文本: {len(texts)} 条")
    except Exception as e:
        print(f"✗ 加载文本失败: {e}")
        return
    
    # 创建TTS对象
    tts = RobustDouyinTTS(config)
    
    # 选择一个音色进行测试
    voice_config = TEST_VOICE_CONFIGS[0]
    voice_type = voice_config["voice_type"]
    voice_code = voice_config["voice_code"]
    voice_name = voice_config["name"]
    
    print(f"\n测试音色: {voice_name} ({voice_code})")
    print(f"测试文本数量: {len(texts)}")
    print(f"网络配置:")
    print(f"  请求间隔: {config['REQUEST_INTERVAL']} 秒")
    print(f"  最大重试: {config['MAX_RETRIES']} 次")
    print(f"  连接超时: {config['CONNECTION_TIMEOUT']} 秒")
    
    # 确认开始测试
    try:
        confirm = input(f"\n是否开始网络优化测试？(y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("测试取消")
            return
    except KeyboardInterrupt:
        print("\n测试被中断")
        return
    
    print(f"\n=== 开始测试 ===")
    start_time = time.time()
    
    # 创建CSV文件
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        csvfile.write('音频名\t类型\t文本\t注音\t音色\n')
        
        for i, text in enumerate(texts, 1):
            print(f"\n[{i}/{len(texts)}] 处理文本: {text[:30]}{'...' if len(text) > 30 else ''}")
            
            # 音频文件名
            audio_name = f"Sc{voice_code}{i:07d}"
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")
            
            # 检查文件是否已存在
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                continue
            
            # 合成音频
            audio_data = await tts.synthesize_text_with_backoff(text, voice_type)
            
            if audio_data:
                try:
                    # 保存音频文件
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)
                    
                    # 写入CSV记录
                    csvfile.write(f"{audio_name}\tc\t{text}\t\t{voice_name}\n")
                    csvfile.flush()
                    
                    print(f"  ✅ 成功保存: {audio_name}.wav ({len(audio_data)} 字节)")
                    
                except Exception as e:
                    print(f"  ❌ 保存失败: {e}")
            else:
                print(f"  ❌ 合成失败")
            
            # 显示统计信息
            stats = tts.get_stats()
            print(f"  统计: 成功 {stats['success']}, 失败 {stats['error']}, 成功率 {stats['success_rate']:.1f}%")
            
            # 请求间隔
            if i < len(texts):  # 不是最后一个
                print(f"  等待 {config['REQUEST_INTERVAL']} 秒...")
                await asyncio.sleep(config["REQUEST_INTERVAL"])
    
    # 最终统计
    elapsed_time = time.time() - start_time
    stats = tts.get_stats()
    
    print(f"\n=== 测试完成 ===")
    print(f"总耗时: {elapsed_time/60:.2f} 分钟")
    print(f"成功: {stats['success']} 个")
    print(f"失败: {stats['error']} 个")
    print(f"成功率: {stats['success_rate']:.1f}%")
    print(f"平均每个文件耗时: {elapsed_time/len(texts):.1f} 秒")
    
    if stats['success_rate'] >= 80:
        print("🎉 网络优化效果良好！")
    else:
        print("⚠️ 仍有网络问题，建议进一步调整参数")

def main():
    """主函数"""
    asyncio.run(test_robust_synthesis())

if __name__ == "__main__":
    main()
