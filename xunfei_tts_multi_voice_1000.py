#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
讯飞TTS多音色生成器 - 生成1000条音频文件
使用5种不同音色，每种音色生成200条
"""

import websocket
import datetime
import hashlib
import base64
import hmac
import json
from urllib.parse import urlencode
import time
import ssl
from wsgiref.handlers import format_date_time
from datetime import datetime
from time import mktime
import _thread as thread
import os
import wave
import csv
import argparse

# ==================== 配置区域 ====================
XUNFEI_CONFIG = {
    # API配置 
    "APPID": "fee2aa97",           # 应用ID
    "APISecret": "YWYzODMwN2ExNjhlMDBmN2ExMzE5MWQz",   # API密钥
    "APIKey": "a4727f9636df58ee310a31d2b857361a",         # API Key
     
    # 语音参数配置
    "SPEED": "50",                    # 语速 (0-100)
    "VOLUME": "80",                   # 音量 (0-100)
    "PITCH": "50",                    # 音调 (0-100)
    
    # 文件配置
    "TEXT_FILE": "../shuffled_from_rank_random.txt",
    "PINYIN_FILE": "../shuffled_from_rank_random_pinyin.txt",
    "OUTPUT_DIR": "audio_output",
    "CSV_FILE": "xunfei_audio_info_1000.csv",
    "REQUEST_INTERVAL": 0.5,          # 请求间隔(秒)
    
    # 生成范围配置
    "START_INDEX": 10001,  # CSV中记录的文件名起始
    "END_INDEX": 11000,    # CSV中记录的文件名结束
    "TEXT_START_INDEX": 1, # 文本文件中的起始行（第1条）
    "TEXT_END_INDEX": 1000, # 文本文件中的结束行（第1000条）
    "TOTAL_COUNT": 1000
}

# 音色配置 - 5种音色，每种生成200条
VOICE_CONFIGS = [
    {"voice_type": "x4_xiaoyan", "name": "讯飞小燕", "start": 10001, "end": 10200},
    {"voice_type": "x4_yezi", "name": "讯飞小露", "start": 10201, "end": 10400},
    {"voice_type": "aisjiuxu", "name": "讯飞许久", "start": 10401, "end": 10600},
    {"voice_type": "aisjinger", "name": "讯飞小婧", "start": 10601, "end": 10800},
    {"voice_type": "aisbabyxu", "name": "讯飞许小宝", "start": 10801, "end": 11000},
]

# 备选音色（如果需要更多音色可以使用）
AVAILABLE_VOICES = [
    {"voice_type": "x4_xiaoyan", "name": "讯飞小燕"},
    {"voice_type": "x4_yezi", "name": "讯飞小露"},
    {"voice_type": "aisjiuxu", "name": "讯飞许久"},
    {"voice_type": "aisjinger", "name": "讯飞小婧"},
    {"voice_type": "aisbabyxu", "name": "讯飞许小宝"},
]

class XunfeiTTSMultiVoice:
    """讯飞TTS多音色核心类"""
    
    def __init__(self, config):
        self.config = config
        self.audio_data = b""
        self.status = 0  # 0: 初始, 1: 连接成功, 2: 完成, -1: 错误
        self.host = "ws-api.xfyun.cn"
        self.path = "/v2/tts"
        self.url = f"wss://{self.host}{self.path}"
        
    def create_url(self):
        """生成带鉴权的WebSocket URL"""
        now = datetime.now()
        date = format_date_time(mktime(now.timetuple()))
        
        signature_origin = f"host: {self.host}\n"
        signature_origin += f"date: {date}\n"
        signature_origin += f"GET {self.path} HTTP/1.1"
        
        signature_sha = hmac.new(
            self.config["APISecret"].encode('utf-8'),
            signature_origin.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')
        
        authorization_origin = f'api_key="{self.config["APIKey"]}", algorithm="hmac-sha256", headers="host date request-line", signature="{signature_sha}"'
        authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode(encoding='utf-8')
        
        v = {
            "authorization": authorization,
            "date": date,
            "host": self.host
        }
        
        return self.url + '?' + urlencode(v)
    
    def on_message(self, ws, message):
        """WebSocket消息回调"""
        try:
            message = json.loads(message)
            code = message["code"]
            
            if code != 0:
                print(f"  ✗ API错误: {message['message']} (code: {code})")
                self.status = -1
            else:
                data = message.get("data")
                if data:
                    audio = data["audio"]
                    audio_data = base64.b64decode(audio)
                    self.audio_data += audio_data
                    
                    if data["status"] == 2:  # 传输完成
                        self.status = 2
                        ws.close()
        except Exception as e:
            print(f"  ✗ 消息处理错误: {e}")
            self.status = -1
    
    def on_error(self, ws, error):
        """WebSocket错误回调"""
        print(f"  ✗ WebSocket错误: {error}")
        self.status = -1
    
    def on_close(self, ws, close_status_code, close_msg):
        """WebSocket关闭回调"""
        pass
    
    def on_open(self, ws):
        """WebSocket连接成功回调"""
        def run(*args):
            data = {
                "common": {"app_id": self.config["APPID"]},
                "business": {
                    "aue": "raw",
                    "auf": "audio/L16;rate=16000",
                    "vcn": self.voice_name,
                    "speed": self.config["SPEED"],
                    "volume": self.config["VOLUME"],
                    "pitch": self.config["PITCH"],
                    "tte": "UTF8"
                },
                "data": {
                    "status": 2,
                    "text": base64.b64encode(self.text.encode('utf-8')).decode('utf-8')
                }
            }
            ws.send(json.dumps(data))
        
        thread.start_new_thread(run, ())
        self.status = 1
    
    def synthesize_text(self, text, voice_name, timeout=30):
        """合成单个文本"""
        self.text = text
        self.voice_name = voice_name
        self.audio_data = b""
        self.status = 0
        
        websocket.enableTrace(False)
        ws_url = self.create_url()
        ws = websocket.WebSocketApp(
            ws_url,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close,
            on_open=self.on_open
        )
        
        ws.run_forever(sslopt={"cert_reqs": ssl.CERT_NONE})
        
        start_time = time.time()
        while self.status not in [2, -1] and (time.time() - start_time) < timeout:
            time.sleep(0.1)
        
        return self.audio_data if self.status == 2 else None

def save_audio_as_wav(audio_data, output_path, sample_rate=16000):
    """保存PCM音频数据为WAV文件"""
    with wave.open(output_path, 'wb') as wav_file:
        wav_file.setnchannels(1)  # 单声道
        wav_file.setsampwidth(2)  # 16位
        wav_file.setframerate(sample_rate)  # 采样率
        wav_file.writeframes(audio_data)

def get_existing_files(output_dir, start_index, end_index):
    """获取已存在的音频文件列表"""
    existing = []
    for i in range(start_index, end_index + 1):
        audio_path = os.path.join(output_dir, f"Sc{i:07d}.wav")
        if os.path.exists(audio_path):
            existing.append(i)
    return existing

def generate_voice_batch(voice_config, texts, pinyins, config, batch_num):
    """生成单个音色批次的音频"""
    voice_type = voice_config["voice_type"]
    voice_name = voice_config["name"]
    start_index = voice_config["start"]
    end_index = voice_config["end"]
    
    print(f"\n=== 批次 {batch_num}: {voice_name} ({voice_type}) ===")
    print(f"范围: {start_index} - {end_index}")
    
    # 检查已存在的文件
    existing_files = get_existing_files(config["OUTPUT_DIR"], start_index, end_index)
    target_count = end_index - start_index + 1
    
    if len(existing_files) == target_count:
        print(f"✓ 该批次已完成，跳过 ({len(existing_files)}/{target_count})")
        return True
    
    print(f"已存在: {len(existing_files)}/{target_count} 个文件")
    
    # 确定需要生成的文件
    indices_to_generate = []
    for i in range(start_index, end_index + 1):
        if i not in existing_files:
            indices_to_generate.append(i)
    
    if not indices_to_generate:
        print("✓ 无需生成新文件")
        return True
    
    print(f"需要生成: {len(indices_to_generate)} 个文件")
    
    # 创建TTS对象
    tts = XunfeiTTSMultiVoice(config)
    
    # 统计信息
    success_count = 0
    error_count = 0
    start_time = time.time()
    
    # 打开CSV文件进行追加
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    with open(csv_path, 'a', newline='', encoding='utf-8-sig') as csvfile:
        
        for i, index in enumerate(indices_to_generate, 1):
            # 计算对应的文本索引（使用第1-1000条文本）
            text_offset = index - config["START_INDEX"]  # 相对于起始位置的偏移
            text_index = config["TEXT_START_INDEX"] - 1 + text_offset  # 文本文件中的实际索引

            text = texts[text_index] if text_index < len(texts) else ""
            pinyin = pinyins[text_index] if text_index < len(pinyins) else ""
            audio_name = f"Sc{index:07d}"
            
            print(f"[{i}/{len(indices_to_generate)}] {audio_name}: {text[:30]}{'...' if len(text) > 30 else ''}")
            
            # 检查文件是否已存在（双重检查）
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                continue
            
            # 生成音频（带重试）
            audio_data = None
            for retry in range(3):
                audio_data = tts.synthesize_text(text, voice_type)
                if audio_data:
                    break
                if retry < 2:
                    print(f"  重试 {retry + 1}/3...")
                    time.sleep(2)
            
            if audio_data:
                try:
                    # 保存音频文件
                    save_audio_as_wav(audio_data, audio_path)
                    
                    # 写入CSV记录（标准CSV格式，包含音色信息）
                    writer = csv.writer(csvfile, quoting=csv.QUOTE_ALL)
                    writer.writerow([audio_name, 'c', text, pinyin, voice_name])
                    csvfile.flush()  # 立即写入文件
                    
                    print(f"  ✓ 成功 ({len(audio_data)} 字节)")
                    success_count += 1
                    
                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1
            
            # 请求间隔
            time.sleep(config["REQUEST_INTERVAL"])
            
            # 每50个显示进度
            if i % 50 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(indices_to_generate) - i) * avg_time
                print(f"  进度: {i}/{len(indices_to_generate)}, 预计剩余: {remaining/60:.1f}分钟")
    
    elapsed_time = time.time() - start_time
    print(f"\n批次 {batch_num} 完成:")
    print(f"  耗时: {elapsed_time:.1f}秒")
    print(f"  成功: {success_count}")
    print(f"  失败: {error_count}")
    
    return error_count == 0

def validate_config(config):
    """验证配置"""
    errors = []
    if config["APPID"] == "your_app_id":
        errors.append("请配置正确的APPID")
    if config["APISecret"] == "your_api_secret":
        errors.append("请配置正确的APISecret")
    if config["APIKey"] == "your_api_key":
        errors.append("请配置正确的APIKey")
    return errors

def main():
    """主函数"""
    print("=" * 60)
    print("讯飞TTS多音色生成器 - 1000条音频")
    print("=" * 60)

    config = XUNFEI_CONFIG

    print("配置信息:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  拼音文件: {config['PINYIN_FILE']}")
    print(f"  输出目录: {config['OUTPUT_DIR']}")
    print(f"  文件名范围: Sc{config['START_INDEX']:07d}.wav - Sc{config['END_INDEX']:07d}.wav")
    print(f"  使用文本: 第{config['TEXT_START_INDEX']}条 - 第{config['TEXT_END_INDEX']}条")
    print(f"  总数: {config['TOTAL_COUNT']} 个文件")
    print(f"  请求间隔: {config['REQUEST_INTERVAL']}秒")

    print(f"\n音色配置:")
    for i, voice in enumerate(VOICE_CONFIGS, 1):
        count = voice["end"] - voice["start"] + 1
        print(f"  {i}. {voice['name']} ({voice['voice_type']}): {voice['start']}-{voice['end']} ({count}条)")

    print("=" * 60)

    # 1. 验证配置
    errors = validate_config(config)
    if errors:
        print("配置错误:")
        for error in errors:
            print(f"  ✗ {error}")
        return

    # 2. 检查必要文件
    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return

    if not os.path.exists(config['PINYIN_FILE']):
        print(f"⚠️ 拼音文件不存在: {config['PINYIN_FILE']}")
        print("将继续处理，但拼音字段将为空")

    # 3. 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)

    # 4. 读取文本和拼音文件
    print(f"\n=== 加载文本文件 ===")
    try:
        with open(config['TEXT_FILE'], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return

    pinyins = []
    if os.path.exists(config['PINYIN_FILE']):
        try:
            with open(config['PINYIN_FILE'], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")

    # 检查文件行数是否足够
    if len(texts) < config['TEXT_END_INDEX']:
        print(f"✗ 文本文件行数不足: 需要{config['TEXT_END_INDEX']}行，实际{len(texts)}行")
        return

    # 5. 初始化CSV文件
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    if not os.path.exists(csv_path):
        with open(csv_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile, quoting=csv.QUOTE_ALL)
            writer.writerow(['音频名', '类型', '文本', '注音1', '音色'])
        print(f"✓ 创建CSV文件: {csv_path}")

    # 6. 开始分批生成
    print(f"\n=== 开始多音色分批生成 ===")

    for i, voice_config in enumerate(VOICE_CONFIGS, 1):
        success = generate_voice_batch(voice_config, texts, pinyins, config, i)
        if not success:
            print("生成被中断或失败")
            break

    # 7. 最终统计
    print(f"\n=== 最终统计 ===")

    total_generated = 0
    for voice in VOICE_CONFIGS:
        existing = get_existing_files(config["OUTPUT_DIR"], voice["start"], voice["end"])
        generated = len(existing)
        total_generated += generated
        target = voice["end"] - voice["start"] + 1
        status = "✓完成" if generated == target else f"{generated}/{target}"
        print(f"{voice['name']}: {status}")

    print(f"\n目标范围({config['START_INDEX']}-{config['END_INDEX']})内文件: {total_generated}/{config['TOTAL_COUNT']}")

    if total_generated == config['TOTAL_COUNT']:
        print("🎉 所有目标文件生成完成！")
    else:
        missing = config['TOTAL_COUNT'] - total_generated
        print(f"⚠️ 还缺少 {missing} 个文件")

    print(f"\n输出目录: {config['OUTPUT_DIR']}")
    print(f"CSV记录: {csv_path}")
    print("=" * 60)

if __name__ == "__main__":
    main()
