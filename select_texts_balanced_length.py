# ============================================================================
# 修改后的文本选择函数 - 长度均匀分布版本
# 确保每种音色都有文本长度从2-30字符的均匀分布
# ============================================================================

def select_texts_for_target_chars(texts, target_chars):
    """
    根据目标字数选择文本，确保文本长度从2-30字符均匀分布
    
    Args:
        texts: 文本列表
        target_chars: 目标字符数
    
    Returns:
        selected_texts: 选中的文本列表
        selected_indices: 选中文本的原始索引
        current_chars: 实际选中的字符数
    """
    import random
    
    # 按文本长度分组
    length_groups = {}
    for i, text in enumerate(texts):
        text_length = len(text)
        # 只考虑长度在2-30之间的文本
        if 2 <= text_length <= 30:
            if text_length not in length_groups:
                length_groups[text_length] = []
            length_groups[text_length].append((i, text))
    
    print(f"  文本长度分布: {sorted(length_groups.keys())}")
    print(f"  各长度文本数量: {[(length, len(group)) for length, group in sorted(length_groups.items())]}")
    
    # 计算每个长度需要的文本数量（均匀分布）
    available_lengths = sorted(length_groups.keys())
    if not available_lengths:
        print("  ⚠️ 没有找到长度在2-30之间的文本")
        return [], [], 0
    
    # 估算每个长度大约需要多少个文本
    total_length_categories = len(available_lengths)
    avg_chars_per_category = target_chars // total_length_categories
    
    selected_texts = []
    selected_indices = []
    current_chars = 0
    length_stats = {}
    
    # 为每个长度类别选择文本
    for text_length in available_lengths:
        group = length_groups[text_length]
        
        # 计算这个长度需要多少个文本
        target_count_for_length = max(1, avg_chars_per_category // text_length)
        actual_count = min(target_count_for_length, len(group))
        
        # 随机选择这个长度的文本
        selected_from_group = random.sample(group, actual_count)
        
        for idx, text in selected_from_group:
            if current_chars + text_length <= target_chars:
                selected_texts.append(text)
                selected_indices.append(idx)
                current_chars += text_length
                
                # 统计
                if text_length not in length_stats:
                    length_stats[text_length] = 0
                length_stats[text_length] += 1
            else:
                break
        
        # 如果已经达到目标字数，停止
        if current_chars >= target_chars * 0.95:  # 达到95%就停止
            break
    
    # 如果还没达到目标字数，随机补充一些文本
    if current_chars < target_chars * 0.9:
        remaining_chars = target_chars - current_chars
        all_remaining = []
        
        for length, group in length_groups.items():
            for idx, text in group:
                if idx not in selected_indices and len(text) <= remaining_chars:
                    all_remaining.append((idx, text, len(text)))
        
        # 按长度排序，优先选择能填满剩余空间的文本
        all_remaining.sort(key=lambda x: abs(x[2] - remaining_chars))
        
        for idx, text, text_len in all_remaining:
            if current_chars + text_len <= target_chars:
                selected_texts.append(text)
                selected_indices.append(idx)
                current_chars += text_len
                
                if text_len not in length_stats:
                    length_stats[text_len] = 0
                length_stats[text_len] += 1
                
                if current_chars >= target_chars * 0.95:
                    break
    
    # 显示长度分布统计
    print(f"  选中文本长度分布: {dict(sorted(length_stats.items()))}")
    print(f"  长度覆盖范围: {min(length_stats.keys()) if length_stats else 0}-{max(length_stats.keys()) if length_stats else 0}")
    print(f"  总文本数: {len(selected_texts)}, 总字数: {current_chars}")
    
    return selected_texts, selected_indices, current_chars

# ============================================================================
# 增强版本 - 更精确的长度分布控制
# ============================================================================

def select_texts_for_target_chars_enhanced(texts, target_chars, min_length=2, max_length=30):
    """
    增强版文本选择函数，更精确地控制长度分布
    
    Args:
        texts: 文本列表
        target_chars: 目标字符数
        min_length: 最小文本长度
        max_length: 最大文本长度
    
    Returns:
        selected_texts: 选中的文本列表
        selected_indices: 选中文本的原始索引
        current_chars: 实际选中的字符数
    """
    import random
    
    # 按文本长度分组
    length_groups = {}
    for i, text in enumerate(texts):
        text_length = len(text)
        if min_length <= text_length <= max_length:
            if text_length not in length_groups:
                length_groups[text_length] = []
            length_groups[text_length].append((i, text))
    
    available_lengths = sorted(length_groups.keys())
    if not available_lengths:
        print(f"  ⚠️ 没有找到长度在{min_length}-{max_length}之间的文本")
        return [], [], 0
    
    print(f"  可用长度范围: {min(available_lengths)}-{max(available_lengths)}")
    print(f"  长度类别数: {len(available_lengths)}")
    
    # 计算每个长度类别的目标文本数量
    # 目标：每个长度至少有一定数量的文本
    min_texts_per_length = 3  # 每个长度至少3个文本
    total_min_texts = len(available_lengths) * min_texts_per_length
    
    selected_texts = []
    selected_indices = []
    current_chars = 0
    length_stats = {}
    
    # 第一轮：为每个长度至少选择min_texts_per_length个文本
    for text_length in available_lengths:
        group = length_groups[text_length]
        
        # 确保每个长度都有代表
        target_count = min(min_texts_per_length, len(group))
        selected_from_group = random.sample(group, target_count)
        
        for idx, text in selected_from_group:
            if current_chars + text_length <= target_chars:
                selected_texts.append(text)
                selected_indices.append(idx)
                current_chars += text_length
                
                if text_length not in length_stats:
                    length_stats[text_length] = 0
                length_stats[text_length] += 1
    
    # 第二轮：根据剩余字数继续选择
    remaining_chars = target_chars - current_chars
    if remaining_chars > 0:
        # 收集所有未选中的文本
        all_remaining = []
        for length, group in length_groups.items():
            for idx, text in group:
                if idx not in selected_indices:
                    all_remaining.append((idx, text, len(text)))
        
        # 随机打乱，然后按能否填入剩余空间排序
        random.shuffle(all_remaining)
        all_remaining.sort(key=lambda x: x[2])  # 按长度排序
        
        for idx, text, text_len in all_remaining:
            if current_chars + text_len <= target_chars:
                selected_texts.append(text)
                selected_indices.append(idx)
                current_chars += text_len
                
                if text_len not in length_stats:
                    length_stats[text_len] = 0
                length_stats[text_len] += 1
                
                if current_chars >= target_chars * 0.98:  # 达到98%就停止
                    break
    
    # 显示详细统计
    print(f"  最终长度分布:")
    for length in sorted(length_stats.keys()):
        count = length_stats[length]
        total_chars_for_length = length * count
        print(f"    长度{length:2d}: {count:3d}个文本, {total_chars_for_length:4d}字符")
    
    print(f"  覆盖长度范围: {min(length_stats.keys())}-{max(length_stats.keys())}")
    print(f"  总文本数: {len(selected_texts)}")
    print(f"  总字数: {current_chars}/{target_chars} ({current_chars/target_chars*100:.1f}%)")
    
    return selected_texts, selected_indices, current_chars

# ============================================================================
# 使用示例
# ============================================================================

def test_text_selection():
    """测试文本选择函数"""
    # 模拟测试数据
    test_texts = [
        "你好",  # 2字
        "测试文本",  # 4字
        "这是一个测试",  # 6字
        "这是一个比较长的测试文本",  # 12字
        "这是一个非常长的测试文本，用来验证函数的效果",  # 20字
        "短",  # 1字（会被过滤）
        "这是一个超级超级超级长的测试文本，用来验证函数对长文本的处理能力和效果",  # 35字（会被过滤）
    ] * 100  # 重复100次以提供足够的数据
    
    print("=== 测试原始函数 ===")
    selected1, indices1, chars1 = select_texts_for_target_chars(test_texts, 1000)
    
    print("\n=== 测试增强函数 ===")
    selected2, indices2, chars2 = select_texts_for_target_chars_enhanced(test_texts, 1000)

if __name__ == "__main__":
    test_text_selection()
