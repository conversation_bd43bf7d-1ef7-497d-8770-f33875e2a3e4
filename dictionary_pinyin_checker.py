#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代汉语词典拼音覆盖率检查工具
检查词典是否覆盖完整拼音集合，分析缺失的拼音
"""

import os
import re
import json
from collections import Counter, defaultdict
from typing import Set, List, Dict, Tuple

# 配置信息
CONFIG = {
    # 文件路径
    "DICTIONARY_FILE": "现代汉语词典.txt",  # 词典文件
    "COMPLETE_PINYIN_FILE": "pinyin_complete.txt",  # 完整拼音集合
    "REPORT_FILE": "dictionary_pinyin_analysis.txt",  # 分析报告
}

def extract_pinyin_from_text(text: str, debug_mode: bool = False) -> Set[str]:
    """
    从文本中提取拼音（支持多种格式）
    """
    patterns = [
        (r'[a-zA-Z]+\d+', '标准格式(ni3)'),
        (r'[a-zA-Z]+[1-5]', '数字声调(ni1)'),
        (r'[a-zA-Z]+', '无声调(ni)'),
    ]
    
    all_pinyins = set()
    
    if debug_mode:
        print(f"    原始文本示例: {text[:100]}...")
    
    for pattern, desc in patterns:
        pinyins = re.findall(pattern, text)
        if pinyins and debug_mode:
            print(f"    {desc}: 找到 {len(pinyins)} 个拼音")
            print(f"    示例: {pinyins[:5]}")
        all_pinyins.update(pinyin.lower() for pinyin in pinyins)
    
    # 过滤掉过短的匹配
    filtered_pinyins = {p for p in all_pinyins if len(p) >= 2}
    
    if debug_mode:
        print(f"    过滤后拼音数量: {len(filtered_pinyins)}")
    
    return filtered_pinyins

def load_complete_pinyin_set(file_path: str) -> Set[str]:
    """加载完整拼音集合"""
    print(f"=== 加载完整拼音集合: {file_path} ===")
    
    if not os.path.exists(file_path):
        print(f"✗ 文件不存在: {file_path}")
        return set()
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        complete_pinyins = extract_pinyin_from_text(content, debug_mode=True)
        print(f"✓ 完整拼音集合: {len(complete_pinyins)} 个拼音")
        
        return complete_pinyins
        
    except Exception as e:
        print(f"✗ 读取文件失败: {e}")
        return set()

def analyze_dictionary_pinyin(dict_file: str) -> Dict:
    """分析词典中的拼音"""
    print(f"\n=== 分析词典拼音: {dict_file} ===")
    
    if not os.path.exists(dict_file):
        print(f"✗ 词典文件不存在: {dict_file}")
        return {}
    
    try:
        with open(dict_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"✓ 词典文件大小: {len(content)} 字符")
        
        # 提取拼音
        dict_pinyins = extract_pinyin_from_text(content, debug_mode=True)
        
        # 分析拼音分布
        pinyin_counter = Counter()
        
        # 按行分析，统计每个拼音的出现频率
        lines = content.split('\n')
        for line in lines:
            line_pinyins = extract_pinyin_from_text(line)
            for pinyin in line_pinyins:
                pinyin_counter[pinyin] += 1
        
        print(f"✓ 词典拼音集合: {len(dict_pinyins)} 个拼音")
        print(f"✓ 拼音总出现次数: {sum(pinyin_counter.values())}")
        
        # 显示最常见的拼音
        most_common = pinyin_counter.most_common(10)
        print(f"✓ 最常见拼音: {most_common}")
        
        return {
            'pinyins': dict_pinyins,
            'counter': pinyin_counter,
            'total_occurrences': sum(pinyin_counter.values())
        }
        
    except Exception as e:
        print(f"✗ 分析词典失败: {e}")
        return {}

def compare_pinyin_coverage(complete_pinyins: Set[str], dict_analysis: Dict) -> Dict:
    """比较词典与完整拼音集合的覆盖情况"""
    print(f"\n=== 比较拼音覆盖情况 ===")
    
    if not complete_pinyins or not dict_analysis:
        print("✗ 数据不完整，无法比较")
        return {}
    
    dict_pinyins = dict_analysis['pinyins']
    pinyin_counter = dict_analysis['counter']
    
    # 计算覆盖情况
    covered_pinyins = dict_pinyins.intersection(complete_pinyins)
    missing_pinyins = complete_pinyins - dict_pinyins
    extra_pinyins = dict_pinyins - complete_pinyins
    
    coverage_rate = len(covered_pinyins) / len(complete_pinyins) * 100
    
    print(f"✓ 词典覆盖率: {coverage_rate:.1f}% ({len(covered_pinyins)}/{len(complete_pinyins)})")
    print(f"✓ 缺失拼音: {len(missing_pinyins)} 个")
    print(f"✓ 额外拼音: {len(extra_pinyins)} 个")
    
    # 分析缺失拼音的特征
    missing_analysis = analyze_missing_pinyins(missing_pinyins)
    
    # 分析覆盖拼音的频率分布
    covered_frequencies = {p: pinyin_counter.get(p, 0) for p in covered_pinyins}
    
    return {
        'coverage_rate': coverage_rate,
        'covered_pinyins': covered_pinyins,
        'missing_pinyins': missing_pinyins,
        'extra_pinyins': extra_pinyins,
        'missing_analysis': missing_analysis,
        'covered_frequencies': covered_frequencies
    }

def analyze_missing_pinyins(missing_pinyins: Set[str]) -> Dict:
    """分析缺失拼音的特征"""
    if not missing_pinyins:
        return {}
    
    # 按声母分组
    initials = defaultdict(list)
    # 按韵母分组
    finals = defaultdict(list)
    # 按声调分组
    tones = defaultdict(list)
    
    for pinyin in missing_pinyins:
        # 提取声调
        tone = re.findall(r'\d+$', pinyin)
        if tone:
            tone_num = tone[0]
            base_pinyin = pinyin[:-len(tone_num)]
            tones[tone_num].append(pinyin)
        else:
            base_pinyin = pinyin
            tones['无声调'].append(pinyin)
        
        # 简单的声母提取（前1-2个字母）
        if len(base_pinyin) >= 2:
            if base_pinyin[:2] in ['zh', 'ch', 'sh']:
                initial = base_pinyin[:2]
                final = base_pinyin[2:]
            elif base_pinyin[0] in 'bcdfghjklmnpqrstwxyz':
                initial = base_pinyin[0]
                final = base_pinyin[1:]
            else:
                initial = ''
                final = base_pinyin
            
            initials[initial].append(pinyin)
            finals[final].append(pinyin)
    
    return {
        'by_initials': dict(initials),
        'by_finals': dict(finals),
        'by_tones': dict(tones)
    }

def generate_analysis_report(complete_pinyins: Set[str], dict_analysis: Dict, 
                           coverage_result: Dict, report_file: str):
    """生成详细分析报告"""
    print(f"\n=== 生成分析报告: {report_file} ===")
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("现代汉语词典拼音覆盖率分析报告\n")
            f.write("=" * 60 + "\n\n")
            
            # 基本统计
            f.write("基本统计信息:\n")
            f.write("-" * 30 + "\n")
            f.write(f"完整拼音集合: {len(complete_pinyins)} 个\n")
            f.write(f"词典拼音集合: {len(dict_analysis['pinyins'])} 个\n")
            f.write(f"词典拼音总出现次数: {dict_analysis['total_occurrences']}\n")
            f.write(f"覆盖率: {coverage_result['coverage_rate']:.2f}%\n")
            f.write(f"缺失拼音: {len(coverage_result['missing_pinyins'])} 个\n")
            f.write(f"额外拼音: {len(coverage_result['extra_pinyins'])} 个\n\n")
            
            # 缺失拼音详细列表
            missing_pinyins = coverage_result['missing_pinyins']
            if missing_pinyins:
                f.write("缺失拼音列表:\n")
                f.write("-" * 30 + "\n")
                missing_list = sorted(list(missing_pinyins))
                for i in range(0, len(missing_list), 10):
                    line_pinyins = missing_list[i:i+10]
                    f.write(f"{', '.join(line_pinyins)}\n")
                f.write("\n")
                
                # 缺失拼音特征分析
                missing_analysis = coverage_result['missing_analysis']
                if missing_analysis:
                    f.write("缺失拼音特征分析:\n")
                    f.write("-" * 30 + "\n")
                    
                    f.write("按声调分组:\n")
                    for tone, pinyins in missing_analysis['by_tones'].items():
                        f.write(f"  {tone}声: {len(pinyins)} 个 - {', '.join(sorted(pinyins)[:10])}\n")
                    
                    f.write("\n按声母分组:\n")
                    for initial, pinyins in missing_analysis['by_initials'].items():
                        initial_name = initial if initial else '零声母'
                        f.write(f"  {initial_name}: {len(pinyins)} 个 - {', '.join(sorted(pinyins)[:10])}\n")
                    
                    f.write("\n")
            
            # 额外拼音
            extra_pinyins = coverage_result['extra_pinyins']
            if extra_pinyins:
                f.write("额外拼音列表（词典有但完整集合没有）:\n")
                f.write("-" * 30 + "\n")
                extra_list = sorted(list(extra_pinyins))
                for i in range(0, len(extra_list), 10):
                    line_pinyins = extra_list[i:i+10]
                    f.write(f"{', '.join(line_pinyins)}\n")
                f.write("\n")
            
            # 高频拼音分析
            f.write("词典中高频拼音（前20个）:\n")
            f.write("-" * 30 + "\n")
            counter = dict_analysis['counter']
            most_common = counter.most_common(20)
            for pinyin, count in most_common:
                f.write(f"{pinyin}: {count} 次\n")
        
        print(f"✓ 分析报告已生成: {report_file}")
        
    except Exception as e:
        print(f"✗ 生成报告失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("现代汉语词典拼音覆盖率检查工具")
    print("=" * 60)
    
    config = CONFIG
    
    print("配置信息:")
    print(f"  词典文件: {config['DICTIONARY_FILE']}")
    print(f"  完整拼音文件: {config['COMPLETE_PINYIN_FILE']}")
    print(f"  报告文件: {config['REPORT_FILE']}")
    print("=" * 60)
    
    # 1. 加载完整拼音集合
    complete_pinyins = load_complete_pinyin_set(config['COMPLETE_PINYIN_FILE'])
    if not complete_pinyins:
        print("✗ 无法加载完整拼音集合，程序退出")
        return
    
    # 2. 分析词典拼音
    dict_analysis = analyze_dictionary_pinyin(config['DICTIONARY_FILE'])
    if not dict_analysis:
        print("✗ 无法分析词典拼音，程序退出")
        return
    
    # 3. 比较覆盖情况
    coverage_result = compare_pinyin_coverage(complete_pinyins, dict_analysis)
    if not coverage_result:
        print("✗ 无法比较覆盖情况，程序退出")
        return
    
    # 4. 生成详细报告
    generate_analysis_report(complete_pinyins, dict_analysis, coverage_result, config['REPORT_FILE'])
    
    # 5. 显示总结
    print(f"\n=== 分析总结 ===")
    print(f"词典拼音覆盖率: {coverage_result['coverage_rate']:.1f}%")
    print(f"缺失拼音数量: {len(coverage_result['missing_pinyins'])}")
    
    if coverage_result['missing_pinyins']:
        print(f"需要添加的拼音示例: {sorted(list(coverage_result['missing_pinyins']))[:10]}")
    
    print(f"\n🎉 词典拼音分析完成！")
    print(f"详细报告: {config['REPORT_FILE']}")
    print("=" * 60)

if __name__ == "__main__":
    main()
