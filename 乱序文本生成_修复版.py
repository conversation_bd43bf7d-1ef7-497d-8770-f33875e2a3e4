#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
乱序文本生成 - 修复版
解决拼音格式不匹配和权重偏差问题
"""

import random
import re
import os
from collections import defaultdict, Counter

# 配置区域
WORD_LIST_PATH = "现代汉语常用词表 .txt"  # 词表文件路径
DICTIONARY_PATH = "现代汉语词典.txt"  # 词典文件路径（备用）
MISSING_PINYIN_PATHS = [
    "aliyun_missing.txt",  # 阿里云文本缺失拼音文件
    "baiduyun_missing.txt",  # 百度云缺失拼音文件
    "doubao_missing.txt"   # 豆包语音缺失拼音文件
]
OUTPUT_PATHS = [
    "supplement_aliyun.txt",
    "supplement_baiduyun.txt",
    "supplement_doubao.txt"
]
PINYIN_OUTPUT_PATHS = [
    "supplement_aliyun_pinyin.txt",
    "supplement_baiduyun_pinyin.txt",
    "supplement_doubao_pinyin.txt"
]
TEXTS_PER_FILE = 10000  # 每个文件生成的文本数量

def extract_pinyin_from_text(text):
    """
    从文本中提取拼音，支持多种格式
    """
    patterns = [
        r'[a-zA-Z]+\d+',           # 带数字声调：ni3, hao3
        r'[a-zA-Z]+[1-5]',         # 数字声调：ni1, hao3
        r'[a-zü]+',                # 无声调：ni, hao
    ]
    
    all_pinyins = set()
    for pattern in patterns:
        pinyins = re.findall(pattern, text)
        all_pinyins.update(pinyin.lower() for pinyin in pinyins)
    
    # 过滤掉过短的匹配
    return {p for p in all_pinyins if len(p) >= 2}

def normalize_pinyin(pinyin):
    """
    标准化拼音格式，去除声调数字
    """
    # 去除数字声调
    base_pinyin = re.sub(r'\d+$', '', pinyin.lower())
    return base_pinyin

def load_word_list_enhanced(file_path):
    """
    增强版词表加载，支持多种格式并建立拼音映射
    返回两个映射：拼音->词语 和 词语->拼音
    """
    pinyin_map = defaultdict(list)  # 拼音 -> 词语列表
    word_pinyin_map = {}  # 词语 -> 拼音
    word_count = 0

    print(f"正在加载词表: {file_path}")

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                parts = line.split()
                if len(parts) < 2:
                    continue

                word = parts[0]
                # 合并拼音部分
                pinyin_part = ' '.join(parts[1:])

                # 提取拼音
                pinyins = extract_pinyin_from_text(pinyin_part)

                if pinyins:
                    word_count += 1
                    # 保存词语对应的拼音（用于生成拼音文件）
                    word_pinyin_map[word] = pinyin_part.strip()

                    # 为每个拼音（标准化后）添加词语
                    for pinyin in pinyins:
                        normalized = normalize_pinyin(pinyin)
                        if normalized:
                            pinyin_map[normalized].append(word)
                            # 也保留原始格式
                            pinyin_map[pinyin].append(word)

                # 显示进度
                if line_num % 1000 == 0:
                    print(f"  已处理 {line_num} 行，找到 {word_count} 个有效词条")

    except Exception as e:
        print(f"读取词表失败: {e}")
        return defaultdict(list), {}

    print(f"✓ 词表加载完成: {word_count} 个词条，{len(pinyin_map)} 个拼音映射")
    return pinyin_map, word_pinyin_map

def load_dictionary_as_backup(dict_path):
    """
    加载词典作为备用数据源
    返回两个映射：拼音->词语 和 词语->拼音
    """
    if not os.path.exists(dict_path):
        return defaultdict(list), {}

    print(f"正在加载备用词典: {dict_path}")
    pinyin_map = defaultdict(list)  # 拼音 -> 词语列表
    word_pinyin_map = {}  # 词语 -> 拼音

    try:
        with open(dict_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue

                parts = line.split()
                if len(parts) >= 2:
                    word = parts[0]
                    pinyin_part = parts[1]

                    # 保存词语对应的拼音
                    word_pinyin_map[word] = pinyin_part

                    pinyins = extract_pinyin_from_text(pinyin_part)
                    for pinyin in pinyins:
                        normalized = normalize_pinyin(pinyin)
                        if normalized:
                            pinyin_map[normalized].append(word)
                            pinyin_map[pinyin].append(word)

        print(f"✓ 备用词典加载完成: {len(pinyin_map)} 个拼音映射")
    except Exception as e:
        print(f"读取备用词典失败: {e}")

    return pinyin_map, word_pinyin_map

def read_missing_pinyin_enhanced(file_path):
    """
    增强版缺失拼音读取，支持多种格式
    """
    missing_pinyin = set()
    
    print(f"读取缺失拼音文件: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取所有拼音
        all_pinyins = extract_pinyin_from_text(content)
        
        # 同时保留原始格式和标准化格式
        for pinyin in all_pinyins:
            missing_pinyin.add(pinyin)
            normalized = normalize_pinyin(pinyin)
            if normalized and normalized != pinyin:
                missing_pinyin.add(normalized)
        
        print(f"✓ 发现 {len(missing_pinyin)} 个缺失拼音（包含标准化格式）")
        
        # 显示一些示例
        sample_pinyins = sorted(list(missing_pinyin))[:10]
        print(f"  示例: {', '.join(sample_pinyins)}")
        
    except Exception as e:
        print(f"读取缺失拼音文件失败: {e}")
    
    return missing_pinyin

def select_words_for_pinyin_balanced(pinyin_map, pinyin, max_words=5):
    """
    为拼音选择词语，使用平衡策略避免重复
    """
    candidates = []
    
    # 尝试多种拼音格式
    search_pinyins = [
        pinyin,
        normalize_pinyin(pinyin),
        pinyin + '1', pinyin + '2', pinyin + '3', pinyin + '4', pinyin + '5'  # 尝试添加声调
    ]
    
    for search_pinyin in search_pinyins:
        if search_pinyin in pinyin_map:
            candidates.extend(pinyin_map[search_pinyin])
    
    if not candidates:
        return []
    
    # 去重并限制数量
    unique_candidates = list(set(candidates))
    num_to_select = min(max_words, len(unique_candidates))
    
    return random.sample(unique_candidates, num_to_select)

def generate_text_and_pinyin_balanced(word_pool, word_pinyin_map, min_length=2, max_length=30):
    """
    生成平衡的文本和对应的拼音，避免重复使用相同词语
    返回 (文本, 拼音, 使用的词语列表)
    """
    if not word_pool:
        return "", "", []

    # 创建词语使用计数器
    word_usage = Counter()

    # 随机决定文本长度
    target_length = random.randint(min_length, max_length)

    text = ""
    used_words = []  # 记录使用的词语
    attempts = 0
    max_attempts = target_length * 3  # 避免无限循环

    while len(text) < target_length and attempts < max_attempts:
        attempts += 1

        # 选择使用次数较少的词语
        available_words = [w for w in word_pool if word_usage[w] < 3]  # 限制每个词最多使用3次
        if not available_words:
            available_words = word_pool  # 如果都用完了，重新开始
            word_usage.clear()

        word = random.choice(available_words)
        word_usage[word] += 1

        # 检查是否可以添加整个词
        if len(text) + len(word) <= target_length:
            text += word
            used_words.append(word)
        else:
            # 添加部分字符以达到目标长度
            remaining = target_length - len(text)
            if remaining > 0 and len(word) > remaining:
                text += word[:remaining]
                used_words.append(word[:remaining])  # 记录使用的部分
            break

    # 生成对应的拼音
    pinyin_parts = []
    for word in used_words:
        if word in word_pinyin_map:
            pinyin_parts.append(word_pinyin_map[word])
        else:
            # 如果找不到完整词的拼音，尝试逐字查找
            char_pinyins = []
            for char in word:
                if char in word_pinyin_map:
                    char_pinyins.append(word_pinyin_map[char])
                else:
                    char_pinyins.append(char)  # 如果找不到拼音，保留原字符
            pinyin_parts.append(' '.join(char_pinyins))

    pinyin_text = ' '.join(pinyin_parts)

    return text, pinyin_text, used_words

def main():
    print("=" * 60)
    print("乱序文本生成 - 修复版")
    print("解决拼音格式不匹配和权重偏差问题")
    print("=" * 60)
    
    # 1. 加载主词表
    pinyin_map, word_pinyin_map = load_word_list_enhanced(WORD_LIST_PATH)

    # 2. 加载备用词典
    backup_map, backup_word_pinyin_map = load_dictionary_as_backup(DICTIONARY_PATH)

    # 3. 合并词表
    print("\n合并词表数据...")
    combined_map = defaultdict(list)
    combined_word_pinyin_map = {}

    # 合并主词表
    for pinyin, words in pinyin_map.items():
        combined_map[pinyin].extend(words)
    combined_word_pinyin_map.update(word_pinyin_map)

    # 合并备用词典
    for pinyin, words in backup_map.items():
        combined_map[pinyin].extend(words)
    combined_word_pinyin_map.update(backup_word_pinyin_map)

    # 去重
    for pinyin in combined_map:
        combined_map[pinyin] = list(set(combined_map[pinyin]))

    print(f"✓ 合并完成: {len(combined_map)} 个拼音映射，{len(combined_word_pinyin_map)} 个词语拼音映射")
    
    # 4. 为每个缺失拼音文件生成补充文本
    for i, (input_path, output_path, pinyin_output_path) in enumerate(zip(MISSING_PINYIN_PATHS, OUTPUT_PATHS, PINYIN_OUTPUT_PATHS)):
        print(f"\n{'='*50}")
        print(f"处理文本 {i+1}: {os.path.basename(input_path)}")
        print(f"{'='*50}")
        
        # 检查输入文件
        if not os.path.exists(input_path):
            print(f"✗ 文件不存在: {input_path}")
            continue
        
        # 读取缺失拼音
        missing_pinyin = read_missing_pinyin_enhanced(input_path)
        if not missing_pinyin:
            print("✗ 没有找到缺失拼音")
            continue
        
        # 为缺失拼音收集词语
        word_pool = []
        found_pinyins = []
        not_found_pinyins = []
        
        for pinyin in missing_pinyin:
            words = select_words_for_pinyin_balanced(combined_map, pinyin)
            if words:
                word_pool.extend(words)
                found_pinyins.append(pinyin)
            else:
                not_found_pinyins.append(pinyin)
        
        print(f"\n拼音匹配结果:")
        print(f"  找到词语的拼音: {len(found_pinyins)} 个")
        print(f"  未找到词语的拼音: {len(not_found_pinyins)} 个")
        
        if not_found_pinyins:
            print(f"  未找到的拼音示例: {', '.join(not_found_pinyins[:10])}")
        
        if not word_pool:
            print("✗ 没有找到任何相关词语，跳过此文件")
            continue
        
        # 去重词池
        word_pool = list(set(word_pool))
        print(f"✓ 创建词池: {len(word_pool)} 个唯一词语")
        
        # 生成文本和拼音
        print(f"\n生成 {TEXTS_PER_FILE} 条文本和对应拼音...")
        all_texts = []
        all_pinyins = []

        for j in range(TEXTS_PER_FILE):
            text, pinyin, used_words = generate_text_and_pinyin_balanced(word_pool, combined_word_pinyin_map)
            if text:
                all_texts.append(text)
                all_pinyins.append(pinyin)

            # 显示进度
            if (j + 1) % 1000 == 0:
                print(f"  已生成 {j + 1}/{TEXTS_PER_FILE} 条文本")

        if not all_texts:
            print("✗ 没有生成任何文本")
            continue
        
        # 按长度排序（同时排序文本和拼音）
        text_pinyin_pairs = list(zip(all_texts, all_pinyins))
        text_pinyin_pairs.sort(key=lambda x: len(x[0]))  # 按文本长度排序
        all_texts, all_pinyins = zip(*text_pinyin_pairs)

        # 保存文本文件
        print(f"\n保存文本到: {output_path}")
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                for text in all_texts:
                    f.write(text + "\n")
            print(f"✓ 文本文件保存成功")
        except Exception as e:
            print(f"✗ 保存文本文件失败: {e}")
            continue

        # 保存拼音文件
        print(f"保存拼音到: {pinyin_output_path}")
        try:
            with open(pinyin_output_path, 'w', encoding='utf-8') as f:
                for pinyin in all_pinyins:
                    f.write(pinyin + "\n")
            print(f"✓ 拼音文件保存成功")
        except Exception as e:
            print(f"✗ 保存拼音文件失败: {e}")

        # 统计信息
        lengths = [len(t) for t in all_texts]
        min_len = min(lengths)
        max_len = max(lengths)
        avg_len = sum(lengths) / len(lengths)

        print(f"\n✓ 生成完成:")
        print(f"  文本数量: {len(all_texts)} 条")
        print(f"  拼音数量: {len(all_pinyins)} 条")
        print(f"  长度范围: {min_len}-{max_len} 字符")
        print(f"  平均长度: {avg_len:.1f} 字符")

        # 长度分布统计
        length_dist = Counter(lengths)
        print(f"  长度分布示例: {dict(list(length_dist.most_common(5)))}")

        # 拼音覆盖检查
        generated_pinyins = set()
        for pinyin_line in all_pinyins:
            line_pinyins = extract_pinyin_from_text(pinyin_line)
            generated_pinyins.update(line_pinyins)

        covered_missing = generated_pinyins.intersection(missing_pinyin)
        coverage_rate = len(covered_missing) / len(missing_pinyin) * 100 if missing_pinyin else 0
        print(f"  拼音覆盖率: {coverage_rate:.1f}% ({len(covered_missing)}/{len(missing_pinyin)})")
    
    print(f"\n{'='*60}")
    print("🎉 补充文本和拼音生成完成！")
    print("\n生成的文件:")
    for i, (text_file, pinyin_file) in enumerate(zip(OUTPUT_PATHS, PINYIN_OUTPUT_PATHS), 1):
        print(f"  {i}. 文本: {text_file}")
        print(f"     拼音: {pinyin_file}")
    print("\n请检查输出文件并验证拼音覆盖情况。")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
