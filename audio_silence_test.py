#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频静音时间标准化测试工具
测试静音检测和标准化功能
"""

import os
import wave
import numpy as np
from pathlib import Path
import pandas as pd
from typing import Dict, List, Tuple
import warnings
import shutil
from datetime import datetime

warnings.filterwarnings('ignore')

class AudioSilenceAnalyzer:
    """音频静音分析器（仅分析，不修改文件）"""
    
    def __init__(self):
        self.silence_threshold = 0.01
    
    def detect_silence_boundaries(self, audio_data: np.ndarray, sample_rate: int) -> Tuple[int, int]:
        """检测音频开头和结尾的静音边界"""
        if len(audio_data) == 0:
            return 0, 0
        
        # 计算静音阈值
        max_amplitude = np.max(np.abs(audio_data))
        silence_threshold = max_amplitude * self.silence_threshold
        
        # 找到开头的非静音位置
        start_index = 0
        for i in range(len(audio_data)):
            if np.abs(audio_data[i]) > silence_threshold:
                start_index = i
                break
        
        # 找到结尾的非静音位置
        end_index = len(audio_data) - 1
        for i in range(len(audio_data) - 1, -1, -1):
            if np.abs(audio_data[i]) > silence_threshold:
                end_index = i
                break
        
        return start_index, end_index
    
    def analyze_audio_silence(self, file_path: str) -> Dict:
        """分析单个音频文件的静音情况"""
        try:
            # 读取音频文件
            with wave.open(file_path, 'rb') as wav_file:
                frames = wav_file.getnframes()
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()
                duration = frames / sample_rate
                
                # 读取音频数据
                audio_data = wav_file.readframes(frames)
                
                # 转换为numpy数组
                if sample_width == 1:
                    dtype = np.uint8
                elif sample_width == 2:
                    dtype = np.int16
                elif sample_width == 4:
                    dtype = np.int32
                else:
                    dtype = np.float32
                
                audio_array = np.frombuffer(audio_data, dtype=dtype)
            
            # 检测静音边界
            start_index, end_index = self.detect_silence_boundaries(audio_array, sample_rate)
            
            # 计算静音时长
            start_silence_ms = (start_index / sample_rate) * 1000
            end_silence_ms = ((len(audio_array) - 1 - end_index) / sample_rate) * 1000
            content_duration = (end_index - start_index + 1) / sample_rate
            
            # 判断是否需要调整
            needs_start_adjustment = start_silence_ms < 150 or start_silence_ms > 250
            needs_end_adjustment = end_silence_ms < 150 or end_silence_ms > 250
            
            result = {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'status': 'success',
                'duration_seconds': duration,
                'sample_rate': sample_rate,
                'channels': channels,
                'sample_width': sample_width,
                'start_silence_ms': start_silence_ms,
                'end_silence_ms': end_silence_ms,
                'content_duration': content_duration,
                'needs_start_adjustment': needs_start_adjustment,
                'needs_end_adjustment': needs_end_adjustment,
                'needs_any_adjustment': needs_start_adjustment or needs_end_adjustment,
                'file_size_kb': os.path.getsize(file_path) / 1024
            }
            
            return result
            
        except Exception as e:
            return {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'status': 'error',
                'error': str(e)
            }
    
    def analyze_sample_files(self, directory: str, sample_count: int = 20) -> pd.DataFrame:
        """分析样本音频文件的静音情况"""
        # 获取所有wav文件
        wav_files = list(Path(directory).glob("*.wav"))
        
        if not wav_files:
            print(f"在目录 {directory} 中未找到wav文件")
            return pd.DataFrame()
        
        # 选择样本文件
        sample_files = wav_files[:sample_count]
        
        print(f"分析 {len(sample_files)} 个样本文件的静音情况...")
        
        results = []
        for i, file_path in enumerate(sample_files, 1):
            print(f"[{i}/{len(sample_files)}] 分析: {file_path.name}")
            result = self.analyze_audio_silence(str(file_path))
            results.append(result)
            
            # 显示分析结果
            if result['status'] == 'success':
                start_ms = result['start_silence_ms']
                end_ms = result['end_silence_ms']
                needs_adj = result['needs_any_adjustment']
                status_icon = "⚠️" if needs_adj else "✓"
                print(f"  {status_icon} 开头静音: {start_ms:.0f}ms, 结尾静音: {end_ms:.0f}ms")
            else:
                print(f"  ✗ 错误: {result['error']}")
        
        return pd.DataFrame(results)

def print_silence_analysis_summary(df: pd.DataFrame):
    """打印静音分析结果摘要"""
    if df.empty:
        print("没有分析结果")
        return
    
    print("\n" + "="*60)
    print("音频静音分析摘要")
    print("="*60)
    
    # 成功分析的文件
    success_df = df[df['status'] == 'success']
    error_df = df[df['status'] == 'error']
    
    print(f"总文件数: {len(df)}")
    print(f"成功分析: {len(success_df)}")
    print(f"分析失败: {len(error_df)}")
    
    if len(success_df) > 0:
        print(f"\n基本信息:")
        print(f"平均时长: {success_df['duration_seconds'].mean():.2f} 秒")
        print(f"采样率: {success_df['sample_rate'].unique()}")
        print(f"声道数: {success_df['channels'].unique()}")
        
        print(f"\n静音时间统计:")
        print(f"开头静音时间:")
        print(f"  平均: {success_df['start_silence_ms'].mean():.1f}ms")
        print(f"  范围: {success_df['start_silence_ms'].min():.1f} - {success_df['start_silence_ms'].max():.1f}ms")
        print(f"  标准差: {success_df['start_silence_ms'].std():.1f}ms")
        
        print(f"结尾静音时间:")
        print(f"  平均: {success_df['end_silence_ms'].mean():.1f}ms")
        print(f"  范围: {success_df['end_silence_ms'].min():.1f} - {success_df['end_silence_ms'].max():.1f}ms")
        print(f"  标准差: {success_df['end_silence_ms'].std():.1f}ms")
        
        # 需要调整的文件统计
        need_start_adj = success_df['needs_start_adjustment'].sum()
        need_end_adj = success_df['needs_end_adjustment'].sum()
        need_any_adj = success_df['needs_any_adjustment'].sum()
        
        print(f"\n需要调整的文件:")
        print(f"开头静音需要调整: {need_start_adj} 个 ({need_start_adj/len(success_df)*100:.1f}%)")
        print(f"结尾静音需要调整: {need_end_adj} 个 ({need_end_adj/len(success_df)*100:.1f}%)")
        print(f"任一端需要调整: {need_any_adj} 个 ({need_any_adj/len(success_df)*100:.1f}%)")
        
        # 显示需要调整的具体文件
        if need_any_adj > 0:
            print(f"\n需要调整的文件详情:")
            need_adj_df = success_df[success_df['needs_any_adjustment']]
            for _, row in need_adj_df.iterrows():
                start_status = "❌" if row['needs_start_adjustment'] else "✓"
                end_status = "❌" if row['needs_end_adjustment'] else "✓"
                print(f"  {row['file_name']}: 开头{start_status}{row['start_silence_ms']:.0f}ms, 结尾{end_status}{row['end_silence_ms']:.0f}ms")
        
        # 静音时间分布
        print(f"\n静音时间分布:")
        
        # 开头静音分布
        start_too_short = (success_df['start_silence_ms'] < 150).sum()
        start_good = ((success_df['start_silence_ms'] >= 150) & (success_df['start_silence_ms'] <= 250)).sum()
        start_too_long = (success_df['start_silence_ms'] > 250).sum()
        
        print(f"开头静音:")
        print(f"  < 150ms (过短): {start_too_short} 个")
        print(f"  150-250ms (正常): {start_good} 个")
        print(f"  > 250ms (过长): {start_too_long} 个")
        
        # 结尾静音分布
        end_too_short = (success_df['end_silence_ms'] < 150).sum()
        end_good = ((success_df['end_silence_ms'] >= 150) & (success_df['end_silence_ms'] <= 250)).sum()
        end_too_long = (success_df['end_silence_ms'] > 250).sum()
        
        print(f"结尾静音:")
        print(f"  < 150ms (过短): {end_too_short} 个")
        print(f"  150-250ms (正常): {end_good} 个")
        print(f"  > 250ms (过长): {end_too_long} 个")
    
    if len(error_df) > 0:
        print(f"\n错误文件:")
        for _, row in error_df.iterrows():
            print(f"  {row['file_name']}: {row['error']}")

def main():
    """主函数"""
    print("=" * 60)
    print("音频静音时间分析测试工具")
    print("=" * 60)
    
    print("功能说明:")
    print("- 分析音频文件开头和结尾的静音时间")
    print("- 检测哪些文件的静音时间不在150-250ms范围内")
    print("- 不会修改任何文件，仅进行分析")
    
    # 分析当前目录的样本文件
    current_dir = "."
    sample_count = 30  # 分析30个样本
    
    print(f"\n开始分析当前目录的前{sample_count}个音频文件...")
    
    analyzer = AudioSilenceAnalyzer()
    df = analyzer.analyze_sample_files(current_dir, sample_count)
    
    if not df.empty:
        # 保存详细结果
        result_file = f"silence_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        df.to_csv(result_file, index=False, encoding='utf-8-sig')
        print(f"\n详细分析结果已保存到: {result_file}")
        
        # 打印摘要
        print_silence_analysis_summary(df)
        
        # 给出建议
        success_df = df[df['status'] == 'success']
        if len(success_df) > 0:
            need_adjustment = success_df['needs_any_adjustment'].sum()
            if need_adjustment > 0:
                print(f"\n💡 建议:")
                print(f"发现 {need_adjustment} 个文件的静音时间不在标准范围内")
                print(f"可以使用 audio_silence_normalizer.py 工具进行批量标准化处理")
            else:
                print(f"\n✅ 所有文件的静音时间都在标准范围内，无需调整")
    else:
        print("未找到可分析的音频文件")

if __name__ == "__main__":
    main()
