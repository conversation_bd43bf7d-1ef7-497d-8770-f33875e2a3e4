#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频日志生成器 - 为Sc0008001.wav到Sc0010000.wav生成CSV日志记录
"""

import os
import csv
from pathlib import Path
import pandas as pd
from datetime import datetime

# ==================== 配置参数 ====================
CONFIG = {
    # 文件配置
    "TEXT_FILE": "../shuffled_from_rank_random.txt",
    "PINYIN_FILE": "../shuffled_from_rank_random_pinyin.txt",
    "AUDIO_DIR": ".",  # 音频文件目录
    "OUTPUT_CSV": "audio_log_8001_10000.csv",
    
    # 范围配置
    "START_INDEX": 8001,
    "END_INDEX": 10000,
    "TOTAL_COUNT": 2000
}

def load_text_files(config):
    """加载文本和拼音文件"""
    print("=== 加载文本和拼音文件 ===")
    
    # 读取文本文件
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    else:
        print(f"✗ 拼音文件不存在: {config['PINYIN_FILE']}")
        return texts, []
    
    return texts, pinyins

def scan_existing_audio_files(config):
    """扫描已存在的音频文件"""
    print("=== 扫描已存在的音频文件 ===")
    
    existing_files = []
    missing_files = []
    
    for i in range(config["START_INDEX"], config["END_INDEX"] + 1):
        audio_name = f"Sc{i:07d}"
        audio_file = f"{audio_name}.wav"
        audio_path = os.path.join(config["AUDIO_DIR"], audio_file)
        
        if os.path.exists(audio_path):
            existing_files.append(i)
        else:
            missing_files.append(i)
    
    print(f"扫描范围: {config['START_INDEX']} - {config['END_INDEX']}")
    print(f"已存在音频文件: {len(existing_files)} 个")
    print(f"缺失音频文件: {len(missing_files)} 个")
    
    if missing_files:
        print(f"缺失文件示例: {missing_files[:10]}{'...' if len(missing_files) > 10 else ''}")
    
    return existing_files, missing_files

def generate_audio_log(config):
    """生成音频日志CSV文件"""
    print("=== 生成音频日志CSV文件 ===")
    
    # 1. 加载文本和拼音文件
    texts, pinyins = load_text_files(config)
    if texts is None:
        return False
    
    # 检查文件行数是否足够
    if len(texts) < config['END_INDEX']:
        print(f"✗ 文本文件行数不足: 需要{config['END_INDEX']}行，实际{len(texts)}行")
        return False
    
    # 2. 扫描已存在的音频文件
    existing_files, missing_files = scan_existing_audio_files(config)
    
    if not existing_files:
        print("✗ 未找到任何音频文件")
        return False
    
    # 3. 生成CSV日志记录
    csv_path = config["OUTPUT_CSV"]
    
    print(f"开始生成CSV日志: {csv_path}")
    
    log_records = []
    
    for index in existing_files:
        # 获取对应的文本和拼音
        text_index = index - 1  # 转换为数组索引
        text = texts[text_index] if text_index < len(texts) else ""
        pinyin = pinyins[text_index] if text_index < len(pinyins) else ""
        
        # 创建日志记录
        audio_name = f"Sc{index:07d}"
        record = {
            '音频名': audio_name,
            '类型': 'c',
            '文本': text,
            '注音1': pinyin
        }
        
        log_records.append(record)
    
    # 按音频名排序
    log_records.sort(key=lambda x: x['音频名'])
    
    # 写入CSV文件（标准CSV格式）
    with open(csv_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.writer(csvfile, quoting=csv.QUOTE_ALL)

        # 写入表头
        writer.writerow(['音频名', '类型', '文本', '注音1'])

        # 写入数据
        for record in log_records:
            writer.writerow([record['音频名'], record['类型'], record['文本'], record['注音1']])
    
    print(f"✓ CSV日志文件已生成: {csv_path}")
    print(f"✓ 记录数量: {len(log_records)} 条")
    
    return True

def print_generation_summary(config):
    """打印生成结果摘要"""
    print("\n" + "="*60)
    print("音频日志生成摘要")
    print("="*60)
    
    # 重新扫描文件
    existing_files, missing_files = scan_existing_audio_files(config)
    
    print(f"目标范围: {config['START_INDEX']} - {config['END_INDEX']}")
    print(f"目标总数: {config['TOTAL_COUNT']} 个文件")
    print(f"已存在文件: {len(existing_files)} 个")
    print(f"缺失文件: {len(missing_files)} 个")
    print(f"完成率: {len(existing_files)/config['TOTAL_COUNT']*100:.1f}%")
    
    # 检查CSV文件
    csv_path = config["OUTPUT_CSV"]
    if os.path.exists(csv_path):
        try:
            with open(csv_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            record_count = len(lines) - 1  # 减去表头
            print(f"CSV记录数: {record_count} 条")
            
            # 显示前几条记录示例
            print(f"\nCSV文件示例 (前5条):")
            for i, line in enumerate(lines[1:6], 1):  # 跳过表头
                parts = line.strip().split('\t')
                if len(parts) >= 3:
                    print(f"  {i}. {parts[0]}: {parts[2][:30]}{'...' if len(parts[2]) > 30 else ''}")
            
        except Exception as e:
            print(f"读取CSV文件失败: {e}")
    
    # 分析缺失文件分布
    if missing_files:
        print(f"\n缺失文件分析:")
        
        # 按范围分组统计
        ranges = [
            (8001, 8200, "第1批(8001-8200)"),
            (8201, 8400, "第2批(8201-8400)"),
            (8401, 8600, "第3批(8401-8600)"),
            (8601, 8800, "第4批(8601-8800)"),
            (8801, 9000, "第5批(8801-9000)"),
            (9001, 9200, "第6批(9001-9200)"),
            (9201, 9400, "第7批(9201-9400)"),
            (9401, 9600, "第8批(9401-9600)"),
            (9601, 9800, "第9批(9601-9800)"),
            (9801, 10000, "第10批(9801-10000)")
        ]
        
        for start, end, name in ranges:
            range_missing = [f for f in missing_files if start <= f <= end]
            range_total = end - start + 1
            range_existing = range_total - len(range_missing)
            print(f"  {name}: {range_existing}/{range_total} ({range_existing/range_total*100:.1f}%)")

def main():
    """主函数"""
    print("=" * 60)
    print("音频日志生成器 - Sc0008001到Sc0010000")
    print("=" * 60)
    
    config = CONFIG
    
    print("配置信息:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  拼音文件: {config['PINYIN_FILE']}")
    print(f"  音频目录: {config['AUDIO_DIR']}")
    print(f"  输出CSV: {config['OUTPUT_CSV']}")
    print(f"  范围: {config['START_INDEX']} - {config['END_INDEX']}")
    print(f"  总数: {config['TOTAL_COUNT']} 个文件")
    print("=" * 60)
    
    # 检查必要文件
    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return
    
    if not os.path.exists(config['PINYIN_FILE']):
        print(f"⚠️ 拼音文件不存在: {config['PINYIN_FILE']}")
        print("将继续处理，但拼音字段将为空")
    
    # 开始生成
    print(f"\n开始生成音频日志...")
    start_time = datetime.now()
    
    success = generate_audio_log(config)
    
    end_time = datetime.now()
    processing_time = (end_time - start_time).total_seconds()
    
    if success:
        print(f"\n🎉 音频日志生成完成！")
        print(f"耗时: {processing_time:.2f}秒")
        
        # 打印摘要
        print_generation_summary(config)
        
        print(f"\n📄 输出文件: {config['OUTPUT_CSV']}")
        print("格式: 制表符分隔的CSV文件")
        print("列: 音频名、类型、文本、注音1")
    else:
        print(f"\n⚠️ 音频日志生成失败")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
