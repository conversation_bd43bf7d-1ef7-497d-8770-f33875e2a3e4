#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云TTS音频生成器 - 完整版
生成第7001-8000条音频文件，包含测试和生成功能
"""

import requests
import json
import time
import csv
import os
import uuid
import hashlib
import hmac
import base64
import glob
import argparse
from datetime import datetime
from urllib.parse import quote
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# ==================== 阿里云配置 ====================
ALIYUN_CONFIG = {
    "ACCESS_KEY_ID": "LTAI5t7reocF8UzbqkiReQQy",
    "ACCESS_KEY_SECRET": "******************************",
    "APP_KEY": "9LNJJ3RhO4Ys8O35",
    "REGION": "cn-shanghai",
    
    # 语音参数配置
    "VOICE": "xiaoyun",  # 可选: xia<PERSON>un, xiaogang, xiaomeng等
    "FORMAT": "wav",
    "SAMPLE_RATE": 16000,
    "VOLUME": 50,
    "SPEECH_RATE": 0,
    "PITCH_RATE": 0,
    
    # 文件配置
    "TEXT_FILE": "shuffled_from_rank_random.txt",
    "PINYIN_FILE": "shuffled_from_rank_random_pinyin.txt",
    "OUTPUT_DIR": "audio_output",
    "CSV_FILE": "audio_info_7001_8000.csv",
    "REQUEST_INTERVAL": 0.3,  # 请求间隔（秒）
    
    # 生成范围配置
    "START_INDEX": 7001,
    "END_INDEX": 8000,
    "TOTAL_COUNT": 1000
}

class AliyunTTSDirect:
    """阿里云TTS直接API调用类"""
    
    def __init__(self, config):
        self.config = config
        self.access_key_id = config["ACCESS_KEY_ID"]
        self.access_key_secret = config["ACCESS_KEY_SECRET"]
        self.app_key = config["APP_KEY"]
        
        # 获取访问令牌
        self.access_token = self._get_access_token()
        if not self.access_token:
            raise Exception("无法获取访问令牌")
    
    def _get_access_token(self):
        """获取阿里云访问令牌"""
        url = "https://nls-meta.cn-shanghai.aliyuncs.com/pop/2018-05-18/tokens"
        
        # 构建请求参数
        timestamp = datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
        nonce = str(uuid.uuid4())
        
        params = {
            'AccessKeyId': self.access_key_id,
            'Action': 'CreateToken',
            'Format': 'JSON',
            'RegionId': 'cn-shanghai',
            'SignatureMethod': 'HMAC-SHA1',
            'SignatureNonce': nonce,
            'SignatureVersion': '1.0',
            'Timestamp': timestamp,
            'Version': '2018-05-18'
        }
        
        # 生成签名
        signature = self._generate_signature('GET', params)
        params['Signature'] = signature
        
        try:
            response = requests.get(url, params=params, timeout=10, verify=False)
            if response.status_code == 200:
                result = response.json()
                if 'Token' in result:
                    print("✓ 成功获取访问令牌")
                    return result['Token']['Id']
                else:
                    print(f"✗ 令牌响应格式错误: {result}")
                    return None
            else:
                print(f"✗ 获取令牌失败: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"✗ 获取令牌异常: {e}")
            return None
    
    def _generate_signature(self, method, params):
        """生成阿里云API签名"""
        # 排序参数
        sorted_params = sorted(params.items())
        query_string = '&'.join([f"{quote(k)}={quote(str(v))}" for k, v in sorted_params])
        
        # 构建待签名字符串
        string_to_sign = f"{method}&{quote('/')}&{quote(query_string)}"
        
        # 计算签名
        signature = base64.b64encode(
            hmac.new(
                (self.access_key_secret + '&').encode(),
                string_to_sign.encode(),
                hashlib.sha1
            ).digest()
        ).decode()
        
        return signature
    
    def synthesize_text(self, text):
        """合成单个文本为语音"""
        url = "https://nls-gateway.cn-shanghai.aliyuncs.com/stream/v1/tts"
        
        headers = {
            'Content-Type': 'application/json',
            'X-NLS-Token': self.access_token
        }
        
        data = {
            'appkey': self.app_key,
            'text': text,
            'voice': self.config["VOICE"],
            'format': self.config["FORMAT"],
            'sample_rate': self.config["SAMPLE_RATE"],
            'volume': self.config["VOLUME"],
            'speech_rate': self.config["SPEECH_RATE"],
            'pitch_rate': self.config["PITCH_RATE"]
        }
        
        try:
            response = requests.post(
                url,
                headers=headers,
                data=json.dumps(data),
                timeout=30,
                verify=False
            )
            
            if response.status_code == 200:
                # 检查响应内容类型
                content_type = response.headers.get('content-type', '')
                if 'audio' in content_type or 'application/octet-stream' in content_type:
                    return response.content
                else:
                    # 可能是错误响应
                    try:
                        error_info = response.json()
                        print(f"  ✗ API错误: {error_info}")
                    except:
                        print(f"  ✗ 未知响应格式: {response.text[:200]}")
                    return None
            else:
                print(f"  ✗ HTTP错误 {response.status_code}: {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            print(f"  ✗ 请求超时")
            return None
        except Exception as e:
            print(f"  ✗ 请求异常: {e}")
            return None

def load_text_files(config):
    """加载文本和拼音文件"""
    print("=== 加载文本文件 ===")
    
    # 读取文本文件
    try:
        with open(config["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return None, None
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(config["PINYIN_FILE"]):
        try:
            with open(config["PINYIN_FILE"], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    return texts, pinyins

def get_existing_files(output_dir, start_index, end_index):
    """获取指定范围内已存在的文件"""
    existing_indices = set()
    
    for i in range(start_index, end_index + 1):
        filename = f"Sc{i:07d}.wav"
        filepath = os.path.join(output_dir, filename)
        if os.path.exists(filepath):
            existing_indices.add(i)
    
    return existing_indices

def test_configuration():
    """测试配置信息"""
    print("=== 配置信息 ===")

    config = ALIYUN_CONFIG

    print("阿里云配置:")
    print(f"  ACCESS_KEY_ID: {config['ACCESS_KEY_ID']}")
    print(f"  ACCESS_KEY_SECRET: {config['ACCESS_KEY_SECRET'][:10]}...")
    print(f"  APP_KEY: {config['APP_KEY']}")
    print(f"  区域: {config['REGION']}")

    print("\n生成配置:")
    print(f"  生成范围: {config['START_INDEX']} - {config['END_INDEX']}")
    print(f"  总数量: {config['TOTAL_COUNT']}")
    print(f"  音频编号: Sc{config['START_INDEX']:07d}.wav - Sc{config['END_INDEX']:07d}.wav")

    print("\n语音参数:")
    print(f"  发音人: {config['VOICE']}")
    print(f"  格式: {config['FORMAT']}")
    print(f"  采样率: {config['SAMPLE_RATE']}Hz")
    print(f"  音量: {config['VOLUME']}")

    print("\n文件配置:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  拼音文件: {config['PINYIN_FILE']}")
    print(f"  输出目录: {config['OUTPUT_DIR']}")
    print(f"  CSV文件: {config['CSV_FILE']}")
    print(f"  请求间隔: {config['REQUEST_INTERVAL']}秒")

def test_files():
    """测试文件配置"""
    print("\n=== 文件检查 ===")

    config = ALIYUN_CONFIG

    # 检查文本文件
    if os.path.exists(config['TEXT_FILE']):
        with open(config['TEXT_FILE'], 'r', encoding='utf-8') as f:
            lines = f.readlines()
        print(f"✓ 文本文件存在: {len(lines)} 行")

        if len(lines) >= 8000:
            print(f"✓ 文件行数足够: 可生成到第8000行")
            # 显示第7001行示例
            if len(lines) >= 7001:
                sample_text = lines[7000].strip()  # 第7001行（索引7000）
                print(f"  第7001行示例: {sample_text[:50]}{'...' if len(sample_text) > 50 else ''}")
        else:
            print(f"✗ 文件行数不足: 需要8000行，实际{len(lines)}行")
    else:
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")

    # 检查拼音文件
    if os.path.exists(config['PINYIN_FILE']):
        with open(config['PINYIN_FILE'], 'r', encoding='utf-8') as f:
            lines = f.readlines()
        print(f"✓ 拼音文件存在: {len(lines)} 行")

        if len(lines) >= 8000:
            print(f"✓ 文件行数足够: 可生成到第8000行")
            # 显示第7001行示例
            if len(lines) >= 7001:
                sample_pinyin = lines[7000].strip()  # 第7001行（索引7000）
                print(f"  第7001行示例: {sample_pinyin[:50]}{'...' if len(sample_pinyin) > 50 else ''}")
        else:
            print(f"✗ 文件行数不足: 需要8000行，实际{len(lines)}行")
    else:
        print(f"✗ 拼音文件不存在: {config['PINYIN_FILE']}")

    # 检查输出目录
    if os.path.exists(config['OUTPUT_DIR']):
        print(f"✓ 输出目录存在: {config['OUTPUT_DIR']}")

        # 检查已存在的目标范围文件
        existing_count = 0
        for i in range(7001, 8001):
            filename = f"Sc{i:07d}.wav"
            filepath = os.path.join(config['OUTPUT_DIR'], filename)
            if os.path.exists(filepath):
                existing_count += 1

        print(f"  目标范围(7001-8000)已存在: {existing_count}/1000 个文件")
    else:
        print(f"○ 输出目录将创建: {config['OUTPUT_DIR']}")

def test_api_connection():
    """测试API连接"""
    print("\n=== API连接测试 ===")

    try:
        # 测试初始化TTS客户端
        tts = AliyunTTSDirect(ALIYUN_CONFIG)
        print("✓ TTS客户端初始化成功")
        print(f"✓ 访问令牌获取成功: {tts.access_token[:20]}...")

        # 测试简单的语音合成
        test_text = "这是一个测试文本"
        print(f"\n测试语音合成: {test_text}")

        audio_data = tts.synthesize_text(test_text)
        if audio_data:
            print(f"✓ 语音合成成功: {len(audio_data)} 字节")

            # 保存测试音频
            test_file = os.path.join(ALIYUN_CONFIG['OUTPUT_DIR'], "test_audio.wav")
            os.makedirs(ALIYUN_CONFIG['OUTPUT_DIR'], exist_ok=True)
            with open(test_file, 'wb') as f:
                f.write(audio_data)
            print(f"✓ 测试音频已保存: {test_file}")
            return True
        else:
            print("✗ 语音合成失败")
            return False

    except Exception as e:
        print(f"✗ API连接测试失败: {e}")
        return False

def generate_audio_batch(config):
    """批量生成音频文件"""
    print("\n=== 阿里云TTS音频批量生成 ===")
    print(f"生成范围: {config['START_INDEX']} - {config['END_INDEX']}")
    print(f"总计: {config['TOTAL_COUNT']} 个文件")
    print("=" * 60)

    # 1. 加载文本文件
    texts, pinyins = load_text_files(config)
    if texts is None:
        return False

    # 检查文件行数是否足够
    if len(texts) < config['END_INDEX']:
        print(f"✗ 文本文件行数不足: 需要{config['END_INDEX']}行，实际{len(texts)}行")
        return False

    # 2. 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)

    # 3. 检查已存在的文件
    existing_files = get_existing_files(config["OUTPUT_DIR"], config['START_INDEX'], config['END_INDEX'])
    print(f"已存在音频文件: {len(existing_files)} 个")

    # 4. 计算需要生成的文件
    indices_to_generate = []
    for i in range(config['START_INDEX'], config['END_INDEX'] + 1):
        if i not in existing_files:
            indices_to_generate.append(i)

    if not indices_to_generate:
        print("✓ 所有文件已存在，无需生成")
        return True

    print(f"需要生成: {len(indices_to_generate)} 个文件")

    # 5. 确认生成
    try:
        confirm = input(f"是否开始生成 {len(indices_to_generate)} 个音频文件？(y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("已取消生成")
            return False
    except KeyboardInterrupt:
        print("\n程序被中断")
        return False

    # 6. 初始化TTS客户端
    try:
        tts = AliyunTTSDirect(config)
    except Exception as e:
        print(f"✗ 初始化TTS客户端失败: {e}")
        return False

    # 7. 开始生成
    success_count = 0
    error_count = 0

    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    csv_exists = os.path.exists(csv_path)

    print(f"\n开始生成音频文件...")
    start_time = time.time()

    # 写入CSV记录（制表符分隔格式）
    with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
        if not csv_exists:
            csvfile.write('音频名\t类型\t文本\t注音1\n')

        for i, index in enumerate(indices_to_generate, 1):
            # 获取文本和拼音（注意：数组索引从0开始，但我们的index从7001开始）
            text_index = index - 1  # 转换为数组索引
            text = texts[text_index]
            pinyin = pinyins[text_index] if text_index < len(pinyins) else ""
            audio_name = f"Sc{index:07d}"

            print(f"[{i}/{len(indices_to_generate)}] {audio_name}: {text[:30]}{'...' if len(text) > 30 else ''}")

            # 检查文件是否已存在（双重检查）
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")
            if os.path.exists(audio_path):
                print(f"  跳过已存在文件")
                continue

            # 生成音频（带重试）
            audio_data = None
            for retry in range(3):
                audio_data = tts.synthesize_text(text)
                if audio_data:
                    break
                if retry < 2:
                    print(f"  重试 {retry + 1}/3...")
                    time.sleep(2)

            if audio_data:
                try:
                    # 保存音频文件
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)

                    # 写入CSV记录（制表符分隔格式）
                    csvfile.write(f"{audio_name}\tc\t{text}\t{pinyin}\n")
                    csvfile.flush()  # 立即写入文件
                    print(f"  ✓ 成功 ({len(audio_data)} 字节)")
                    success_count += 1

                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1

            # 请求间隔
            time.sleep(config["REQUEST_INTERVAL"])

            # 每50个显示进度
            if i % 50 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(indices_to_generate) - i) * avg_time
                print(f"  进度: {i}/{len(indices_to_generate)}, 预计剩余: {remaining/60:.1f}分钟")

    elapsed_time = time.time() - start_time
    print(f"\n=== 生成完成 ===")
    print(f"耗时: {elapsed_time/60:.1f}分钟")
    print(f"成功: {success_count}")
    print(f"失败: {error_count}")
    print(f"CSV记录文件: {csv_path}")

    return error_count == 0

def run_test():
    """运行测试模式"""
    print("=" * 60)
    print("阿里云TTS配置测试")
    print("=" * 60)

    # 测试配置
    test_configuration()

    # 测试文件
    test_files()

    # 测试API连接
    api_success = test_api_connection()

    print("\n" + "=" * 60)
    if api_success:
        print("✓ 所有测试通过！可以开始生成音频")
        print("运行命令: python aliyun_tts_complete.py --generate")
    else:
        print("✗ 测试未完全通过，请检查配置")
    print("=" * 60)

def run_generate():
    """运行生成模式"""
    print("=" * 60)
    print("阿里云TTS音频生成器 - 第7001-8000条")
    print("=" * 60)

    config = ALIYUN_CONFIG

    # 显示配置信息
    print("配置信息:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  拼音文件: {config['PINYIN_FILE']}")
    print(f"  生成范围: {config['START_INDEX']} - {config['END_INDEX']}")
    print(f"  输出目录: {config['OUTPUT_DIR']}")
    print(f"  CSV文件: {config['CSV_FILE']}")
    print(f"  语音类型: {config['VOICE']}")
    print("=" * 60)

    # 检查必要文件
    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return

    if not os.path.exists(config['PINYIN_FILE']):
        print(f"✗ 拼音文件不存在: {config['PINYIN_FILE']}")
        return

    # 开始生成
    success = generate_audio_batch(config)

    if success:
        print("\n🎉 音频生成任务完成！")
    else:
        print("\n⚠️ 音频生成任务未完全成功")

    # 最终统计
    final_existing = get_existing_files(config["OUTPUT_DIR"], config['START_INDEX'], config['END_INDEX'])
    print(f"\n最终统计:")
    print(f"  目标范围({config['START_INDEX']}-{config['END_INDEX']})内文件: {len(final_existing)}/{config['TOTAL_COUNT']}")

    if len(final_existing) == config['TOTAL_COUNT']:
        print("✓ 所有目标文件生成完成！")
    else:
        missing_count = config['TOTAL_COUNT'] - len(final_existing)
        print(f"⚠️ 还缺少 {missing_count} 个文件")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='阿里云TTS音频生成器')
    parser.add_argument('--test', '-t', action='store_true',
                       help='运行测试模式，检查配置和API连接')
    parser.add_argument('--generate', '-g', action='store_true',
                       help='运行生成模式，开始生成音频文件')

    args = parser.parse_args()

    if args.test:
        run_test()
    elif args.generate:
        run_generate()
    else:
        # 默认显示帮助和选项
        print("=" * 60)
        print("阿里云TTS音频生成器 - 完整版")
        print("生成第7001-8000条音频文件")
        print("=" * 60)
        print("\n使用方法:")
        print("  测试配置: python aliyun_tts_complete.py --test")
        print("  生成音频: python aliyun_tts_complete.py --generate")
        print("\n配置信息:")
        print(f"  生成范围: {ALIYUN_CONFIG['START_INDEX']} - {ALIYUN_CONFIG['END_INDEX']}")
        print(f"  输出目录: {ALIYUN_CONFIG['OUTPUT_DIR']}")
        print(f"  CSV文件: {ALIYUN_CONFIG['CSV_FILE']}")
        print(f"  发音人: {ALIYUN_CONFIG['VOICE']}")
        print("\n建议先运行测试模式检查配置！")
        print("=" * 60)

if __name__ == "__main__":
    main()
