#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
华为云TTS多音色测试版
测试多种音色的可用性
"""

import io
import os
import sys
import json
import pycurl
import requests
import time
import csv
from datetime import datetime

# ==================== 测试配置参数 ====================
TEST_CONFIG = {
    # 华为云API配置
    "AK": "EC7BD181019626B182FC676E6EBEB83D",
    "SK": "C2C7CD527D0A8D3E77C8C6BD669CFED8",
    "AUTH_URL": "https://10.150.4.10:8080/auth/v3/generateToken",
    "TTS_URL": "https://10.150.4.10:8080/hivoice/v3/tts",
    
    # 音频参数配置
    "COMPRESS_RATE": 0,
    "SPEED": 100,
    "VOLUME": 140,
    "PITCH": 100,
    "SAMPLE_RATE": 16000, 
    
    # 文件配置
    "TEXT_FILE": "../shuffled_from_rank_random.txt",
    "PINYIN_FILE": "../shuffled_from_rank_random_pinyin.txt",
    "OUTPUT_DIR": "audio_output",
    "CSV_FILE": "huawei_multi_voice_test.csv",
    "REQUEST_INTERVAL": 1.0,  # 测试时稍微慢一点
}

# 测试音色配置 - 5种音色各生成1个文件
TEST_VOICES = [
    {"voice_id": "0", "voice_name": "小艺"},
    {"voice_id": "1", "voice_name": "yoyo"},
    {"voice_id": "2", "voice_name": "童声"},
    {"voice_id": "3", "voice_name": "男声"},
    {"voice_id": "6", "voice_name": "HMS女声---一菲"},
]

class HuaweiTTSTest:
    """华为云TTS测试核心类"""
    
    def __init__(self, config):
        self.config = config
        self.token = None
        
    def get_token(self):
        """获取华为云访问令牌"""
        print("正在获取华为云访问令牌...")
        
        url = self.config["AUTH_URL"]
        
        headers = {
            'Content-Type': "application/json",
            'sender': "APP",
            'receiver': "AS", 
            'deviceId': "Testbywanyahua",
            'sessionId': "testsff12345",
            'interactionId': "1",
            'locate': "CN",
            'appVersion': "11-0"
        }
        
        body = {
            'ak': self.config["AK"],
            'sk': self.config["SK"]
        }
        
        payload = json.dumps(body)
        
        try:
            response = requests.post(
                url=url,
                data=payload,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                print('✓ 成功获取访问令牌')
                data = response.text.split()[-2]
                token = json.loads(data)['accessToken']
                self.token = token
                return token
            else:
                print(f'✗ 获取令牌失败: {response.status_code} - {response.text}')
                return None
        except Exception as e:
            print(f'✗ 获取令牌异常: {e}')
            return None
    
    def text_to_speech(self, text, person_id, index):
        """文本转语音"""
        if not self.token:
            self.token = self.get_token()
            if not self.token:
                return None
                
        url = self.config["TTS_URL"]
        request_id = f"test_request_{index}_{int(time.time())}"
        
        headers = [
            "messageName: text2audio",
            "deviceId: Testbywanyahua", 
            "appName: com.huawei.vassistant",
            "appVersion: 11.0.12.20",
            "deviceCategory: phone",
            f"token: Bearer {self.token}",
            "sender: APP",
            "receiver: TTS",
            "sessionId: testsff12345",
            "interactionId: 1",
            "locate: CN",
            "language: zh",
            f"person: {person_id}"
        ]
        
        body = {
            "session": {
                "appId": "",
                "devF": "GWQ**00307",
                "dialogId": 0,
                "interactionId": 0,
                "isExperiencePlan": False,
                "isFinish": False,
                "messageId": "06cff0ec-2430-4992-a54a-3ec6fe7c984f",
                "sender": "com.huawei.vassistant",
                "sessionId": "0f026d5a-83ab-4f9b-9929-df1c8365b2bf"
            },
            "contexts": [{
                "header": {
                    "namespace": "System",
                    "name": "Device"
                },
                "payload": {
                    "deviceName": "MHA",
                    "deviceType": "M300-AL00",
                    "osType": "android",
                    "osVersion": "1.0.0",
                    "sysVersion": "11.0",
                    "romVersion": "PE_TL10C00B560",
                    "mccmnc": "",
                    "appInfo": "tts1.0.0",
                    "net": "4G",
                    "deltaPlatformVer": "-1",
                    "minApiLevel": "0",
                    "deviceBrand": "HUAWEI",
                    "screenOrientation": "portrait",
                    "voiceKitVersion": "10.1.3.200",
                    "securityLevel": "unlocked"
                }
            }],
            "events": [{
                "header": {
                    "namespace": "TTS",
                    "name": "dospeak"
                },
                "payload": {
                    "token": "",
                    "device_id": "Testbywyh",
                    "request_id": request_id,
                    "text": text,
                    "device_type": 0,
                    "compress_rate": self.config["COMPRESS_RATE"],
                    "speed": self.config["SPEED"],
                    "volume": self.config["VOLUME"],
                    "pitch": self.config["PITCH"],
                    "person": int(person_id),
                    "style": "",
                    "sample_rate": self.config["SAMPLE_RATE"]
                }
            }]
        }
        
        payload = json.dumps(body)
        buf = io.BytesIO()
        
        try:
            curl = pycurl.Curl()
            curl.setopt(pycurl.WRITEFUNCTION, buf.write)
            curl.setopt(pycurl.URL, url)
            curl.setopt(pycurl.HTTPHEADER, headers)
            curl.setopt(pycurl.POSTFIELDS, payload)
            curl.perform()
            
            res_code = curl.getinfo(pycurl.HTTP_CODE)
            res = buf.getvalue()
            
            if res_code == 200:
                return res
            else:
                try:
                    error_info = json.loads(res)
                    print(f"  ✗ API错误: {error_info.get('message', '未知错误')}")
                except:
                    print(f"  ✗ HTTP错误 {res_code}: {res[:200]}")
                return None
        except Exception as e:
            print(f"  ✗ 合成异常: {e}")
            return None
        finally:
            buf.close()
            curl.close()

def test_huawei_multi_voices():
    """测试华为云多音色功能"""
    print("=" * 60)
    print("华为云TTS多音色测试")
    print("=" * 60)
    
    config = TEST_CONFIG
    
    # 检查必要文件
    if not os.path.exists(config['TEXT_FILE']):
        print(f"✗ 文本文件不存在: {config['TEXT_FILE']}")
        return False
    
    # 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)
    
    # 读取测试文本
    try:
        with open(config['TEXT_FILE'], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        print(f"✓ 加载文本文件: {len(texts)} 行")
    except Exception as e:
        print(f"✗ 读取文本文件失败: {e}")
        return False
    
    # 读取拼音文件
    pinyins = []
    if os.path.exists(config['PINYIN_FILE']):
        try:
            with open(config['PINYIN_FILE'], 'r', encoding='utf-8') as f:
                pinyins = [line.strip() for line in f if line.strip()]
            print(f"✓ 加载拼音文件: {len(pinyins)} 行")
        except Exception as e:
            print(f"读取拼音文件失败: {e}")
    
    # 创建TTS对象
    tts = HuaweiTTSTest(config)
    
    # 统计信息
    success_count = 0
    error_count = 0
    
    # 创建CSV文件
    csv_path = os.path.join(config["OUTPUT_DIR"], config["CSV_FILE"])
    with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        csvfile.write('音频名\t类型\t文本\t注音1\t音色\n')
        
        print(f"\n开始测试 {len(TEST_VOICES)} 种音色:")
        
        for i, voice_config in enumerate(TEST_VOICES, 1):
            voice_id = voice_config["voice_id"]
            voice_name = voice_config["voice_name"]
            
            # 使用第i个文本进行测试
            text_index = i - 1
            text = texts[text_index] if text_index < len(texts) else "这是一个测试文本"
            pinyin = pinyins[text_index] if text_index < len(pinyins) else ""
            
            audio_name = f"test_huawei_{voice_id}"
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")
            
            print(f"\n[{i}/{len(TEST_VOICES)}] 测试 {voice_name} (ID: {voice_id})")
            print(f"  文本: {text[:50]}{'...' if len(text) > 50 else ''}")
            
            # 生成音频
            audio_data = tts.text_to_speech(text, voice_id, i)
            if audio_data:
                try:
                    # 保存音频文件
                    with open(audio_path, "wb") as f:
                        f.write(audio_data)
                    
                    # 写入CSV记录
                    csvfile.write(f"{audio_name}\tc\t{text}\t{pinyin}\t{voice_name}\n")
                    csvfile.flush()
                    
                    print(f"  ✓ 成功 ({len(audio_data)} 字节) -> {audio_name}.wav")
                    success_count += 1
                    
                except Exception as e:
                    print(f"  ✗ 保存失败: {e}")
                    error_count += 1
            else:
                print(f"  ✗ 生成失败")
                error_count += 1
            
            # 请求间隔
            time.sleep(config["REQUEST_INTERVAL"])
    
    print(f"\n=== 测试完成 ===")
    print(f"成功: {success_count}/{len(TEST_VOICES)}")
    print(f"失败: {error_count}")
    print(f"输出目录: {config['OUTPUT_DIR']}")
    print(f"CSV记录: {csv_path}")
    
    if success_count == len(TEST_VOICES):
        print("🎉 所有音色测试成功！可以开始批量生成")
        return True
    else:
        print("⚠️ 部分音色测试失败，请检查配置")
        return False

def main():
    """主函数"""
    success = test_huawei_multi_voices()
    
    if success:
        print(f"\n💡 测试成功！")
        print(f"现在可以运行 huawei_tts_multi_voice.py 进行批量生成")
    else:
        print(f"\n⚠️ 测试失败，请检查配置和网络连接")

if __name__ == "__main__":
    main()
