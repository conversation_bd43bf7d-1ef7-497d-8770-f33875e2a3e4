#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
乱序文本生成器 - 去重版本
主要功能：
1. 文本长度限制为2-30字符
2. 处理拼音变调规则（连续三声、重叠词轻声）
3. 去除标点符号，生成纯文本
4. 处理拼音中的单引号连接
5. 与已存在文本进行去重检测
"""

import random
import numpy as np
import re
import os

def process_tone_sandhi(pinyin_list, word_list):
    """
    处理拼音变调规则
    1. 对于二字词，如果两个字都是三声，将第一个字改为二声
    2. 对于重叠词（两个相同字组成），将第二个字改为轻声（去除数字）
    """
    processed_pinyins = []
    
    for i, pinyin in enumerate(pinyin_list):
        word = word_list[i] if i < len(word_list) else ""
        
        # 处理单引号连接的拼音，分割成单个字的拼音
        if "'" in pinyin:
            # 分割拼音并用空格连接
            syllables = pinyin.split("'")
        else:
            # 如果没有单引号，尝试其他分割方法
            syllables = [pinyin]
        
        # 检查是否为二字词
        if len(syllables) == 2 and len(word) == 2:
            # 检查是否为重叠词（两个相同的字）
            if word[0] == word[1]:
                # 重叠词：第二个字改为轻声（去除数字）
                syllables[1] = re.sub(r'\d+$', '', syllables[1])
            else:
                # 非重叠词：检查是否都是三声
                if syllables[0].endswith('3') and syllables[1].endswith('3'):
                    # 将第一个字的三声改为二声
                    syllables[0] = syllables[0][:-1] + '2'
        
        # 用空格连接音节
        processed_pinyin = ' '.join(syllables)
        processed_pinyins.append(processed_pinyin)
    
    return processed_pinyins

def load_existing_texts(existing_file_path):
    """加载已存在的文本，用于去重检测"""
    existing_texts = set()
    if os.path.exists(existing_file_path):
        try:
            with open(existing_file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    text = line.strip()
                    if text:
                        existing_texts.add(text)
            print(f"加载已存在文本: {len(existing_texts)} 条")
        except Exception as e:
            print(f"读取已存在文本失败: {e}")
    else:
        print(f"未找到已存在文本文件: {existing_file_path}")
    return existing_texts

def generate_unique_sentence(words, pinyins, weights, target_length, existing_texts, max_attempts=50):
    """生成一个不与已存在文本重复的句子"""
    for attempt in range(max_attempts):
        # 动态选择词语直到达到目标长度
        selected_words = []
        selected_pinyins = []
        current_length = 0
        
        while current_length < target_length:
            # 选择一个词语
            selected_index = np.random.choice(len(words), p=weights)
            word = words[selected_index]
            pinyin = pinyins[selected_index]
            
            # 检查添加这个词是否会超过目标长度
            if current_length + len(word) <= target_length:
                selected_words.append(word)
                selected_pinyins.append(pinyin)
                current_length += len(word)
            elif current_length == 0:
                # 如果是第一个词且超长，仍然添加（确保至少有一个词）
                selected_words.append(word)
                selected_pinyins.append(pinyin)
                current_length += len(word)
                break
            else:
                # 如果添加会超长，尝试找一个更短的词
                attempts = 0
                while attempts < 10:  # 最多尝试10次
                    selected_index = np.random.choice(len(words), p=weights)
                    word = words[selected_index]
                    pinyin = pinyins[selected_index]
                    
                    if current_length + len(word) <= target_length:
                        selected_words.append(word)
                        selected_pinyins.append(pinyin)
                        current_length += len(word)
                        break
                    attempts += 1
                
                # 如果找不到合适的词，就结束
                if attempts >= 10:
                    break

        # 构建纯文本句子（不添加标点符号）
        sentence = ''.join(selected_words)
        
        # 检查是否与已存在文本重复
        if sentence not in existing_texts:
            # 处理拼音：应用变调规则和格式化
            processed_pinyins = process_tone_sandhi(selected_pinyins, selected_words)
            pinyin_sentence = ' '.join(processed_pinyins)
            return sentence, pinyin_sentence
    
    # 如果尝试多次仍然重复，返回None
    return None, None

def generate_shuffled_text_with_dedup(txt_path, output_path, num_sentences=10000, existing_file_path="shuffled_from_rank_random.txt"):
    """
    修改版乱序文本生成函数 - 带去重检测
    
    参数:
    txt_path: 词典文件路径（格式：词语\t拼音\t频序号）
    output_path: 输出文件路径
    num_sentences: 生成句子数量
    existing_file_path: 已存在文本文件路径，用于去重
    """
    # 加载已存在的文本
    existing_texts = load_existing_texts(existing_file_path)
    
    # 加载词典数据
    word_data = []
    with open(txt_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue  # 跳过注释行
            parts = line.split('\t')  # 使用制表符分隔
            if len(parts) < 3:
                continue
            word, pinyin, rank_str = parts[0], parts[1], parts[2]
            try:
                rank = int(rank_str)
                word_data.append({
                    'word': word,
                    'pinyin': pinyin,
                    'rank': rank
                })
            except ValueError:
                continue  # 忽略无法转为整数的 rank

    print(f"加载词语数: {len(word_data)}")
    
    # 准备加权随机选择
    words = [item['word'] for item in word_data]
    pinyins = [item['pinyin'] for item in word_data]
    ranks = np.array([item['rank'] for item in word_data])

    # 频序转权重 (rank 1 → 权重最高)
    base = 1.5  # 基础衰减率
    k = 20      # 衰减速度控制
    weights = base ** (-ranks / k)
    weights /= weights.sum()  # 归一化

    # 存储所有生成的句子（用于后续排序）
    all_sentences = []
    all_pinyin_sentences = []
    
    # 统计信息
    duplicate_count = 0
    failed_count = 0

    # 生成乱序文本
    for i in range(num_sentences):
        # 随机生成文本长度 (2-30字符)
        text_length = random.randint(2, 30)
        
        # 生成不重复的句子
        sentence, pinyin_sentence = generate_unique_sentence(
            words, pinyins, weights, text_length, existing_texts, max_attempts=50
        )
        
        if sentence is not None:
            # 存储句子和长度（用于排序）
            all_sentences.append((len(sentence), sentence))
            all_pinyin_sentences.append((len(sentence), pinyin_sentence))
            
            # 将新生成的句子加入已存在文本集合，避免后续重复
            existing_texts.add(sentence)
        else:
            # 生成失败，尝试生成更简单的句子
            failed_count += 1
            # 使用更短的目标长度重试
            shorter_length = random.randint(2, min(10, text_length))
            sentence, pinyin_sentence = generate_unique_sentence(
                words, pinyins, weights, shorter_length, existing_texts, max_attempts=20
            )
            
            if sentence is not None:
                all_sentences.append((len(sentence), sentence))
                all_pinyin_sentences.append((len(sentence), pinyin_sentence))
                existing_texts.add(sentence)
            else:
                duplicate_count += 1

        # 打印进度
        if i % 1000 == 0:
            print(f"已生成 {i}/{num_sentences} 句，重复/失败: {duplicate_count + failed_count}")

    # 按句子长度排序（从短到长）
    all_sentences.sort(key=lambda x: x[0])
    all_pinyin_sentences.sort(key=lambda x: x[0])

    # 写入文件（按长度排序后的结果）
    with open(output_path, 'w', encoding='utf-8') as out:
        pinyin_output = output_path.replace('.txt', '_pinyin.txt')
        with open(pinyin_output, 'w', encoding='utf-8') as pinyin_out:
            # 写入文本文件
            for _, sentence in all_sentences:
                out.write(sentence + '\n')
            
            # 写入拼音文件
            for _, pinyin_sentence in all_pinyin_sentences:
                pinyin_out.write(pinyin_sentence + '\n')

    print(f"生成完成! 保存至: {output_path}, {pinyin_output}")
    print(f"实际生成: {len(all_sentences)} 句")
    print(f"重复/失败: {duplicate_count + failed_count} 句")
    print(f"最短句子长度: {all_sentences[0][0]} 字")
    print(f"最长句子长度: {all_sentences[-1][0]} 字")
    
    # 统计长度分布
    length_counts = {}
    for length, _ in all_sentences:
        length_counts[length] = length_counts.get(length, 0) + 1
    
    print(f"\n长度分布统计:")
    for length in sorted(length_counts.keys())[:10]:  # 显示前10个长度的统计
        print(f"  {length}字: {length_counts[length]} 句")
    
    return len(all_sentences), duplicate_count + failed_count

def test_tone_sandhi():
    """测试变调规则函数"""
    print("=== 测试变调规则 ===")
    
    test_cases = [
        ("lao3'shi1", "老师"),   # 老师 - 第一个字是三声，第二个字是一声，不变调
        ("hao3'hao3", "好好"),   # 好好 - 重叠词，第二个字改为轻声
        ("mei3'li4", "美丽"),    # 美丽 - 第一个三声，第二个四声，不变调
        ("xiao3'jie3", "小姐"),  # 小姐 - 两个三声，第一个改为二声
        ("da4'jia1", "大家"),    # 大家 - 不是三声，不变调
        ("ni3'hao3", "你好"),    # 你好 - 两个三声，第一个改为二声
        ("ge1'ge1", "哥哥"),     # 哥哥 - 重叠词，第二个字改为轻声
        ("ma1'ma1", "妈妈"),     # 妈妈 - 重叠词，第二个字改为轻声
        ("kan4'kan4", "看看"),   # 看看 - 重叠词，第二个字改为轻声
    ]
    
    for pinyin, word in test_cases:
        result = process_tone_sandhi([pinyin], [word])
        print(f"  {word}({pinyin}) -> {result[0]}")

def main():
    """主函数"""
    print("=" * 60)
    print("乱序文本生成器 - 去重版本")
    print("=" * 60)
    
    # 测试变调规则
    test_tone_sandhi()
    
    print("\n" + "=" * 60)
    print("开始生成去重乱序文本")
    print("=" * 60)
    
    # 生成乱序文本
    success_count, failed_count = generate_shuffled_text_with_dedup(
        txt_path="现代汉语常用词表 .txt",
        output_path='shuffled_text_dedup.txt',
        num_sentences=40000,
        existing_file_path="shuffled_text_random.txt"
    )
    
    print(f"\n=== 最终统计 ===")
    print(f"成功生成: {success_count} 句")
    print(f"重复/失败: {failed_count} 句")
    print(f"去重率: {(failed_count / 40000) * 100:.2f}%")
    
    if success_count >= 36000:  # 如果成功率超过90%
        print("🎉 去重文本生成成功！")
    else:
        print("⚠️ 去重效果不理想，可能需要调整参数")

if __name__ == "__main__":
    main()
