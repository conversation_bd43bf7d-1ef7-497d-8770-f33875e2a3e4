# ============================================================================
# Jupyter Notebook TTS解决方案
# 复制以下代码到Jupyter Notebook的不同单元格中运行
# ============================================================================

# ===== 单元格 1: 安装依赖 =====
"""
!pip install edge-tts pandas nest-asyncio
"""

# ===== 单元格 2: 导入库和基础设置 =====
import asyncio
import json
import logging
import uuid
import os
import time
import pandas as pd
from pathlib import Path
import nest_asyncio

# 允许在Jupyter中运行异步代码
nest_asyncio.apply()

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print("✓ 基础环境设置完成")

# ===== 单元格 3: Edge TTS引擎类 =====
class EdgeTTSEngine:
    """微软Edge TTS引擎 - 免费且稳定"""
    
    def __init__(self):
        self.name = "Edge TTS"
        self.success_count = 0
        self.error_count = 0
        
        # 中文音色映射
        self.voices = {
            "D001": {"voice": "zh-CN-XiaoxiaoNeural", "name": "晓晓(女)"},
            "D002": {"voice": "zh-CN-YunxiNeural", "name": "云希(男)"},
            "D003": {"voice": "zh-CN-XiaoyiNeural", "name": "晓伊(女)"},
            "D004": {"voice": "zh-CN-YunyangNeural", "name": "云扬(男)"},
            "D005": {"voice": "zh-CN-XiaohanNeural", "name": "晓涵(女)"},
            "D006": {"voice": "zh-CN-XiaomengNeural", "name": "晓梦(女)"},
            "D007": {"voice": "zh-CN-XiaomoNeural", "name": "晓墨(女)"},
            "D008": {"voice": "zh-CN-XiaoqiuNeural", "name": "晓秋(女)"},
            "D009": {"voice": "zh-CN-XiaoruiNeural", "name": "晓睿(女)"},
            "D010": {"voice": "zh-CN-XiaoshuangNeural", "name": "晓双(女)"},
        }
    
    async def synthesize(self, text, voice_code, output_path, speed=1.0):
        """合成音频"""
        try:
            import edge_tts
            
            # 获取音色
            voice_info = self.voices.get(voice_code, self.voices["D001"])
            edge_voice = voice_info["voice"]
            
            # 语速调整
            if speed != 1.0:
                rate = f"{(speed - 1) * 100:+.0f}%"
                communicate = edge_tts.Communicate(text, edge_voice, rate=rate)
            else:
                communicate = edge_tts.Communicate(text, edge_voice)
            
            await communicate.save(output_path)
            self.success_count += 1
            return True
            
        except Exception as e:
            logger.error(f"Edge TTS合成失败: {e}")
            self.error_count += 1
            return False
    
    def get_stats(self):
        total = self.success_count + self.error_count
        success_rate = (self.success_count / total * 100) if total > 0 else 0
        return {
            "success": self.success_count,
            "error": self.error_count,
            "success_rate": success_rate
        }

print("✓ Edge TTS引擎类定义完成")

# ===== 单元格 4: 简单TTS生成器 =====
class SimpleTTSGenerator:
    """简化的TTS生成器"""
    
    def __init__(self, output_dir="audio_output_jupyter"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.engine = EdgeTTSEngine()
        
    def load_texts(self, file_path, max_count=100):
        """加载文本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                texts = [line.strip() for line in f if line.strip()][:max_count]
            print(f"✓ 加载文本: {len(texts)} 条")
            return texts
        except Exception as e:
            print(f"✗ 加载文本失败: {e}")
            return []
    
    def select_texts_by_length(self, texts, target_count=50):
        """按长度均匀选择文本"""
        if len(texts) <= target_count:
            return texts
        
        # 按长度分组
        length_groups = {}
        for text in texts:
            length = len(text)
            if length not in length_groups:
                length_groups[length] = []
            length_groups[length].append(text)
        
        # 均匀选择
        selected = []
        lengths = sorted(length_groups.keys())
        per_length = max(1, target_count // len(lengths))
        
        for length in lengths:
            group_texts = length_groups[length][:per_length]
            selected.extend(group_texts)
            if len(selected) >= target_count:
                break
        
        return selected[:target_count]
    
    async def generate_single_voice(self, texts, voice_code, speed=1.0):
        """生成单个音色的音频"""
        voice_info = self.engine.voices.get(voice_code, self.engine.voices["D001"])
        voice_name = voice_info["name"]
        
        print(f"\n=== 生成音色: {voice_name} ({voice_code}) ===")
        print(f"文本数量: {len(texts)}")
        print(f"语速: {speed}x")
        
        records = []
        success_count = 0
        
        for i, text in enumerate(texts, 1):
            filename = f"Sc{voice_code}{i:07d}.wav"
            filepath = self.output_dir / filename
            
            print(f"[{i}/{len(texts)}] {text[:30]}{'...' if len(text) > 30 else ''}")
            
            # 检查文件是否已存在
            if filepath.exists():
                print(f"  跳过已存在文件")
                success_count += 1
                continue
            
            # 合成音频
            success = await self.engine.synthesize(text, voice_code, str(filepath), speed)
            
            if success:
                print(f"  ✓ 成功")
                success_count += 1
                
                # 记录
                records.append({
                    "音频名": filename.replace('.wav', ''),
                    "类型": "c",
                    "文本": text,
                    "注音": "",
                    "音色": voice_name
                })
            else:
                print(f"  ✗ 失败")
            
            # 间隔
            await asyncio.sleep(0.5)
        
        # 保存CSV
        if records:
            csv_path = self.output_dir / f"tts_log_{voice_code}.csv"
            df = pd.DataFrame(records)
            df.to_csv(csv_path, index=False, encoding='utf-8-sig', sep='\t')
            print(f"✓ CSV保存: {csv_path}")
        
        print(f"{voice_name} 完成: {success_count}/{len(texts)}")
        return success_count, len(texts) - success_count
    
    async def generate_multiple_voices(self, texts, voice_codes, speed=1.0):
        """生成多个音色的音频"""
        print(f"=== 开始批量生成 ===")
        print(f"文本数量: {len(texts)}")
        print(f"音色数量: {len(voice_codes)}")
        
        total_success = 0
        total_error = 0
        
        for i, voice_code in enumerate(voice_codes, 1):
            print(f"\n进度: {i}/{len(voice_codes)}")
            
            success, error = await self.generate_single_voice(texts, voice_code, speed)
            total_success += success
            total_error += error
        
        # 最终统计
        stats = self.engine.get_stats()
        print(f"\n=== 生成完成 ===")
        print(f"总成功: {total_success}")
        print(f"总失败: {total_error}")
        print(f"成功率: {stats['success_rate']:.1f}%")
        print(f"输出目录: {self.output_dir}")
        
        return stats

print("✓ 简单TTS生成器定义完成")

# ===== 单元格 5: 快速测试 =====
async def quick_test():
    """快速测试Edge TTS"""
    print("=== Edge TTS快速测试 ===")
    
    generator = SimpleTTSGenerator("test_output")
    
    # 测试文本
    test_texts = ["你好", "测试", "中国", "北京", "上海"]
    
    # 测试单个音色
    await generator.generate_single_voice(test_texts, "D001", speed=1.0)
    
    print("✓ 快速测试完成")

# 运行快速测试
await quick_test()

# ===== 单元格 6: 完整生成示例 =====
async def full_generation_example():
    """完整生成示例"""
    print("=== 完整生成示例 ===")
    
    # 创建生成器
    generator = SimpleTTSGenerator("audio_output_full")
    
    # 方式1: 使用预定义文本
    sample_texts = [
        "你好", "测试", "中国", "北京", "上海", "广州", "深圳", 
        "杭州", "成都", "西安", "南京", "武汉", "天津", "重庆", "苏州"
    ]
    
    # 方式2: 从文件加载（如果文件存在）
    file_path = "../shuffled_from_rank_random.txt"
    if os.path.exists(file_path):
        loaded_texts = generator.load_texts(file_path, max_count=100)
        if loaded_texts:
            # 选择合适数量的文本
            selected_texts = generator.select_texts_by_length(loaded_texts, 20)
            print(f"使用文件中的文本: {len(selected_texts)} 条")
        else:
            selected_texts = sample_texts
            print("使用预定义文本")
    else:
        selected_texts = sample_texts
        print("使用预定义文本")
    
    # 选择音色
    voice_codes = ["D001", "D002", "D003"]  # 3种音色
    
    # 设置语速
    speed = 1.0  # 正常语速
    
    # 开始生成
    stats = await generator.generate_multiple_voices(selected_texts, voice_codes, speed)
    
    return stats

# 运行完整生成（注释掉，需要时取消注释）
# stats = await full_generation_example()

print("✓ 所有代码单元格准备完成")
print("\n使用说明:")
print("1. 运行 quick_test() 进行快速测试")
print("2. 运行 full_generation_example() 进行完整生成")
print("3. 可以自定义文本、音色和语速")

# ===== 单元格 7: 自定义生成 =====
def create_custom_generator():
    """创建自定义生成器供用户使用"""
    
    # 创建生成器实例
    generator = SimpleTTSGenerator("my_audio_output")
    
    # 显示可用音色
    print("可用音色:")
    for code, info in generator.engine.voices.items():
        print(f"  {code}: {info['name']}")
    
    return generator

# 创建生成器实例供用户使用
my_generator = create_custom_generator()

# 使用示例:
# await my_generator.generate_single_voice(["你好", "世界"], "D001", speed=1.2)

print("\n✅ Jupyter TTS解决方案准备完成!")
print("现在可以使用 my_generator 进行音频生成")
