#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包TTS修复版 - 解决编码和API兼容性问题
"""

import asyncio
import json
import logging
import uuid
import os
import time
import re
import unicodedata
from datetime import datetime
import websockets
from protocols import MsgType, full_client_request, receive_message, is_audio_message, is_final_message, extract_audio_data

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 修复版配置
FIXED_CONFIG = {
    # API配置
    "APPID": "9862368305",
    "ACCESS_TOKEN": "bbxNaAzRzBSkySjouDItXl6tBUUyNd2W",
    
    # WebSocket配置
    "ENDPOINT": "wss://openspeech.bytedance.com/api/v1/tts/ws_binary",
    
    # 音频格式配置
    "ENCODING": "wav",
    
    # 文件配置
    "TEXT_FILE": "../shuffled_from_rank_random.txt",
    "PINYIN_FILE": "../shuffled_from_rank_random_pinyin.txt",
    "OUTPUT_DIR": "audio_output_fixed",
    "CSV_FILE": "douyin_fixed_audio_log.csv",
    
    # 网络配置
    "REQUEST_INTERVAL": 2.0,
    "MAX_RETRIES": 3,
    "CONNECTION_TIMEOUT": 20,
}

def clean_text(text):
    """清理和标准化文本"""
    if not text:
        return ""
    
    # 移除控制字符
    text = ''.join(char for char in text if unicodedata.category(char)[0] != 'C')
    
    # 标准化Unicode
    text = unicodedata.normalize('NFC', text)
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', '', text)
    
    # 只保留中文字符、数字、字母和基本标点
    text = re.sub(r'[^\u4e00-\u9fff\u3400-\u4dbf\w\s，。！？；：""''（）【】《》]', '', text)
    
    # 确保文本不为空且长度合理
    text = text.strip()
    if len(text) < 1:
        return None
    if len(text) > 100:  # 限制最大长度
        text = text[:100]
    
    return text

def get_cluster(voice: str) -> str:
    """获取集群名称"""
    if voice.startswith("ICL_"):
        return "volcano_icl"
    return "volcano_tts"

class FixedDouyinTTS:
    """修复版豆包TTS类"""
    
    def __init__(self, config):
        self.config = config
        self.headers = {
            "Authorization": f"Bearer;{config['ACCESS_TOKEN']}",
        }
    
    async def synthesize_text(self, text, voice_type):
        """合成文本，带文本清理和错误处理"""
        # 清理文本
        cleaned_text = clean_text(text)
        if not cleaned_text:
            logger.error(f"文本清理后为空: {text}")
            return None
        
        logger.info(f"合成文本: '{cleaned_text}' (原文: '{text}')")
        
        for attempt in range(self.config["MAX_RETRIES"]):
            try:
                # 创建WebSocket连接
                websocket = await asyncio.wait_for(
                    websockets.connect(
                        self.config["ENDPOINT"],
                        additional_headers=self.headers,
                        max_size=10 * 1024 * 1024,
                        ping_interval=20,
                        ping_timeout=10
                    ),
                    timeout=self.config["CONNECTION_TIMEOUT"]
                )
                
                try:
                    # 确定集群
                    cluster = get_cluster(voice_type)
                    
                    # 准备请求数据
                    request_data = {
                        "app": {
                            "appid": self.config["APPID"],
                            "token": self.config["ACCESS_TOKEN"],
                            "cluster": cluster,
                        },
                        "user": {
                            "uid": str(uuid.uuid4()),
                        },
                        "audio": {
                            "voice_type": voice_type,
                            "encoding": self.config["ENCODING"],
                        },
                        "request": {
                            "reqid": str(uuid.uuid4()),
                            "text": cleaned_text,  # 使用清理后的文本
                            "operation": "submit",
                            "with_timestamp": "1",
                            "extra_param": json.dumps({
                                "disable_markdown_filter": False,
                            }),
                        },
                    }
                    
                    # 确保JSON编码正确
                    request_json = json.dumps(request_data, ensure_ascii=False)
                    request_bytes = request_json.encode('utf-8')
                    
                    logger.debug(f"发送请求: {len(request_bytes)} 字节")
                    
                    # 发送请求
                    await full_client_request(websocket, request_bytes)
                    
                    # 接收音频数据
                    audio_data = bytearray()
                    message_count = 0
                    max_messages = 100  # 防止无限循环
                    
                    while message_count < max_messages:
                        try:
                            msg = await asyncio.wait_for(receive_message(websocket), timeout=15.0)
                            message_count += 1
                            
                            if msg.type == MsgType.FrontEndResultServer:
                                continue
                            elif is_audio_message(msg):
                                audio_chunk = extract_audio_data(msg)
                                audio_data.extend(audio_chunk)
                                if is_final_message(msg):
                                    break
                            elif msg.type == MsgType.ErrorServer:
                                error_msg = msg.payload.decode('utf-8', errors='ignore')
                                raise RuntimeError(f"服务器错误: {error_msg}")
                            else:
                                logger.warning(f"未知消息类型: {msg.type}")
                                
                        except asyncio.TimeoutError:
                            logger.warning(f"接收消息超时 (消息数: {message_count})")
                            if message_count == 0:
                                raise RuntimeError("未收到任何响应消息")
                            break
                    
                    # 检查音频数据
                    if not audio_data:
                        raise RuntimeError("未收到音频数据")
                    
                    logger.info(f"合成成功: {len(audio_data)} 字节")
                    return bytes(audio_data)
                    
                finally:
                    try:
                        await websocket.close()
                    except:
                        pass
                        
            except Exception as e:
                logger.error(f"合成失败 (尝试 {attempt + 1}/{self.config['MAX_RETRIES']}): {e}")
                if attempt < self.config["MAX_RETRIES"] - 1:
                    wait_time = (attempt + 1) * 2
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"所有重试失败")
                    return None
        
        return None

async def test_fixed_synthesis():
    """测试修复版合成功能"""
    print("=" * 60)
    print("豆包TTS修复版测试")
    print("=" * 60)
    
    config = FIXED_CONFIG
    
    # 创建输出目录
    os.makedirs(config["OUTPUT_DIR"], exist_ok=True)
    
    # 测试文本（包含一些可能有问题的文本）
    test_texts = [
        "你好",
        "测试",
        "被使",
        "上是",
        "不我",
        "中国",
        "北京",
        "上海",
        "广州",
        "深圳"
    ]
    
    print(f"测试文本: {test_texts}")
    
    # 创建TTS对象
    tts = FixedDouyinTTS(config)
    
    # 测试音色
    voice_type = "zh_female_wanqudashu_moon_bigtts"
    voice_name = "婉曲大叔"
    
    print(f"测试音色: {voice_name}")
    
    # 确认开始测试
    try:
        confirm = input(f"\n是否开始修复版测试？(y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("测试取消")
            return
    except KeyboardInterrupt:
        print("\n测试被中断")
        return
    
    print(f"\n=== 开始测试 ===")
    start_time = time.time()
    
    success_count = 0
    error_count = 0
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n[{i}/{len(test_texts)}] 测试文本: '{text}'")
        
        # 合成音频
        audio_data = await tts.synthesize_text(text, voice_type)
        
        if audio_data:
            # 保存音频文件
            audio_name = f"test_{i:03d}_{text}"
            audio_path = os.path.join(config["OUTPUT_DIR"], f"{audio_name}.wav")
            
            try:
                with open(audio_path, "wb") as f:
                    f.write(audio_data)
                print(f"  ✅ 成功: {audio_name}.wav ({len(audio_data)} 字节)")
                success_count += 1
            except Exception as e:
                print(f"  ❌ 保存失败: {e}")
                error_count += 1
        else:
            print(f"  ❌ 合成失败")
            error_count += 1
        
        # 请求间隔
        if i < len(test_texts):
            await asyncio.sleep(config["REQUEST_INTERVAL"])
    
    # 最终统计
    elapsed_time = time.time() - start_time
    total = success_count + error_count
    success_rate = (success_count / total * 100) if total > 0 else 0
    
    print(f"\n=== 测试完成 ===")
    print(f"总耗时: {elapsed_time:.1f} 秒")
    print(f"成功: {success_count} 个")
    print(f"失败: {error_count} 个")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 修复版效果良好！")
        print("可以应用到主程序中")
    else:
        print("⚠️ 仍有问题，需要进一步调试")

def main():
    """主函数"""
    asyncio.run(test_fixed_synthesis())

if __name__ == "__main__":
    main()
