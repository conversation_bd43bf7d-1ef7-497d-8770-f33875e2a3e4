# ============================================================================
# 简化TTS音频生成程序 - Jupyter版本
# 直接替换TTS.ipynb中的代码
# 每种音色生成24000字音频（约1小时）
# ============================================================================

# 第一步：安装依赖（如果需要）
# !pip install edge-tts pandas nest-asyncio

# 第二步：导入库和配置
import asyncio
import json
import logging
import uuid
import os
import time
import pandas as pd
from pathlib import Path

# Jupyter异步支持
import nest_asyncio
nest_asyncio.apply()

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print("✅ 环境设置完成")

# ==================== 配置区域 ====================
CONFIG = {
    # 文件配置
    "TEXT_FILE": "shuffled_from_rank_random.txt",  # 文本文件路径
    "OUTPUT_DIR": "audio_output_24k",              # 输出目录
    "CSV_FILE": "tts_24k_log.csv",                 # CSV记录文件
    
    # 生成配置
    "TARGET_CHARS_PER_VOICE": 24000,  # 每种音色目标字数
    "REQUEST_INTERVAL": 0.8,           # 请求间隔（秒）
    
    # TTS引擎选择（推荐使用edge，因为豆包有1007错误）
    "USE_ENGINE": "edge",  # "edge" 或 "douyin"
}

# 音色配置 - 对应您原来的格式
VOICE_CONFIGS = [
    {"code": "001", "name": "通用女声", "edge_voice": "zh-CN-XiaoxiaoNeural"},
    {"code": "002", "name": "通用男声", "edge_voice": "zh-CN-YunxiNeural"},
    {"code": "701", "name": "擎苍", "edge_voice": "zh-CN-XiaoyiNeural"},
    {"code": "119", "name": "通用赘婿", "edge_voice": "zh-CN-YunyangNeural"},
    {"code": "102", "name": "儒雅青年", "edge_voice": "zh-CN-XiaohanNeural"},
    {"code": "113", "name": "甜宠少御", "edge_voice": "zh-CN-XiaomengNeural"},
    {"code": "115", "name": "古风少御", "edge_voice": "zh-CN-XiaomoNeural"},
    {"code": "007", "name": "亲切女声", "edge_voice": "zh-CN-XiaoqiuNeural"},
    {"code": "056", "name": "阳光男声", "edge_voice": "zh-CN-YunjianNeural"},
    {"code": "005", "name": "活泼女声", "edge_voice": "zh-CN-XiaoruiNeural"},
]

print("✅ 配置加载完成")

# 第三步：简化的TTS生成器类
class SimpleTTSGenerator:
    """简化的TTS生成器"""
    
    def __init__(self, config):
        self.config = config
        self.output_dir = Path(config["OUTPUT_DIR"])
        self.output_dir.mkdir(exist_ok=True)
        
        self.success_count = 0
        self.error_count = 0
    
    def load_texts(self):
        """加载文本文件"""
        try:
            with open(self.config["TEXT_FILE"], 'r', encoding='utf-8') as f:
                texts = [line.strip() for line in f if line.strip()]
            print(f"✅ 加载文本: {len(texts)} 条")
            return texts
        except Exception as e:
            print(f"❌ 加载文本失败: {e}")
            return []
    
    def select_texts_for_target_chars(self, texts, target_chars):
        """选择文本达到目标字数"""
        selected_texts = []
        current_chars = 0
        
        for text in texts:
            text_length = len(text)
            if current_chars + text_length <= target_chars:
                selected_texts.append(text)
                current_chars += text_length
            else:
                # 如果接近目标字数，也添加
                if target_chars - current_chars > text_length // 2:
                    selected_texts.append(text)
                    current_chars += text_length
                break
        
        return selected_texts, current_chars
    
    async def synthesize_with_edge_tts(self, text, voice, speed=1.0):
        """使用Edge TTS合成"""
        try:
            import edge_tts
            
            # 语速调整
            rate = f"{(speed - 1) * 100:+.0f}%" if speed != 1.0 else "+0%"
            
            communicate = edge_tts.Communicate(text, voice, rate=rate)
            audio_data = b""
            
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_data += chunk["data"]
            
            return audio_data if audio_data else None
            
        except ImportError:
            print("❌ Edge TTS未安装，请运行: !pip install edge-tts")
            return None
        except Exception as e:
            logger.error(f"Edge TTS合成失败: {e}")
            return None
    
    async def generate_voice_audio(self, voice_config, texts, speed=1.0):
        """为单个音色生成音频"""
        voice_code = voice_config["code"]
        voice_name = voice_config["name"]
        edge_voice = voice_config["edge_voice"]
        
        print(f"\n=== 生成音色: {voice_name} ({voice_code}) ===")
        print(f"Edge音色: {edge_voice}")
        print(f"文本数量: {len(texts)}")
        print(f"语速: {speed}x")
        
        success_count = 0
        error_count = 0
        records = []
        start_time = time.time()
        
        for i, text in enumerate(texts, 1):
            # 文件名格式：音色代码 + 序号（保持原格式）
            filename = f"{voice_code}{i:04d}.wav"
            filepath = self.output_dir / filename
            
            print(f"[{i}/{len(texts)}] {filename}: {text[:30]}{'...' if len(text) > 30 else ''}")
            
            # 检查文件是否已存在
            if filepath.exists():
                print(f"  跳过已存在文件")
                success_count += 1
                continue
            
            # 合成音频
            audio_data = await self.synthesize_with_edge_tts(text, edge_voice, speed)
            
            if audio_data:
                try:
                    # 保存音频文件
                    with open(filepath, "wb") as f:
                        f.write(audio_data)
                    
                    print(f"  ✅ 成功: {len(audio_data)} 字节")
                    success_count += 1
                    self.success_count += 1
                    
                    # 记录到CSV
                    records.append({
                        "音频名": filename.replace('.wav', ''),
                        "类型": "c",
                        "文本": text,
                        "注音": "",
                        "音色": voice_name
                    })
                    
                except Exception as e:
                    print(f"  ❌ 保存失败: {e}")
                    error_count += 1
                    self.error_count += 1
            else:
                print(f"  ❌ 合成失败")
                error_count += 1
                self.error_count += 1
            
            # 请求间隔
            await asyncio.sleep(self.config["REQUEST_INTERVAL"])
            
            # 每10个显示进度
            if i % 10 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(texts) - i) * avg_time
                print(f"  进度: {i}/{len(texts)}, 预计剩余: {remaining/60:.1f}分钟")
        
        # 保存CSV记录
        if records:
            csv_path = self.output_dir / f"tts_log_{voice_code}.csv"
            df = pd.DataFrame(records)
            df.to_csv(csv_path, index=False, encoding='utf-8-sig', sep='\t')
            print(f"✅ CSV保存: {csv_path}")
        
        elapsed_time = time.time() - start_time
        print(f"\n{voice_name} 完成:")
        print(f"  耗时: {elapsed_time/60:.1f}分钟")
        print(f"  成功: {success_count}")
        print(f"  失败: {error_count}")
        print(f"  成功率: {(success_count/(success_count+error_count)*100):.1f}%" if success_count+error_count > 0 else "0%")
        
        return success_count, error_count

print("✅ TTS生成器类定义完成")

# 第四步：主函数
async def main():
    """主函数 - 简化版本"""
    print("=" * 60)
    print("简化TTS音频生成程序 - Edge TTS版本")
    print("=" * 60)
    
    config = CONFIG
    
    print("配置信息:")
    print(f"  文本文件: {config['TEXT_FILE']}")
    print(f"  输出目录: {config['OUTPUT_DIR']}")
    print(f"  目标字数: {config['TARGET_CHARS_PER_VOICE']} 字/音色")
    print(f"  使用引擎: Edge TTS（免费、稳定）")
    print(f"  请求间隔: {config['REQUEST_INTERVAL']} 秒")
    
    print(f"\n音色配置:")
    for i, voice in enumerate(VOICE_CONFIGS, 1):
        print(f"  {i}. {voice['name']} ({voice['code']})")
    
    print("=" * 60)
    
    # 创建生成器
    generator = SimpleTTSGenerator(config)
    
    # 1. 加载文本
    print("\n=== 加载文本 ===")
    all_texts = generator.load_texts()
    if not all_texts:
        print("❌ 没有可用文本")
        return
    
    total_chars = sum(len(text) for text in all_texts)
    print(f"文本总字数: {total_chars} 字")
    
    # 2. 检查文件是否存在
    if not os.path.exists(config['TEXT_FILE']):
        print(f"❌ 文本文件不存在: {config['TEXT_FILE']}")
        print("请确保文本文件在正确位置")
        return
    
    # 3. 开始生成
    print(f"\n=== 开始生成 ===")
    
    total_success = 0
    total_error = 0
    
    for i, voice_config in enumerate(VOICE_CONFIGS, 1):
        print(f"\n进度: {i}/{len(VOICE_CONFIGS)}")
        
        # 为每个音色选择文本
        selected_texts, actual_chars = generator.select_texts_for_target_chars(
            all_texts, config["TARGET_CHARS_PER_VOICE"]
        )
        
        print(f"选择文本: {len(selected_texts)} 条, {actual_chars} 字")
        
        # 生成音频
        success, error = await generator.generate_voice_audio(voice_config, selected_texts, speed=1.0)
        total_success += success
        total_error += error
        
        # 简单的继续逻辑（Jupyter中不需要输入确认）
        print(f"音色 {voice_config['name']} 完成")
    
    # 4. 最终统计
    print(f"\n=== 生成完成 ===")
    print(f"总成功: {total_success}")
    print(f"总失败: {total_error}")
    print(f"成功率: {(total_success/(total_success+total_error)*100):.1f}%" if total_success+total_error > 0 else "0%")
    print(f"输出目录: {config['OUTPUT_DIR']}")
    
    # 生成总的CSV文件
    csv_path = generator.output_dir / config["CSV_FILE"]
    print(f"CSV记录: {csv_path}")

print("✅ 主函数定义完成")

# 第五步：运行程序
print("\n🚀 开始运行TTS生成程序...")
print("使用Edge TTS替代豆包TTS，避免1007错误")

# 在Jupyter中直接运行
await main()

print("\n🎉 程序运行完成！")
print("如果遇到问题，请检查:")
print("1. 文本文件路径是否正确")
print("2. Edge TTS是否已安装: !pip install edge-tts")
print("3. 网络连接是否正常")
