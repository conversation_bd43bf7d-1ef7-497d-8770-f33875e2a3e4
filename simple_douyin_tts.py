#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包TTS简化版生成器
逻辑简单，支持语速调整，每种音色生成前确认
"""

import asyncio
import json
import logging
import uuid
import os
import time
import websockets
from protocols import MsgType, full_client_request, receive_message

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 简化配置
CONFIG = {
    "APPID": "9862368305",
    "ACCESS_TOKEN": "bbxNaAzRzBSkySjouDItXl6tBUUyNd2W",
    "ENDPOINT": "wss://openspeech.bytedance.com/api/v1/tts/ws_binary",
    "TEXT_FILE": "../shuffled_from_rank_random.txt",
    "OUTPUT_DIR": "audio_simple",
    "REQUEST_INTERVAL": 2.0,  # 请求间隔2秒
}

# 简化音色配置（只用几种基础音色测试）
VOICES = [
    {"code": "D001", "type": "BV001_streaming", "name": "通用女声"},
    {"code": "D002", "type": "BV002_streaming", "name": "通用男声"},
    {"code": "D003", "type": "BV007_streaming", "name": "亲切女声"},
    {"code": "D004", "type": "BV056_streaming", "name": "阳光男声"},
    {"code": "D005", "type": "BV005_streaming", "name": "活泼女声"},
]

def get_cluster(voice_type):
    """获取集群名称"""
    if voice_type.startswith("S_"):
        return "volcano_icl"
    return "volcano_tts"

async def synthesize_single_text(text, voice_type, speed=1.0):
    """
    合成单个文本
    speed: 语速倍率，0.5-2.0，1.0为正常速度
    """
    headers = {"Authorization": f"Bearer;{CONFIG['ACCESS_TOKEN']}"}
    
    try:
        # 连接WebSocket
        websocket = await websockets.connect(
            CONFIG["ENDPOINT"], 
            additional_headers=headers, 
            max_size=10 * 1024 * 1024
        )
        
        # 构建请求
        request = {
            "app": {
                "appid": CONFIG["APPID"],
                "token": CONFIG["ACCESS_TOKEN"],
                "cluster": get_cluster(voice_type),
            },
            "user": {"uid": str(uuid.uuid4())},
            "audio": {
                "voice_type": voice_type,
                "encoding": "wav",
                "speed_ratio": speed,  # 语速调整
            },
            "request": {
                "reqid": str(uuid.uuid4()),
                "text": text,
                "operation": "submit",
            },
        }
        
        # 发送请求
        await full_client_request(websocket, json.dumps(request).encode())
        
        # 接收音频数据
        audio_data = bytearray()
        while True:
            msg = await receive_message(websocket)
            
            if msg.type == MsgType.FrontEndResultServer:
                continue
            elif msg.type == MsgType.AudioOnlyServer:
                audio_data.extend(msg.payload)
                if msg.sequence < 0:  # 最后一条消息
                    break
            elif msg.type == MsgType.ErrorServer:
                error_msg = msg.payload.decode('utf-8', errors='ignore')
                raise RuntimeError(f"服务器错误: {error_msg}")
            else:
                logger.warning(f"未知消息类型: {msg.type}")
        
        await websocket.close()
        
        if not audio_data:
            raise RuntimeError("未收到音频数据")
            
        return bytes(audio_data)
        
    except Exception as e:
        logger.error(f"合成失败: {e}")
        return None

def load_texts(max_count=100):
    """加载文本文件，限制数量"""
    try:
        with open(CONFIG["TEXT_FILE"], 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()][:max_count]
        print(f"✓ 加载文本: {len(texts)} 条")
        return texts
    except Exception as e:
        print(f"✗ 加载文本失败: {e}")
        return []

def select_texts_by_length(texts, target_count=50):
    """按长度均匀选择文本"""
    if len(texts) <= target_count:
        return texts
    
    # 按长度分组
    length_groups = {}
    for text in texts:
        length = len(text)
        if length not in length_groups:
            length_groups[length] = []
        length_groups[length].append(text)
    
    # 从每个长度组均匀选择
    selected = []
    lengths = sorted(length_groups.keys())
    per_length = max(1, target_count // len(lengths))
    
    for length in lengths:
        group_texts = length_groups[length][:per_length]
        selected.extend(group_texts)
        if len(selected) >= target_count:
            break
    
    return selected[:target_count]

async def generate_voice_audio(voice_config, texts, speed=1.0):
    """为单个音色生成音频"""
    voice_code = voice_config["code"]
    voice_type = voice_config["type"]
    voice_name = voice_config["name"]
    
    print(f"\n=== 生成音色: {voice_name} ({voice_code}) ===")
    print(f"音色类型: {voice_type}")
    print(f"文本数量: {len(texts)}")
    print(f"语速: {speed}x")
    
    # 确认是否生成
    try:
        confirm = input(f"是否生成 {voice_name}？(y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print(f"跳过 {voice_name}")
            return 0, 0
    except KeyboardInterrupt:
        print("\n生成被中断")
        return 0, 0
    
    # 创建输出目录
    os.makedirs(CONFIG["OUTPUT_DIR"], exist_ok=True)
    
    # 生成音频
    success_count = 0
    error_count = 0
    start_time = time.time()
    
    for i, text in enumerate(texts, 1):
        print(f"[{i}/{len(texts)}] {text[:20]}{'...' if len(text) > 20 else ''}")
        
        # 文件名：Sc + 音色代码 + 序号
        filename = f"Sc{voice_code}{i:07d}.wav"
        filepath = os.path.join(CONFIG["OUTPUT_DIR"], filename)
        
        # 检查文件是否已存在
        if os.path.exists(filepath):
            print(f"  跳过已存在文件")
            success_count += 1
            continue
        
        # 合成音频
        audio_data = await synthesize_single_text(text, voice_type, speed)
        
        if audio_data:
            try:
                with open(filepath, "wb") as f:
                    f.write(audio_data)
                print(f"  ✓ 成功: {len(audio_data)} 字节")
                success_count += 1
            except Exception as e:
                print(f"  ✗ 保存失败: {e}")
                error_count += 1
        else:
            print(f"  ✗ 合成失败")
            error_count += 1
        
        # 请求间隔
        await asyncio.sleep(CONFIG["REQUEST_INTERVAL"])
        
        # 每10个显示进度
        if i % 10 == 0:
            elapsed = time.time() - start_time
            avg_time = elapsed / i
            remaining = (len(texts) - i) * avg_time
            print(f"  进度: {i}/{len(texts)}, 预计剩余: {remaining/60:.1f}分钟")
    
    elapsed_time = time.time() - start_time
    print(f"\n{voice_name} 完成:")
    print(f"  耗时: {elapsed_time/60:.1f}分钟")
    print(f"  成功: {success_count}")
    print(f"  失败: {error_count}")
    
    return success_count, error_count

async def main():
    """主函数"""
    print("=" * 60)
    print("豆包TTS简化版生成器")
    print("=" * 60)
    
    # 1. 加载文本
    print("1. 加载文本文件...")
    all_texts = load_texts(max_count=1000)  # 最多加载1000条
    if not all_texts:
        print("没有可用文本，程序退出")
        return
    
    # 2. 选择文本数量
    try:
        text_count = input(f"每种音色生成多少个文件？(默认50): ").strip()
        text_count = int(text_count) if text_count else 50
    except:
        text_count = 50
    
    selected_texts = select_texts_by_length(all_texts, text_count)
    print(f"选择文本: {len(selected_texts)} 条")
    
    # 3. 选择语速
    try:
        speed_input = input(f"语速倍率 (0.5-2.0，默认1.0): ").strip()
        speed = float(speed_input) if speed_input else 1.0
        speed = max(0.5, min(2.0, speed))  # 限制范围
    except:
        speed = 1.0
    
    print(f"语速设置: {speed}x")
    
    # 4. 显示音色列表
    print(f"\n可用音色:")
    for i, voice in enumerate(VOICES, 1):
        print(f"  {i}. {voice['name']} ({voice['code']})")
    
    # 5. 选择音色
    try:
        voice_choice = input(f"选择音色 (1-{len(VOICES)}，回车全部): ").strip()
        if voice_choice:
            voice_idx = int(voice_choice) - 1
            if 0 <= voice_idx < len(VOICES):
                selected_voices = [VOICES[voice_idx]]
            else:
                print("无效选择，使用全部音色")
                selected_voices = VOICES
        else:
            selected_voices = VOICES
    except:
        selected_voices = VOICES
    
    print(f"选择音色: {[v['name'] for v in selected_voices]}")
    
    # 6. 确认开始
    try:
        confirm = input(f"\n开始生成？(y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("已取消")
            return
    except KeyboardInterrupt:
        print("\n已取消")
        return
    
    # 7. 开始生成
    print(f"\n=== 开始生成 ===")
    total_success = 0
    total_error = 0
    
    for i, voice in enumerate(selected_voices, 1):
        print(f"\n进度: {i}/{len(selected_voices)}")
        
        success, error = await generate_voice_audio(voice, selected_texts, speed)
        total_success += success
        total_error += error
        
        # 询问是否继续
        if i < len(selected_voices):
            try:
                continue_choice = input(f"\n继续生成下一个音色？(y/n): ").strip().lower()
                if continue_choice not in ['y', 'yes', '是']:
                    print("生成被中断")
                    break
            except KeyboardInterrupt:
                print("\n生成被中断")
                break
    
    # 8. 最终统计
    print(f"\n=== 生成完成 ===")
    print(f"总成功: {total_success}")
    print(f"总失败: {total_error}")
    print(f"成功率: {total_success/(total_success+total_error)*100:.1f}%" if total_success+total_error > 0 else "0%")
    print(f"输出目录: {CONFIG['OUTPUT_DIR']}")

if __name__ == "__main__":
    asyncio.run(main())
